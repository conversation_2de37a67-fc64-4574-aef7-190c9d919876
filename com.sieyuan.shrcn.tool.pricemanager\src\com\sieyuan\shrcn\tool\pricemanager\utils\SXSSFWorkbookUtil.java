package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFHyperlink;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 *
 * Apache POI操作Excel对象
 * HSSF：操作Excel 2007之前版本(.xls)格式,生成的EXCEL不经过压缩直接导出
 * XSSF：操作Excel 2007及之后版本(.xlsx)格式,内存占用高于HSSF
 * SXSSF:从POI3.8 beta3开始支持,基于XSSF,低内存占用,专门处理大数据量(建议)。
 *
 * 注意:
 *      值得注意的是SXSSFWorkbook只能写(导出)不能读(导入)
 *
 * 说明:
 *      .xls格式的excel(最大行数65536行,最大列数256列)
 *      .xlsx格式的excel(最大行数1048576行,最大列数16384列)
 */
public class SXSSFWorkbookUtil {

    private static final int border_bottom = 1 << 1;
    private static final int border_left = 1 << 2;
    private static final int border_right = 1 << 4;
    private static final int border_top = 1 << 3;

    public static final int DEFAULT_COLUMN_WIDTH = 17;// 默认列宽
    public static final String DEFAULT_DATE_PATTERN = "yyyy年MM月dd日";// 默认日期格式
    private static Map<String, CellStyle> styleCache = new HashMap<>();
    private static final int border_all = border_bottom | border_left | border_top | border_right;

    /**
     * 单元格的创建格式2
     * @param titleRow
     * @param col
     * @param value 单元格内容
     * @param wb
     * @param borderBottom 是否有下边框
     * @param borderLeft 是否有左边框
     * @param borderTop 是否有上边框
     * @param borderRight 是否有右边框
     * @param right 对齐方式   CellStyle.ALIGN_LEFT左对齐,HorizontalAlignment.CENTER居中,ALIGN_RIGHT右对齐
     */
    public static void createCell(Row row, int col, String value, SXSSFWorkbook wb, boolean borderBottom,
        boolean borderLeft, boolean borderTop, boolean borderRight, HorizontalAlignment right) {
        Cell cell = CellUtil.createCell(row, col, value);
        int border = 0;
        if (borderBottom) {
            border |= border_bottom;
        }
        if (borderLeft) {
            border |= border_left;
        }
        if (borderTop) {
            border |= border_top;
        }
        if (borderRight) {
            border |= border_right;
        }
        
        CellStyle style = getStyle(wb, right, VerticalAlignment.CENTER, border, (short)10, false);
        cell.setCellStyle(style);
    }

    /**
     * 单元格的创建格式1
     * @param titleRow
     * @param col
     * @param value 单元格内容
     * @param wb
     * @param bold 是否是标题
     */
	public static void createCell(Row row, int col, String value, SXSSFWorkbook wb, boolean bold, short fontSize) {
		Cell cell = CellUtil.createCell(row, col, value);
		CellStyle style = getStyle(wb, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, border_all, fontSize, (bold));
		cell.setCellStyle(style);
	}

	/**
	 * 单元格的创建格式1(添加颜色)
	 * @param titleRow
	 * @param col
	 * @param value
	 * @param wb
	 * @param colorIndex 颜色
	 */
	public static void createCell(Row titleRow , int col , String value, SXSSFWorkbook wb, short colorIndex) {
		Cell titleCell = CellUtil.createCell(titleRow, col, value);
		CellStyle style = getStyle(wb, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, border_all, (short)10, false);
		titleCell.setCellStyle(style);
	}

    /**
     * 单元格的创建格式
     * @param titleRow
     * @param col
     * @param value 单元格内容
     * @param wb
     * @param bold 是否是标题
     */
	public static void createCellNumberic(Row row, int col, String value, SXSSFWorkbook wb, boolean bold, short fontSize) {
		Cell cell = CellUtil.createCell(row, col, value);
		CellStyle style = getStyle(wb, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, border_all, fontSize, (bold));
		cell.setCellStyle(style);
		if (CalcUtil.isDouble(value)) {
			double f1 = new BigDecimal(value).doubleValue();
			cell.setCellType(CellType.NUMERIC);
			cell.setCellValue(f1);
		}
	}

	/**
	 * 单元格的创建格式2
	 * 
	 * @param titleRow
	 * @param col
	 * @param value
	 *            单元格内容
	 * @param wb
	 * @param borderBottom
	 *            是否有下边框
	 * @param borderLeft
	 *            是否有左边框
	 * @param borderTop
	 *            是否有上边框
	 * @param borderRight
	 *            是否有右边框
	 * @param alignment
	 *            对齐方式 CellStyle.ALIGN_LEFT左对齐,HorizontalAlignment.CENTER居中,
	 *            ALIGN_RIGHT右对齐
	 */
	public static void createCellNumeric(Row row, int col, String value, SXSSFWorkbook wb, boolean borderBottom, boolean borderLeft, boolean borderTop, boolean borderRight, HorizontalAlignment alignment) {
		Cell cell = CellUtil.createCell(row, col, value);
		int border = 0;
		if (borderBottom) {
			border |= border_bottom;
		}
		if (borderLeft) {
			border |= border_left;
		}
		if (borderTop) {
			border |= border_top;
		}
		if (borderRight) {
			border |= border_right;
		}
		CellStyle style = getStyle(wb, alignment, VerticalAlignment.CENTER, border, (short) 10, false);
		cell.setCellStyle(style);
		if (CalcUtil.isDouble(value)) {
			BigDecimal bg = new BigDecimal(value);
			double f1 = bg.setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
			cell.setCellType(CellType.NUMERIC);
			cell.setCellValue(f1);
		}
	}

    /**
     * 导出Excel(.xlsx)格式
     * @param titleList 表格头信息集合
     * @param dataArray 数据数组
     * @param os 文件输出流
     * @param format 格式对齐
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
	public static void exportExcel(ArrayList<LinkedHashMap> titleList, JSONArray dataArray, OutputStream os, Boolean format, Boolean flag) {
		String datePattern = DEFAULT_DATE_PATTERN;
		int minBytes = DEFAULT_COLUMN_WIDTH;

		/**
		 * 声明一个工作薄
		 */
		SXSSFWorkbook workbook = new SXSSFWorkbook(1000);// 大于1000行时会把之前的行写入硬盘
		workbook.setCompressTempFiles(true);

		// 表头1样式
		CellStyle title1Style = workbook.createCellStyle();
		title1Style.setAlignment(HorizontalAlignment.CENTER);// 水平居中
		title1Style.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直居中
		Font titleFont = workbook.createFont();// 字体
		titleFont.setFontHeightInPoints((short) 20);
		titleFont.setBold(true);
		title1Style.setFont(titleFont);

		// 表头2样式
		CellStyle title2Style = workbook.createCellStyle();
		title2Style.setAlignment(HorizontalAlignment.CENTER);
		title2Style.setVerticalAlignment(VerticalAlignment.CENTER);
		title2Style.setBorderTop(BorderStyle.THIN);// 上边框
		title2Style.setBorderRight(BorderStyle.THIN);// 右
		title2Style.setBorderBottom(BorderStyle.THIN);// 下
		title2Style.setBorderLeft(BorderStyle.THIN);// 左
		Font title2Font = workbook.createFont();
		title2Font.setUnderline((byte) 1);
		title2Font.setColor(IndexedColors.BLUE.index);
		title2Style.setFont(title2Font);

		// head样式
		CellStyle headerStyle = workbook.createCellStyle();
		headerStyle.setAlignment(HorizontalAlignment.CENTER);
		headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headerStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.index);// 设置颜色
		headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);// 前景色纯色填充
		headerStyle.setBorderTop(BorderStyle.THIN);
		headerStyle.setBorderRight(BorderStyle.THIN);
		headerStyle.setBorderBottom(BorderStyle.THIN);
		headerStyle.setBorderLeft(BorderStyle.THIN);
		Font headerFont = workbook.createFont();
		headerFont.setFontHeightInPoints((short) 12);
		headerFont.setBold(true);
		headerStyle.setFont(headerFont);

		CellStyle cellStyle;
		if (format) {
			cellStyle = getStyle(workbook, HorizontalAlignment.CENTER, VerticalAlignment.CENTER, border_all, (short) 10, false);
		} else {
			cellStyle = workbook.createCellStyle();
			cellStyle.setAlignment(HorizontalAlignment.CENTER);
			cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
			cellStyle.setBorderTop(BorderStyle.THIN);
			cellStyle.setBorderRight(BorderStyle.THIN);
			cellStyle.setBorderBottom(BorderStyle.THIN);
			cellStyle.setBorderLeft(BorderStyle.THIN);
		}

		CellStyle cellWarnStyle = workbook.createCellStyle();
		cellWarnStyle.setAlignment(HorizontalAlignment.CENTER);
		cellWarnStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.index);// 设置颜色
		cellWarnStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);// 前景色纯色填充
		cellWarnStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		cellWarnStyle.setBorderTop(BorderStyle.THIN);
		cellWarnStyle.setBorderRight(BorderStyle.THIN);
		cellWarnStyle.setBorderBottom(BorderStyle.THIN);
		cellWarnStyle.setBorderLeft(BorderStyle.THIN);

		String title1 = (String) titleList.get(0).get("title1");
		String title2 = (String) titleList.get(0).get("title2");
		LinkedHashMap<String, String> headMap = titleList.get(1);

		/**
		 * 生成一个(带名称)表格
		 */
		SXSSFSheet sheet = (SXSSFSheet) workbook.createSheet(title1);

		/**
		 * 生成head相关信息+设置每列宽度
		 */
		int[] colWidthArr = new int[headMap.size()];// 列宽数组
		String[] headKeyArr = new String[headMap.size()];// headKey数组
		String[] headValArr = new String[headMap.size()];// headVal数组
		int i = 0;
		for (Map.Entry<String, String> entry : headMap.entrySet()) {
			headKeyArr[i] = entry.getKey();
			headValArr[i] = entry.getValue();

			int bytes = headKeyArr[i].getBytes().length;
			colWidthArr[i] = bytes < minBytes ? minBytes : bytes;
			sheet.setColumnWidth(i, colWidthArr[i] * 256);// 设置列宽
			i++;
		}

		/**
		 * 遍历数据集合，产生Excel行数据
		 */
		int rowIndex = 0;
		for (Object obj : dataArray) {
			// 生成title+head信息
			if (rowIndex == 0) {
				if (flag) {
					sheet.createFreezePane(0, 3, 0, 3);// (单独)冻结前三行
					SXSSFRow title1Row = (SXSSFRow) sheet.createRow(0);// title1行
					title1Row.createCell(0).setCellValue(title1);
					title1Row.getCell(0).setCellStyle(title1Style);
					sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headMap.size() - 1));// 合并单元格

					SXSSFRow title2Row = (SXSSFRow) sheet.createRow(1);// title2行
					title2Row.createCell(0).setCellValue(title2);

					CreationHelper createHelper = workbook.getCreationHelper();
					XSSFHyperlink hyperLink = (XSSFHyperlink) createHelper.createHyperlink(HyperlinkType.URL);
					hyperLink.setAddress(title2);
					title2Row.getCell(0).setHyperlink(hyperLink);// 添加超链接

					title2Row.getCell(0).setCellStyle(title2Style);
					sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, headMap.size() - 1));// 合并单元格

					SXSSFRow headerRow = (SXSSFRow) sheet.createRow(2);// head行
					for (int j = 0; j < headValArr.length; j++) {
						headerRow.createCell(j).setCellValue(headValArr[j]);
						headerRow.getCell(j).setCellStyle(headerStyle);
					}
					rowIndex = 3;
				} else {
					sheet.createFreezePane(0, 1, 0, 1);// (单独)冻结前三行
					SXSSFRow headerRow = (SXSSFRow) sheet.createRow(0);// head行
					for (int j = 0; j < headValArr.length; j++) {
						headerRow.createCell(j).setCellValue(headValArr[j]);
						headerRow.getCell(j).setCellStyle(headerStyle);
					}
					rowIndex = 1;
				}
			}

			JSONObject jo = (JSONObject) JSONObject.toJSON(obj);
			// 生成数据
			SXSSFRow dataRow = (SXSSFRow) sheet.createRow(rowIndex);// 创建行
			for (int m = 0; m < headKeyArr.length; m++) {
				int k = m;
				if (!flag) {
					k = m;
				}
				SXSSFCell cell = (SXSSFCell) dataRow.createCell(k);// 创建单元格
				Object o = jo.get(headKeyArr[m]);
				String cellValue = "";
				if (o == null) {
					cellValue = "";
				} else if (o instanceof Date) {
					cellValue = new SimpleDateFormat(datePattern).format(o);
				} else if (o instanceof Float || o instanceof Double) {
					cellValue = new BigDecimal(o.toString()).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
				} else {
					cellValue = o.toString();
				}

				cell.setCellValue(cellValue);
				cell.setCellStyle(cellStyle);
				if (headKeyArr[m].equals("searchdevtype") && jo.get("ocunnt") != null && !StringUtils.isEmpty(jo.get("ocunnt").toString())) {

					if (isDouble(jo.get("ocunnt").toString())) {
						if (Double.valueOf(jo.get("ocunnt").toString()) > 0) {
							cell.setCellStyle(cellWarnStyle);
						}
					}
				}
				if (headKeyArr[m].equals("isValid")) {
					if (jo.get("isValid") != null && jo.get("isValid").toString().equals("否")) {
						cell.setCellStyle(cellWarnStyle);
					}
				}
			}
			rowIndex++;
		}

		try {
			workbook.write(os);
			os.flush();// 刷新此输出流并强制将所有缓冲的输出字节写出
			os.close();// 关闭流
			workbook.dispose();// 释放workbook所占用的所有windows资源
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	private static CellStyle getStyle(SXSSFWorkbook wb, HorizontalAlignment alignH, VerticalAlignment alignV, int border, short fontSize, boolean bold) {
		String key = wb.hashCode() + "," + alignH + "," + alignV + "," + border + "," + fontSize + "," + bold;
		CellStyle style = styleCache.get(key);
		if (style == null) {
			style = wb.createCellStyle();
			style.setAlignment(alignH);// 左对齐 居中 右对齐
			style.setVerticalAlignment(alignV);// 垂直

			style.setWrapText(true);// 设置自动换行
			if ((border & border_bottom) > 0) {
				style.setBorderBottom(BorderStyle.THIN); // 下边框
			}
			if ((border & border_left) > 0) {
				style.setBorderLeft(BorderStyle.THIN);// 左边框
			}
			if ((border & border_top) > 0) {
				style.setBorderTop(BorderStyle.THIN);// 上边框
			}
			if ((border & border_right) > 0) {
				style.setBorderRight(BorderStyle.THIN);// 右边框
			}
			Font font = wb.createFont();
			font.setFontName("宋体");// 设置字体
			font.setFontHeightInPoints(fontSize);// 设置字体大小
			font.setBold(bold);
			style.setFont(font);
			styleCache.put(key, style);
		}
		return style;
	}

    // 判断浮点数（double和float）
    private static boolean isDouble(String str) {
        if (null == str || "".equals(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-\\+]?[.\\d]*$");
        return pattern.matcher(str).matches();
    }

    public static void reset() {
        styleCache.clear();
    }

}
