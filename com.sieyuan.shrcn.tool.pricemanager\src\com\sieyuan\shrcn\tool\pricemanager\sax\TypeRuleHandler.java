package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;
import com.sieyuan.shrcn.tool.pricemanager.model.TypeRule;

/**
 * 型号填写规则
 * 
 * <AUTHOR>
 * @version 1.0, 2023-5-18
 * 
 */
@SuppressWarnings("rawtypes")
public class TypeRuleHandler extends SheetsHandler {

	private TypeRule typeRule;
	private Map<String, TypeRule> typeRuleMap;

	public TypeRuleHandler() {
		typeRule = new TypeRule();
		typeRuleMap = new HashMap<String, TypeRule>();
	}

	@Override
	public void cell(String cellReference, String formattedValue, XSSFComment comment) {
		super.cell(cellReference, formattedValue, comment);
		if (currentRow > 0 && !isEmpty(formattedValue)) {
			if (currentCol == 0) {
				typeRule.setDesc(formattedValue);
			} else if (currentCol == 1) {
				typeRule.setType(formattedValue);
			} else if (currentCol == 2) {
				typeRule.setManufacturer(formattedValue);
			}
		}
	}

	@Override
	public void endRow(int rowNum) {
		if (typeRule != null && !StringUtils.isEmpty(typeRule.getDesc())) {
			String desc = typeRule.getDesc();
			typeRuleMap.put(desc, typeRule);
		}
	}

	@Override
	public void startRow(int rowNum) {
		super.startRow(rowNum);
		this.typeRule = new TypeRule();
	}

	public Map<String, TypeRule> getTypeRuleMap() {
		return typeRuleMap;
	}

	public void setTypeRuleMap(Map<String, TypeRule> typeRuleMap) {
		this.typeRuleMap = typeRuleMap;
	}

}
