package com.sieyuan.shrcn.tool.pricemanager.action;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.FileDialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PriceRateDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PriceRate;
import com.sieyuan.shrcn.tool.pricemanager.utils.ExcelExportUtil;

/**
 * 按照辅参名称统计
 * 
 * <AUTHOR>
 * @version 1.0, 2020-12-10
 * 
 */
public class AnalysisByPriceRateAction extends MenuAction {

	public AnalysisByPriceRateAction(String title) {
		super(title);
	}

	/**
	 * 导出数据
	 * 
	 * @param path
	 */
	private void export(String path) {
		List<PriceRate> priceRateList = PriceRateDao.getPriceRatesOrderBy();
		ExcelExportUtil.exportPriceRateReport(path, priceRateList, ToolConstants.PRICERATE);
	}

	@Override
	public void run() {
		final String path = FileDialogHelper.selectExcelFile(getShell());
		if (path != null && !"".equals(path)) {
			IRunnableWithProgress openProgress = new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
					monitor.setTaskName("正在导出数据，请稍后......");
					Display.getDefault().asyncExec(new Runnable() {
						@Override
						public void run() {
							export(path);
						}
					});
				}
			};
			ProgressManager.execute(openProgress, false);
			DialogHelper.showAsynInformation("导出辅参报价检查统计结果成功！");
		}
	}
}
