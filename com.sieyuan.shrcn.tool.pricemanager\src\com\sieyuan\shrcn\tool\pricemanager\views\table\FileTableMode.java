package com.sieyuan.shrcn.tool.pricemanager.views.table;

import com.sieyuan.shr.u21.ui.table.TableModel;
import com.sieyuan.shrcn.tool.pricemanager.model.FileItem;

import de.kupzog.ktable.KTableCellEditor;
import de.kupzog.ktable.KTableCellRenderer;
import de.kupzog.ktable.editors.KTableCellEditorCheckbox;

/**
 * @Description:FileTableMode 文件列表
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON><PERSON>uan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午11:09:30
 
 */
public class FileTableMode extends TableModel {

    /**
     * Initialize the base implementation.
     */
    public FileTableMode() {
        super();
    }

    /**
     * 设置表单元格编辑器
     * 
     * @return
     */
    @Override
    public KTableCellEditor doGetCellEditor(int col, int row) {
        if (col == 0 && row > 0) {
            return new KTableCellEditorCheckbox();
        }

        return null;
    }

    /*
     * overridden from superclass
     */
    @Override
    public KTableCellRenderer doGetCellRenderer(int col, int row) {
        KTableCellRenderer temp = null;
        if (col == 0 && row > 0) {
            temp = m_checkableRenderer;
        } else {
            temp = super.doGetCellRenderer(col, row);
        }
        return temp;

    }

    /**
     * 取得表格列个数
     */
    @Override
    public int doGetColumnCount() {
        return getHead().length;
    }

    /*
     * (non-Javadoc)
     * @see com.shrcn.vdd.ui.table.model.TableModel#doGetContentAt(int, int)
     */
    @Override
    public Object doGetContentAt(int col, int row) {
        // 第一行是标题行
        if (row == 0) {
            return getHead()[col];
        }
        FileItem item = (FileItem)items.get(row - 1);
        switch (col) {
            case 0:
                return item.isChecked();
            case 1:
                return row;
            case 2:
                return item.getFileName();
            case 3:
                return item.getFileType();
            case 4:
                return item.getFileSize();
            case 5:
                return item.getStatus();
            default:
                return "";
        }
    }

    /*
     * (non-Javadoc)
     * @see com.shrcn.vdd.ui.table.model.TableModel#doSetContentAt(int, int, java.lang.Object)
     */
    @Override
    public void doSetContentAt(int col, int row, Object value) {
        if (row - 1 >= 0) {
            FileItem item = (FileItem)items.get(row - 1);
            switch (col) {
                case 0:
                    String filename = item.getFileName();
                    filename = filename.substring(0, filename.length() - 3);
                    for (int i = 0; i < items.size(); i++) {
                        FileItem itemtemp = (FileItem)items.get(i);
                        String tempfilename = itemtemp.getFileName();
                        if (tempfilename.length() <= 3)
                            continue;
                        tempfilename = tempfilename.substring(0, tempfilename.length() - 3);
                        if (filename.equals(tempfilename)) {
                            itemtemp.setChecked((Boolean)value);
                        }
                    }
                    // item.setChecked(true);
                    break;
                case 2:
                    item.setFileName((String)value);
                    break;
                case 3:
                    item.setFileType((String)value);
                    break;
                case 4:
                    item.setFileSize((String)value);
                    break;
                case 5:
                    item.setStatus((String)value);
                    break;

            }
        }
    }

    public String[] getHead() {
        return ITableMessage.FILE_HEAD;
    }

    public int[] getHeadWidth() {

        return ITableMessage.FILE_WIDTH;

    }

    /**
     * 得到初始化行宽度
     */
    @Override
    public int getInitialColumnWidth(int col) {
        return (col < getHeadWidth().length) ? getHeadWidth()[col] : super.getInitialColumnWidth(col);
    }
}
