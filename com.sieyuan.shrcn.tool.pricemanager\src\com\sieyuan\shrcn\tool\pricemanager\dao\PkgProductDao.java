package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;

/**
 * @Description:组件配置表Dao
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:58:10
 
 */
public class PkgProductDao {

    public static List<PkgProduct> getPkgProductByDevTypeAndBidNo(String bidNo, String devType) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgProduct> sList = new ArrayList<>();
        StringBuffer sql =
            new StringBuffer("select * from pkgproduct where bidno='" + bidNo + "' and odevtype='" + devType + "'");
        sql.append(" order by orderid");
        try {
            sList = sqliteHelper.executeQuery(sql.toString(), new RowMapper<PkgProduct>() {
                @Override
                public PkgProduct mapRow(ResultSet rs, int index) throws SQLException {
                    PkgProduct pkgProduct = new PkgProduct();
                    pkgProduct.setId(rs.getInt("id"));
                    pkgProduct.setNumber(rs.getString("number"));
                    pkgProduct.setParentid(rs.getInt("parentid"));
                    pkgProduct.setName(rs.getString("name"));
                    pkgProduct.setBidNo(rs.getString("bidno"));
                    pkgProduct.setDevname(rs.getString("devname"));
                    pkgProduct.setDevtype(rs.getString("devtype"));
                    pkgProduct.setUnit(rs.getString("unit"));
                    pkgProduct.setCount(rs.getString("count"));
                    pkgProduct.setOdevtype(rs.getString("odevtype"));
                    pkgProduct.setOcunnt(rs.getString("ocunnt"));
                    pkgProduct.setSupply(rs.getString("supply"));
                    pkgProduct.setArea(rs.getString("area"));
                    pkgProduct.setQuote(rs.getString("quote"));
                    pkgProduct.setIntelligence(rs.getString("intelligence"));
                    pkgProduct.setSearchdevtype(rs.getString("searchdevtype"));
                    pkgProduct.setLnType(rs.getInt("lntype"));
                    pkgProduct.setCostprice(rs.getString("costprice"));
                    pkgProduct.setPrice(rs.getString("price"));
                    pkgProduct.setWeight(rs.getString("weight"));
                    pkgProduct.setRowId(rs.getInt("prowid"));
                    return pkgProduct;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static List<PkgProduct> getPkgProductByParentid(Integer id) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgProduct> sList = new ArrayList<>();
        StringBuffer sql = new StringBuffer("select * from pkgproduct ");
        if (id != null) {
            sql.append("where parentid =  " + id);
        }
        sql.append(" order by orderid");
        try {
            sList = sqliteHelper.executeQuery(sql.toString(), new RowMapper<PkgProduct>() {
                @Override
                public PkgProduct mapRow(ResultSet rs, int index) throws SQLException {
                    PkgProduct pkgProduct = new PkgProduct();
                    pkgProduct.setId(rs.getInt("id"));
                    pkgProduct.setNumber(rs.getString("number"));
                    pkgProduct.setParentid(rs.getInt("parentid"));
                    pkgProduct.setName(rs.getString("name"));
                    pkgProduct.setBidNo(rs.getString("bidno"));
                    pkgProduct.setDevname(rs.getString("devname"));
                    pkgProduct.setDevtype(rs.getString("devtype"));
                    pkgProduct.setUnit(rs.getString("unit"));
                    pkgProduct.setCount(rs.getString("count"));
                    pkgProduct.setOdevtype(rs.getString("odevtype"));
                    pkgProduct.setOcunnt(rs.getString("ocunnt"));
                    pkgProduct.setSupply(rs.getString("supply"));
                    pkgProduct.setArea(rs.getString("area"));
                    pkgProduct.setQuote(rs.getString("quote"));
                    pkgProduct.setIntelligence(rs.getString("intelligence"));
                    pkgProduct.setSearchdevtype(rs.getString("searchdevtype"));
                    pkgProduct.setLnType(rs.getInt("lntype"));
                    pkgProduct.setCostprice(rs.getString("costprice"));
                    pkgProduct.setPrice(rs.getString("price"));
                    pkgProduct.setWeight(rs.getString("weight"));
                    pkgProduct.setRowId(rs.getInt("prowid"));
                    return pkgProduct;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static void savePkgProductlist() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        String sql =
            "INSERT INTO 'pkgproduct' ('orderid','number','parentid', 'name', 'bidno', 'devname', 'devtype', 'unit', "
                + "'count', 'odevtype', 'ocunnt', 'supply',  'area',  'quote', 'intelligence', 'prowid') select b.orderid, b.number, a.id as parentid, b.name,b.bidno, b.devname, "
                + "b.devtype,b.unit,b.count,b.odevtype,b.ocunnt,b.supply,b.area, b.quote, a.intelligence, b.prowid from pkginfo a "
                + "left join product b on a.bidno=b.bidno and a.name=b.name and a.prowid=b.prowid where b.id is not null";
        try {
            sqliteHelper.executeUpdate(sql);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

    public static void updatePkgProductAnalysisPrice(List<PkgProduct> pkgProductList) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgProduct pkgProduct : pkgProductList) {
            Integer id = pkgProduct.getId();
            String price = pkgProduct.getPrice();
            String totalPrice = pkgProduct.getTotalprice();
            String weight = pkgProduct.getWeight();
            String sql =
                "update pkgproduct set totalprice='" + totalPrice + "', weight='" + weight + "', price='" + price
                    + "' where id=" + id;
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

    public static void updatePkgProductPrice(List<PkgProduct> pkgProductList) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgProduct pkgProduct : pkgProductList) {
            String odevtype = pkgProduct.getOdevtype();
            String ocount = pkgProduct.getOcunnt();
            String supply = pkgProduct.getSupply();
            Integer id = pkgProduct.getId();
            String searchdevtype = pkgProduct.getSearchdevtype();
            Integer lntype = pkgProduct.getLnType();
            String costprice = pkgProduct.getCostprice();
            String price = pkgProduct.getPrice();
            String quote = pkgProduct.getQuote();
            String area = pkgProduct.getArea();
            String weight = pkgProduct.getWeight();
            String sql =
                "update pkgproduct set odevtype='" + odevtype + "', ocunnt='" + ocount + "',supply='" + supply
                    + "',quote='" + quote + "',area='" + area + "',searchdevtype='" + searchdevtype + "', lntype=" + lntype
                    + ",costprice='" + costprice + "', price='" + price + "', weight='" + weight + "' where id=" + id;
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 清除价格
     */
	public static void deletePkgProductPrice() {
		SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
		String sql = "update pkgproduct set price ='', totalprice='';";
		try {
			sqliteHelper.executeUpdate(sql);
		} catch (ClassNotFoundException | SQLException e) {
			e.printStackTrace();
		}
	}
	
    /**
     * 删除包信息
     */
	public static void deletePkg(String pkgName) {
		SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
		String sql = "DELETE FROM pkgadjust where pkgname='" + pkgName + "'" ;
		String sql2 = "DELETE FROM pkginfo where name='" + pkgName + "'" ;
		String sql3 = "DELETE FROM pkgproduct where name='" + pkgName + "'" ;
		String sql4 = "DELETE FROM pkgtotal where pkgname='" + pkgName + "'" ;
		sqls.add(sql);
		sqls.add(sql2);
		sqls.add(sql3);
		sqls.add(sql4);
		try {
			sqliteHelper.executeUpdate(sqls);
		} catch (ClassNotFoundException | SQLException e) {
			e.printStackTrace();
		}
	}
}
