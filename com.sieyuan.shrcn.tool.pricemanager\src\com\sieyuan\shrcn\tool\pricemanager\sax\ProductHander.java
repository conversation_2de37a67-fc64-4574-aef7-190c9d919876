/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTDecimalNumber;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;

import com.shrcn.found.common.log.SCTLogger;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.Product;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;

/**
 * @Description:“组件材料配置表”解析类
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-23 上午11:27:22
 
 */
public class ProductHander {

    private static final String endtag = "注";
    private static final String endtag2 = "填写规定";
    private static final String endtag3 = "配置规定";
    private static final String removetag = "屏柜总量";
    private static final String removetag2 = "逻辑表";

    /** 将“组件材料配置表”的标题中“元件名称”作为起始依据 */
    private static final String tag = "元件名称";
    private static final int tag_col = 1;
    private static int tag_row = 2;

    private static boolean isProductTable(int row, int col, String cellText) {
        if ((col == tag_col) && tag.equals(cellText)) {
            tag_row = row;
            return true;
        } else {
            return false;
        }
    }

    private Map<String, CostPrice> priceMap = new HashMap<>();

    public ProductHander(Map<String, CostPrice> priceMap) {
        this.priceMap = priceMap;
    }

    /**
     * 获取当前单元格的colspan（列合并）的列数
     * 
     * @param tcPr 单元格属性
     * @return
     */
    public int getColspan(CTTcPr tcPr) {
        // 判断是否存在列合并
        CTDecimalNumber gridSpan = null;
        if ((gridSpan = tcPr.getGridSpan()) != null) { // 合并的起始列
            // 获取合并的列数
            BigInteger num = gridSpan.getVal();
            return num.intValue();
        } else { // 其他被合并的列或正常列
            return 1;
        }
    }

    public List<Product> parse(String wordPath, List<Product> products, Integer rowId) {
        int orderid = 0;
        FileInputStream in = null;
        Boolean isCttc = false;// 第二列是否为合并的
        try {
            in = new FileInputStream(wordPath);// 载入文档
            // word 2007 图片不会被读取， 表格中的数据会被放在字符串的最后
            XWPFDocument xwpf = new XWPFDocument(in);// 得到word文档的信息
            // List<XWPFParagraph> listParagraphs = xwpf.getParagraphs();//得到段落信息
            Iterator<XWPFTable> it = xwpf.getTablesIterator();// 得到word中的表格
            boolean found = false;
            hword:
            while (it.hasNext() && !found) {
                XWPFTable table = it.next();
                List<XWPFTableRow> rows = table.getRows();
                // 读取每一行数据
                for (int row = 0; row < rows.size(); row++) {
                    XWPFTableRow tbrow = rows.get(row);
                    // 读取每一列数据
                    List<XWPFTableCell> cells = tbrow.getTableCells();
                    Product product = null;
                    for (int col = 0; col < cells.size(); col++) {
                        XWPFTableCell cell = cells.get(col);
                        // 输出当前的单元格的数据
                        String cellText = cell.getText();
                        if (!found) {
                            found = isProductTable(row, col, cellText);
                        }
                        if (found && (row > tag_row)) {
                            if (cellText.startsWith(endtag) || cellText.startsWith(endtag2)
                                || cellText.startsWith(endtag3)) {
                                break hword;
                            } else {
                                if (col == 1) {
                                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                                    int colspan = getColspan(tcPr);
                                    if (colspan > 1) { // 合并的列
                                        isCttc = true;
                                    } else { // 正常列
                                        isCttc = false;
                                    }
                                }
                                if (!isCttc) {
                                    // } else if (product != null && cellText!=null) {
                                    switch (col) {
                                        case 0:
                                            product = new Product(wordPath);
                                            product.setNumber(cellText);
                                            orderid++;
                                            product.setQuote("否");
                                            product.setOrderid(orderid);
                                            product.setBidno(FieldUtils.getFileId(wordPath));
                                            product.setName(FieldUtils.getPkgName(wordPath));
                                            if (!StringUtils.isEmpty(cellText) && cellText.contains("、")) {
                                                String[] array = cellText.split("、");
                                                product.setNumber(array[0]);
                                                if (array.length > 1) {
                                                    product.setDevname(array[1]);
                                                    if (array[1].contains(removetag) || array[1].contains(removetag2)) {
                                                        products.remove(product);
                                                        break hword;
                                                    }
                                                }
                                            }
                                            product.setRowId(rowId);
                                            products.add(product);
                                            break;
                                        case 1:
                                            if (StringUtils.isEmpty(product.getDevname())) {
                                                product.setDevname(cellText);
                                            }
                                            if (cellText.contains(removetag) || cellText.contains(removetag2)) {
                                                products.remove(product);
                                                break hword;
                                            }
                                            break;
                                        case 2:
                                            product.setDevtype(cellText);
                                            break;
                                        case 3:
                                            product.setUnit(cellText);
                                            break;
                                        case 4:
                                            product.setCount(cellText);
                                            break;
                                        case 5:
                                            product.setOdevtype(cellText);
                                            break;
                                        case 6:
                                            product.setOcunnt(cellText);
                                            if (!StringUtils.isEmpty(product.getOcunnt())) {
                                                if (StringUtils.isNumeric(product.getOcunnt())
                                                    && Integer.valueOf(product.getOcunnt()) > 0) {
                                                    product.setQuote("是");
                                                } else {
                                                    product.setQuote("否");
                                                }
                                            } else {
                                                product.setQuote("否");
                                            }
                                            // 如果为变压器保护

                                            // TG-8：每面屏含:
                                            // UDT-531：变压器差动保护2台、按需配置尾纤、盘线架、光缆配线架
                                            //
                                            // TG-8：每面屏含:
                                            // UDT-531：变压器保护2台、
                                            // UDT-531D：非电量保护 1 台、电压切换箱 0台、
                                            // UDC-381：操作箱 2台、
                                            // Epson LQ-520K：打印机 1 台

                                            Boolean isNUmber = StringUtils.isNumeric(cellText);
                                            if (!StringUtils.isEmpty(cellText) && isNUmber
                                                && Integer.valueOf(cellText) > 0) {
                                                // 包含每面屏含需要拆分
                                                if (!StringUtils.isEmpty(product.getOdevtype())
                                                    && product.getOdevtype().contains(ToolConstants.SPITSIGN)) {

                                                    Product pr = new Product(wordPath);
                                                    orderid++;
                                                    pr.setNumber("");
                                                    pr.setOrderid(orderid);
                                                    pr.setDevname("屏柜");
                                                    pr.setOcunnt(String.valueOf(1 * Integer.valueOf(cellText)));
                                                    pr.setCount(String.valueOf(1 * Integer.valueOf(cellText)));
                                                    pr.setUnit("面");
                                                    pr.setOdevtype("TG-8");
                                                    pr.setDevtype("TG-8");
                                                    CostPrice costPrice = priceMap.get("TG-8");
                                                    if (costPrice != null) {
                                                        pr.setSupply(costPrice.getSupply());
                                                        pr.setArea(costPrice.getArea());
                                                    } else {
                                                        pr.setSupply("思源弘瑞");
                                                        pr.setArea("中国");
                                                    }
                                                    pr.setBidno(FieldUtils.getFileId(wordPath));
                                                    pr.setName(FieldUtils.getPkgName(wordPath));

                                                    // 截取每面屏含后面的字符串，替换英文的冒号和顿号
                                                    String odevType =
                                                        product
                                                            .getOdevtype()
                                                            .substring(
                                                                product.getOdevtype().indexOf(ToolConstants.SPITSIGN) + 5,
                                                                product.getOdevtype().length()).replace("、", "&&&")
                                                            .replace(":", "：");

                                                    // 如果有子项，才拆分
                                                    if (odevType.contains("：")) {
                                                        pr.setRowId(rowId);
                                                        product.setQuote("否");
                                                        pr.setQuote("是");
                                                        products.add(pr);
                                                    }

                                                    String[] devArray = odevType.split("&&&");
                                                    for (int j = 0; j < devArray.length; j++) {
                                                        Product prtemp = new Product(wordPath);
                                                        String dev = devArray[j];
                                                        if (dev.contains("：")) {
                                                            String[] devTyps = dev.split("：");
                                                            String name = devTyps[0];
                                                            String value = devTyps[1];
                                                            costPrice = priceMap.get(name);
                                                            // 从value中提取数量和名称
                                                            prtemp.setOdevtype(name);
                                                            prtemp.setDevtype(name);

                                                            // 从value中取出数字作为数量，取出数量后面的文字作为单位
                                                            String cs = FieldUtils.getNumbers(value);
                                                            if (!StringUtils.isEmpty(cs)) {
                                                                orderid++;
                                                                prtemp.setOrderid(orderid);
                                                                String typs = value.replaceAll(cs, "&&&");
                                                                String[] arraytyps = typs.split("&&&");
                                                                String devType = arraytyps[0];
                                                                String unit = "";
                                                                if (arraytyps.length > 1) {
                                                                    // 单位取数字后面一位
                                                                    unit =
                                                                        StringUtils.trimToEmpty(arraytyps[1])
                                                                            .substring(0, 1);
                                                                }
                                                                // 乘以倍数
                                                                Integer count =
                                                                    Integer.valueOf(cs) * Integer.valueOf(cellText);
                                                                prtemp.setDevname(devType);
                                                                prtemp.setCount(String.valueOf(count));
                                                                prtemp.setOcunnt(String.valueOf(count));
                                                                prtemp.setUnit(unit);
                                                                prtemp.setNumber("");
                                                                prtemp.setBidno(FieldUtils.getFileId(wordPath));
                                                                prtemp.setName(FieldUtils.getPkgName(wordPath));
                                                                if (costPrice != null) {
                                                                    prtemp.setSupply(costPrice.getSupply());
                                                                    prtemp.setArea(costPrice.getArea());
                                                                } else {
                                                                    prtemp.setSupply("思源弘瑞");
                                                                    prtemp.setArea("中国");
                                                                }
                                                                prtemp.setRowId(rowId);
                                                                if (!StringUtils.isEmpty(prtemp.getOcunnt())) {
                                                                    if (StringUtils.isNumeric(prtemp.getOcunnt())
                                                                        && Integer.valueOf(prtemp.getOcunnt()) > 0) {
                                                                        prtemp.setQuote("是");
                                                                    } else {
                                                                        prtemp.setQuote("否");
                                                                    }
                                                                } else {
                                                                    prtemp.setQuote("否");
                                                                }
                                                                products.add(prtemp);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            break;
                                        case 7:
                                            product.setSupply(cellText);
                                            break;
                                        case 8:
                                            product.setArea(cellText);
                                            break;
                                        default:
                                            break;
                                    }
                                } else {
                                    // } else if (product != null && cellText!=null) {
                                    switch (col) {
                                        case 0:
                                            product = new Product(wordPath);
                                            product.setNumber(cellText);
                                            orderid++;
                                            product.setQuote("否");
                                            product.setOrderid(orderid);
                                            product.setBidno(FieldUtils.getFileId(wordPath));
                                            product.setName(FieldUtils.getPkgName(wordPath));
                                            if (!StringUtils.isEmpty(cellText) && cellText.contains("、")) {
                                                String[] array = cellText.split("、");
                                                product.setNumber(array[0]);
                                                if (array.length > 1) {
                                                    product.setDevname(array[1]);
                                                    if (array[1].contains(removetag) || array[1].contains(removetag2)) {
                                                        products.remove(product);
                                                        break hword;
                                                    }
                                                }
                                            }
                                            product.setRowId(rowId);
                                            products.add(product);
                                            break;
                                        case 1:
                                            if (StringUtils.isEmpty(product.getDevname())) {
                                                product.setDevname(cellText);
                                            }
                                            if (cellText.contains(removetag) || cellText.contains(removetag2)) {
                                                products.remove(product);
                                                break hword;
                                            }
                                            product.setDevtype("");
                                            break;
                                        case 2:
                                            product.setUnit(cellText);
                                            break;
                                        case 3:
                                            product.setCount(cellText);
                                            break;
                                        case 4:
                                            product.setOdevtype(cellText);
                                            break;
                                        case 5:
                                            product.setOcunnt(cellText);
                                            if (!StringUtils.isEmpty(product.getOcunnt())) {
                                                if (StringUtils.isNumeric(product.getOcunnt())
                                                    && Integer.valueOf(product.getOcunnt()) > 0) {
                                                    product.setQuote("是");
                                                } else {
                                                    product.setQuote("否");
                                                }
                                            } else {
                                                product.setQuote("否");
                                            }
                                            // 如果为变压器保护

                                            // TG-8：每面屏含:
                                            // UDT-531：变压器差动保护2台、按需配置尾纤、盘线架、光缆配线架
                                            //
                                            // TG-8：每面屏含:
                                            // UDT-531：变压器保护2台、
                                            // UDT-531D：非电量保护 1 台、电压切换箱 0台、
                                            // UDC-381：操作箱 2台、
                                            // Epson LQ-520K：打印机 1 台

                                            Boolean isNUmber = StringUtils.isNumeric(cellText);
                                            if (!StringUtils.isEmpty(cellText) && isNUmber
                                                && Integer.valueOf(cellText) > 0) {
                                                // 包含每面屏含需要拆分
                                                if (!StringUtils.isEmpty(product.getOdevtype())
                                                    && product.getOdevtype().contains(ToolConstants.SPITSIGN)) {

                                                    Product pr = new Product(wordPath);
                                                    orderid++;
                                                    pr.setNumber("");
                                                    pr.setOrderid(orderid);
                                                    pr.setDevname("屏柜");
                                                    pr.setOcunnt(String.valueOf(1 * Integer.valueOf(cellText)));
                                                    pr.setCount(String.valueOf(1 * Integer.valueOf(cellText)));
                                                    pr.setUnit("面");
                                                    pr.setOdevtype("TG-8");
                                                    pr.setDevtype("TG-8");
                                                    CostPrice costPrice = priceMap.get("TG-8");
                                                    if (costPrice != null) {
                                                        pr.setSupply(costPrice.getSupply());
                                                        pr.setArea(costPrice.getArea());
                                                    } else {
                                                        pr.setSupply("思源弘瑞");
                                                        pr.setArea("中国");
                                                    }
                                                    pr.setBidno(FieldUtils.getFileId(wordPath));
                                                    pr.setName(FieldUtils.getPkgName(wordPath));

                                                    // 截取每面屏含后面的字符串，替换英文的冒号和顿号
                                                    String odevType =
                                                        product
                                                            .getOdevtype()
                                                            .substring(
                                                                product.getOdevtype().indexOf(ToolConstants.SPITSIGN) + 5,
                                                                product.getOdevtype().length()).replace("、", "&&&")
                                                            .replace(":", "：");

                                                    // 如果有子项，才拆分
                                                    if (odevType.contains("：")) {
                                                        pr.setRowId(rowId);
                                                        product.setQuote("否");
                                                        pr.setQuote("是");
                                                        products.add(pr);
                                                    }

                                                    String[] devArray = odevType.split("&&&");
                                                    for (int j = 0; j < devArray.length; j++) {
                                                        Product prtemp = new Product(wordPath);
                                                        String dev = devArray[j];
                                                        if (dev.contains("：")) {
                                                            String[] devTyps = dev.split("：");
                                                            String name = devTyps[0];
                                                            String value = devTyps[1];
                                                            costPrice = priceMap.get(name);
                                                            // 从value中提取数量和名称
                                                            prtemp.setOdevtype(name);
                                                            prtemp.setDevtype(name);

                                                            // 从value中取出数字作为数量，取出数量后面的文字作为单位
                                                            String cs = FieldUtils.getNumbers(value);
                                                            if (!StringUtils.isEmpty(cs)) {
                                                                orderid++;
                                                                prtemp.setOrderid(orderid);
                                                                String typs = value.replaceAll(cs, "&&&");
                                                                String[] arraytyps = typs.split("&&&");
                                                                String devType = arraytyps[0];
                                                                String unit = "";
                                                                if (arraytyps.length > 1) {
                                                                    // 单位取数字后面一位
                                                                    unit =
                                                                        StringUtils.trimToEmpty(arraytyps[1])
                                                                            .substring(0, 1);
                                                                }
                                                                // 乘以倍数
                                                                Integer count =
                                                                    Integer.valueOf(cs) * Integer.valueOf(cellText);
                                                                prtemp.setDevname(devType);
                                                                prtemp.setCount(String.valueOf(count));
                                                                prtemp.setOcunnt(String.valueOf(count));
                                                                prtemp.setUnit(unit);
                                                                prtemp.setNumber("");
                                                                prtemp.setBidno(FieldUtils.getFileId(wordPath));
                                                                prtemp.setName(FieldUtils.getPkgName(wordPath));
                                                                if (costPrice != null) {
                                                                    prtemp.setSupply(costPrice.getSupply());
                                                                    prtemp.setArea(costPrice.getArea());
                                                                } else {
                                                                    prtemp.setSupply("思源弘瑞");
                                                                    prtemp.setArea("中国");
                                                                }
                                                                prtemp.setRowId(rowId);
                                                                if (!StringUtils.isEmpty(prtemp.getOcunnt())) {
                                                                    if (StringUtils.isNumeric(prtemp.getOcunnt())
                                                                        && Integer.valueOf(prtemp.getOcunnt()) > 0) {
                                                                        prtemp.setQuote("是");
                                                                    } else {
                                                                        prtemp.setQuote("否");
                                                                    }
                                                                } else {
                                                                    prtemp.setQuote("否");
                                                                }
                                                                products.add(prtemp);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            break;
                                        case 6:
                                            product.setSupply(cellText);
                                            break;
                                        case 7:
                                            product.setArea(cellText);
                                            break;
                                        default:
                                            break;
                                    }

                                }
                            }
                        }
                    }
                }

            }
        } catch (IOException e) {
            SCTLogger.error("", e);
        } finally {
            try {
                if (in != null)
                    in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return products;
    }
}
