/**
 * Copyright (c) 2007-2010 上海思弘瑞电力控制技术有限公司. All rights reserved. This program is an eclipse Rich Client Application
 * based on IEC61850 SCT.
 */
package com.sieyuan.shrcn.tool.pricemanager.views.table;

import org.eclipse.swt.widgets.Composite;

import com.shrcn.found.ui.model.TableConfig;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.table.TableBuilder;

/**
 * U21配置工具表格工厂类.
 * 
 * <AUTHOR>
 * @version 1.0, 2014-06-05
 */
public class TableFactory {

	public static final String PKG_ADJUST_TABLE = "pkgAdjust";
	public static final String PKG_TOTAL_BYID_TABLE = "pkgTotalById";
	public static final String PKG_TOTAL_TABLE = "pkgTotal";

	public static final String PKG_ORADER_TABLE = "pkgOrder";
	public static final String ID_PRICE_TABLE = "idPriceTable";
	
	public static final String BID_EVAL_TABLE = "bidEval";
	public static final String BID_EVAL_RESULT_TABLE = "bidEvalResult";
	
	private static UIConfig uicfg = UIConfig.getInstance();

	public static RKTable getPkgAdjustByIdTable(Composite container) {
		TableConfig tableCfg = uicfg.getDefinedTable(PKG_TOTAL_BYID_TABLE);
		RKTable table = TableBuilder.createKTable(PkgTotalByIdTable.class, container, tableCfg);
		table.setFromZero(false);
		return table;
	}
	
	public static RKTable getPkgAdjustTable(Composite container) {
		TableConfig tableCfg = uicfg.getDefinedTable(PKG_ADJUST_TABLE);
		RKTable table = TableBuilder.createKTable(PkgAdjustTable.class, container, tableCfg);
		table.setFromZero(false);
		return table;
	}

	public static RKTable getPkgTotalTable(Composite container) {
		TableConfig tableCfg = uicfg.getDefinedTable(PKG_TOTAL_TABLE);
		RKTable table = TableBuilder.createKTable(PkgTotalTable.class, container, tableCfg);
		table.setFromZero(false);
		return table;
	}
	
	// 开标测算表格
	public static RKTable getBidEvalTable(Composite container) {
		TableConfig tableCfg = uicfg.getDefinedTable(BID_EVAL_TABLE);
		RKTable table = TableBuilder.createKTable(BidEvalTable.class, container, tableCfg);
		table.setFromZero(false);
		return table;
	}
	
	public static RKTable getBidEvalResultTable(Composite container) {
		TableConfig tableCfg = uicfg.getDefinedTable(BID_EVAL_RESULT_TABLE);
		RKTable table = TableBuilder.createKTable(RKTable.class, container, tableCfg);
		table.setFromZero(false);
		return table;
	}

	public static RKTable getPkgOrderTable(Composite container) {
		TableConfig tableCfg = uicfg.getDefinedTable(PKG_ORADER_TABLE);
		RKTable table = TableBuilder.createKTable(RKTable.class, container, tableCfg);
		table.setFromZero(false);
		return table;
	}


	
}
