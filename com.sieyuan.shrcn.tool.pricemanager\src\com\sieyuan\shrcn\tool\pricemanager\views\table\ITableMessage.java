package com.sieyuan.shrcn.tool.pricemanager.views.table;

import com.sieyuan.shrcn.tool.pricemanager.utils.UIPreferencesUtil;

/**
 * 
 * @Description:ITableMessage table属性
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:56:43
 
 */
public interface ITableMessage {

    String[] ANALYSIS_HEAD = {"序号", "包号", "含税成本", "国网限价", "目标价格", "最终报价"};
    int[] ANALYSIS_WIDTH = {60, 80, 100, 100, 100, 100};

    String[] FILE_HEAD = {"", "序号", "文件名称", "文件类型", "文件大小", "处理状态"};
    int[] FILE_WIDTH = {20, 40, 280, 60, 100, 100};

    String[] PACKAGE_DATA_HEAD = {"序号", "包号", "项目单位 ", "项目名称", "物资名称", "物资描述", "单位", "数量", "是否智能", "电压等级", "技术规范ID", "申请号",
        "单个最高限价（万元）", "总限价（万元）"};
    String[] PACKAGE_DATA_ID_HEAD = {"序号", "技术规范ID", "包号", "项目单位", "项目名称", "物资名称", "物资描述", "单位", "数量", "是否智能", "电压等级", "申请号",
        "最高限价（万元）", "总限价（万元）", "含税成本（万元）", "单套报价（万元）", "总报价（万元）", "平均值（万元）", "AVG平均值（万元）", "是否在±" + UIPreferencesUtil.getIdLimit() + "%范围"};

    int[] PACKAGE_DATA_ID_WIDTH = {40, 150, 50, 100, 100, 100, 100, 40, 40, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100};
    int[] PACKAGE_DATA_WIDTH = {40, 50, 100, 100, 100, 100, 40, 40, 100, 100, 100, 150, 100};

    String[] PRICE_INFO_HEAD = {"序号", "价格选型型号", "制造商", "产地", "类型", "成本价格（万元）", "对外报价（万元）"};
    int[] PRICE_INFO_WIDTH = {40, 150, 80, 80, 80, 100, 100};

    String[] PRODUCT_DATA_HEAD = {"序号", "元件名称", "规格型号", "单位", "数量", "规格型式", "数量", "制造商", "产地", "是否报价", "价格选型型号", "类型",
        "成本价格（万元）", "对外报价（万元）", "报价权重"};
    int[] PRODUCT_DATA_WIDTH = {80, 160, 160, 60, 60, 160, 80, 100, 100, 80, 160, 50, 90, 90, 90};
}
