package com.sieyuan.shrcn.tool.pricemanager.action;

import java.lang.reflect.InvocationTargetException;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.file.util.FileManipulate;
import com.shrcn.found.ui.action.MenuAction;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;

/**
 * @Description:导出工程
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:10:39
 
 */
public class ExportProjectAction extends MenuAction {

	private UIPreferences perference = UIPreferences.newInstance();

	public ExportProjectAction(String text) {
		super(text);
		setAccelerator(SWT.CTRL + 'G');
	}

	@Override
	public void run() {

		// 获取当前工程名称，如果为空，提示
		String prj = perference
				.getInfo("com.shrcn.pricemangertool.curentprojectname");
		if (StringUtils.isEmpty(prj)) {
			DialogHelper.showAsynWarning("请先打开工程，再执行保存！");
			return;
		} else {
			final String file = DirManager.getProjectFile(prj);
			final String path = DialogHelper.selectFile(getShell(), 0, "*.db");
			if (path != null && !"".equals(path)) {
				IRunnableWithProgress openProgress = new IRunnableWithProgress() {
					@Override
					public void run(IProgressMonitor monitor)
							throws InvocationTargetException,
							InterruptedException {
						monitor.setTaskName("正在导出工程数据，请稍后......");
						Display.getDefault().asyncExec(new Runnable() {
							@Override
							public void run() {
								FileManipulate.copyByChannel(file, path + ".db");
							}
						});
					}
				};
				ProgressManager.execute(openProgress, false);
				DialogHelper.showAsynInformation("导出工程成功！");
			}
		}
	}
}
