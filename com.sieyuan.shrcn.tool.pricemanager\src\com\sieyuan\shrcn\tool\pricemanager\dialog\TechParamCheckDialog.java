package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Shell;
import org.xml.sax.InputSource;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.u21.table.Table;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.model.FileItem;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.views.table.FileTableMode;
import org.apache.poi.openxml4j.util.ZipSecureFile;

public class TechParamCheckDialog extends WrappedDialog {
	static {
		// 允许更高的压缩比，解决POI的Zip bomb检测问题
		ZipSecureFile.setMinInflateRatio(0.0001);
	}

	private FileTableMode fileTableMode;
	private Table table;
	private static int lastColIndex = -1;

	// 不需要检查的问题名称列表
	private static final String[] IGNORE_PROBLEM_NAMES = {
		"组件材料配置表",
		"技术参数特性表",
		"使用环境条件表",
		"抗振能力",
		"图纸",
		"采集单元参数特性表",
		"采样参数",
		"直流电源电压采样（取UdN=220V或UdN =110V）",
		"测量元件特性准确度",
		"触发录波",
		"连续录波",
		"异常报文",
		"采集接口",
		"连续报文",
		"其它参数",
		"管理单元参数特性表",
		"通信能力",
		"信息传输能力",
		"电磁兼容(必填)",
		"使用环境条件表(必填)",
		"抗振能力(必填)",
		"非电量保护",
		"抗震能力",
		"组件配置规格形式",
		"站控层设备",
		"750kV/500kV/330kV测控柜",
		"110（66）kV\\220kV测控柜",
		"主变压器测控柜",
		"35kV保护测控装置",
		"10（20）kV保护测控一体化装置",
		"110kV及以下电压并列柜",
		"事项",
		"技术参数特性表（若组件配置表中，对应的设备值为0，则该项设备参数特性不作为必须响应值。并在投标过程中，不作为评标打分项。）",
		"监控系统",
		"各工作站的CPU 平均负荷率",
		"网络平均负荷率",
		"*实时数据库容量",
		"*历史数据库储存容量",
		"*事件顺序记录分辨率（ SOE ）",
		"网络交换机",
		"站控层通信端口",
		"II型网络安全监测装置",
		"防火墙",
		"百兆防火墙",
		"#VPN（可选）",
		"温度",
		"湿度",
		"SCD免配置功能",
		"压板功能设",
		"电磁兼容",
		"SCD免配置功能",
		"压板功能"
	};

	public TechParamCheckDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		container.setLayout(new GridLayout(3, false));

		table = new Table(container, SWT.BORDER | SWT.V_SCROLL | SWT.H_SCROLL);
		table.setLayout(new GridLayout(1, false));

		GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 6, 10);
		gd_table.widthHint = 650;
		gd_table.heightHint = 280;
		table.setLayoutData(gd_table);
		fileTableMode = new FileTableMode();
		// 加载本地文件列表
		List<FileItem> lastItems = loadFileListFromLocal();
		if (lastItems != null && !lastItems.isEmpty()) {
			fileTableMode.setItems(lastItems);
		}
		table.setModel(fileTableMode);

		return container;
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == OK) {
			checkFile();
			return;
		} else if (buttonId == IDialogConstants.ABORT_ID) {
			addFiles();
			saveFileListToLocal(fileTableMode.getItems());
			return;
		} else if (buttonId == IDialogConstants.BACK_ID) {
			removeFiles();
			saveFileListToLocal(fileTableMode.getItems());
			return;
		}
		super.buttonPressed(buttonId);
	}

	@Override
	public boolean close() {
		saveFileListToLocal(fileTableMode.getItems());
		return super.close();
	}

	private void addFiles() {
		FileDialog dialog = new FileDialog(this.getShell(), SWT.OPEN | SWT.MULTI);
		dialog.setFilterExtensions(new String[] { "*.xlsx" });
		dialog.open();
		String[] fileNames = dialog.getFileNames();
		String path = dialog.getFilterPath();
		List<FileItem> items = fileTableMode.getItems();
		for (String fileItem : fileNames) {
			FileItem item = new FileItem();
			item.setChecked(true);
			item.setStatus("");
			item.setFileName(fileItem);
			item.setFileType(fileItem.substring(fileItem.lastIndexOf(".") + 1));
			File file = new File(path + File.separator + fileItem);
			item.setAbsFileName(file.getAbsolutePath());
			item.setFileSize(FieldUtils.getFileSize(file));
			items.add(item);
		}
		fileTableMode.setItems(items);
		table.redraw();
	}

	private void removeFiles() {
		List<FileItem> itemsList = new ArrayList<>();
		List<FileItem> items = fileTableMode.getItems();
		for (FileItem fileItem : items) {
			if (!fileItem.isChecked()) {
				itemsList.add(fileItem);
			}
		}
		fileTableMode.setItems(itemsList);
		table.redraw();
	}

	private void checkFile() {
		final List<FileItem> items = fileTableMode.getItems();
		ProgressManager.execute(new IRunnableWithProgress() {
			@Override
			public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
				boolean hasError = false;
				monitor.beginTask("检查技术参数表......", items.size());
				for (final FileItem fileItem : items) {
					fileItem.setStatus("检查中...");
					monitor.setTaskName("检查" + fileItem.getFileName() + "中...");
					Display.getDefault().asyncExec(new Runnable() {
						@Override
						public void run() {
							table.redraw();
						}
					});
					try {
						List<String> errors = checkSingleFile(fileItem.getAbsFileName());
						if (errors.isEmpty()) {
							fileItem.setStatus("检查通过");
						} else {
							fileItem.setStatus("检查失败: " + errors.size() + "个问题");
							ConsoleManager.getInstance().append("检查文件: " + fileItem.getFileName() + " 发现以下问题:");
							for (String error : errors) {
								ConsoleManager.getInstance().append(error);
							}
							hasError = true;
						}
					} catch (Exception e) {
						fileItem.setStatus("检查异常: " + e.getMessage());
						SCTLogger.error("Error checking file: " + fileItem.getAbsFileName(), e);
						hasError = true;
					}
					Display.getDefault().asyncExec(new Runnable() {
						@Override
						public void run() {
							table.redraw();
						}
					});
					monitor.worked(1);
				}
				if (!hasError) {
					DialogHelper.showAsynInformation("所有文件检查通过！");
				} else {
					DialogHelper.showAsynError("部分文件检查失败，请查看状态列！");
				}
				monitor.done();
			}
		}, true);
	}

	private List<String> checkSingleFile(String filePath) throws Exception {
		List<String> issues = new LinkedList<String>();
		OPCPackage pkg = null;
		try {
			pkg = OPCPackage.open(filePath, PackageAccess.READ);
			XSSFReader xssfReader = new XSSFReader(pkg);
			StylesTable styles = xssfReader.getStylesTable();
			ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(pkg);
			XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
			while (iter.hasNext()) {
				InputStream sheet = null;
				String sheetName = null;
				try {
					sheet = iter.next();
					sheetName = iter.getSheetName();
					XMLReader sheetParser = createXMLReader();
					SheetHandler handler = new SheetHandler(styles, strings, issues, sheetName);
					sheetParser.setContentHandler(handler);
					sheetParser.parse(new InputSource(sheet));
				} catch (Exception e) {
					issues.add("Sheet:" + (sheetName == null ? "未知" : sheetName) + " 解析异常: " + e.getMessage());
				} finally {
					if (sheet != null) {
						try {
							sheet.close();
						} catch (Exception e) {
						}
					}
				}
			}
		} finally {
			if (pkg != null) {
				try {
					pkg.close();
				} catch (Exception e) {
				}
			}
		}
		return issues;
	}

	private XMLReader createXMLReader() throws Exception {
		SAXParserFactory saxFactory = SAXParserFactory.newInstance();
		SAXParser saxParser = saxFactory.newSAXParser();
		return saxParser.getXMLReader();
	}

	private static class SheetHandler extends DefaultHandler {
		private StylesTable stylesTable;
		private ReadOnlySharedStringsTable sharedStringsTable;
		private List<String> issues;
		private String cellValue;
		private int currentRow = 0;
		private Map<String, Integer> colIndexMap = new HashMap<>();
		private Map<Integer, String> rowData = new HashMap<>();
		private boolean isHeaderRow = false;
		private boolean isDataRow = false;
		private int headerRowNum = 4; // 标题行在第4行
		private int colCount = 0;
		private String currentColumn = null;
		private String sheetName;
		private int colCursor = 0;
		private int currentCellColIdx = 0;

		public SheetHandler(StylesTable styles, ReadOnlySharedStringsTable strings, List<String> issues, String sheetName) {
			this.stylesTable = styles;
			this.sharedStringsTable = strings;
			this.issues = issues;
			this.sheetName = sheetName;
		}

		@Override
		public void startElement(String uri, String localName, String name, org.xml.sax.Attributes attributes) {
			if ("row".equals(name)) {
				currentRow = Integer.parseInt(attributes.getValue("r"));
				rowData.clear();
				isHeaderRow = (currentRow == headerRowNum);
				isDataRow = (currentRow > headerRowNum);
				colCursor = 0;
			} else if ("c".equals(name)) {
				currentColumn = attributes.getValue("r").replaceAll("[0-9]", "");
				currentCellColIdx = getColIndex(currentColumn);
				// 补齐空单元格
				while (colCursor < currentCellColIdx) {
					rowData.put(colCursor, "");
					colCursor++;
				}
			} else if ("v".equals(name)) {
				cellValue = "";
			}
		}

		@Override
		public void endElement(String uri, String localName, String name) {
			if ("v".equals(name)) {
				if (currentColumn != null) {
					String value = cellValue;
					try {
						int idx = Integer.parseInt(value);
						value = sharedStringsTable.getEntryAt(idx);
					} catch (Exception e) {
						// 非共享字符串
					}
					rowData.put(colCursor, value != null ? value.trim() : "");
					colCursor++;
				}
			} else if ("row".equals(name)) {
				// 行结束时补齐本行所有空单元格（假设最大列数为colCursor）
				// 可根据表头行的列数补齐
				int maxCol = colCursor;
				if (isHeaderRow) {
					maxCol = rowData.size();
				}
				for (int i = colCursor; i < maxCol; i++) {
					if (!rowData.containsKey(i)) {
						rowData.put(i, "");
					}
				}
				if (isHeaderRow) {
					// 记录标题列名和索引
					colIndexMap.clear();
					for (Map.Entry<Integer, String> entry : rowData.entrySet()) {
						colIndexMap.put(entry.getValue(), entry.getKey());
					}
				} else if (isDataRow) {
					checkRow(rowData, currentRow, colIndexMap, issues);
				}
			}
		}

		@Override
		public void characters(char[] ch, int start, int length) {
			if (cellValue != null) {
				cellValue += new String(ch, start, length);
			}
		}

		private int getColIndex(String colName) {
			// A=0, B=1, ...
			int idx = 0;
			for (char c : colName.toCharArray()) {
				idx = idx * 26 + (c - 'A' + 1);
			}
			return idx - 1;
		}

		private void checkRow(Map<Integer, String> data, int rowNum, Map<String, Integer> colIndexMap, List<String> issues) {
			Integer idxProjectValue = getColIndexFuzzy(colIndexMap, "项目单位要求值");
			Integer idxBidderValue = getColIndexFuzzy(colIndexMap, "投标人保证值");
			Integer idxProjectValueFix = getColIndexFuzzy(colIndexMap, "项目单位要求值修正值");
			Integer idxProblemName = getColIndexFuzzy(colIndexMap, "问题名称");
			if (idxProjectValue == null || idxBidderValue == null || idxProjectValueFix == null || idxProblemName == null) {
				issues.add("Sheet:" + sheetName + " 第" + rowNum + "行：列名识别失败（项目单位要求值/投标人保证值/项目单位要求值修正值/问题名称）");
				return;
			}
			String problemName = data.containsKey(idxProblemName) ? data.get(idxProblemName) : "";
			java.util.List<String> ignoreList = java.util.Arrays.asList(IGNORE_PROBLEM_NAMES);
			if (ignoreList.contains(problemName)) {
				return; // 不检查
			}
	
			String projectValue = data.containsKey(idxProjectValue) ? data.get(idxProjectValue) : "";
			String bidderValue = data.containsKey(idxBidderValue) ? data.get(idxBidderValue) : "";
			String projectValueFix = data.containsKey(idxProjectValueFix) ? data.get(idxProjectValueFix) : "";
			
			// 针对"图纸(必填)"特殊处理
			if ("图纸(必填)".equals(problemName)) {
				if (StringUtil.isEmpty(bidderValue)) {
					issues.add("Sheet:" + sheetName + " 第" + rowNum + "行：投标人保证值未填写，请检查！（图纸(必填)）");
				}
				// 项目单位要求值可以为空且允许不一致，不做不一致预警
				return;
			}
			
			// 新增：投标人保证值为空预警
			if (StringUtil.isEmpty(bidderValue) && !("详见招标文件.docx".equals(projectValue))) {
				issues.add("Sheet:" + sheetName + " 第" + rowNum + "行：投标人保证值未填写，请检查！");
			}
			// 规则1（如果项目单位要求值为"详见招标文件.docx"，则不做不一致预警）
			if (!projectValue.equals(bidderValue)
				&& !("详见招标文件.docx".equals(projectValue))) {
				issues.add("Sheet:" + sheetName + " 第" + rowNum + "行：项目单位要求值与投标人保证值不一致。项目单位要求值=[" + projectValue + "]，投标人保证值=[" + bidderValue + "]");
			}
			// 敏感词列表
			String[] sensitiveWords = {"", "投标人填写", "投标人响应", "投标方填写", "投标方响应", "卖方填写", "卖方响应"};
			boolean hasSensitive = StringUtil.isEmpty(projectValue);
			for (String word : sensitiveWords) {
				if (!word.isEmpty() && projectValue.contains(word)) {
					hasSensitive = true;
					break;
				}
			}
			if (hasSensitive) {
				issues.add("Sheet:" + sheetName + " 第" + rowNum + "行：项目单位要求值包含敏感词（空白/投标人填写/投标人响应/投标方填写/投标方响应/卖方填写/卖方响应），实际内容=[" + projectValue + "]");
			}
			// 规则3
			if (projectValueFix != null && !projectValueFix.isEmpty()) {
				issues.add("Sheet:" + sheetName + " 第" + rowNum + "行：项目单位要求值修正值有内容，实际内容=[" + projectValueFix + "]");
			}
		}

		// 新增：模糊匹配列名，忽略前后空格和换行
		private Integer getColIndexFuzzy(Map<String, Integer> colIndexMap, String target) {
			for (Map.Entry<String, Integer> entry : colIndexMap.entrySet()) {
				String key = entry.getKey();
				if (key == null)
					continue;
				String cleanKey = key.trim().replaceAll("\\s+", "");
				String cleanTarget = target.trim().replaceAll("\\s+", "");
				if (cleanTarget.equals(cleanKey)) {
					return entry.getValue();
				}
			}
			return null;
		}
	}

	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("技术参数表检查工具");
	}

	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, "检查", true);
		createButton(parent, IDialogConstants.ABORT_ID, "添加文件", false);
		createButton(parent, IDialogConstants.BACK_ID, "移除文件", false);
		createButton(parent, IDialogConstants.CANCEL_ID, "关闭", false);
	}

	@Override
	protected Point getInitialSize() {
		return new Point(860, 480);
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

	// 文件列表本地持久化方法
	private static final String FILE_LIST_PATH = System.getProperty("user.home") + "/.tech_param_check_files";

	private void saveFileListToLocal(List<FileItem> items) {
		try {
			ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(FILE_LIST_PATH));
			oos.writeObject(new ArrayList<FileItem>(items));
			oos.close();
		} catch (Exception e) {
			// ignore
		}
	}

	@SuppressWarnings("unchecked")
	private List<FileItem> loadFileListFromLocal() {
		try {
			ObjectInputStream ois = new ObjectInputStream(new FileInputStream(FILE_LIST_PATH));
			List<FileItem> items = (List<FileItem>) ois.readObject();
			ois.close();
			return items;
		} catch (Exception e) {
			return null;
		}
	}
}