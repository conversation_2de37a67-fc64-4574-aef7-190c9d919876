/**
 * Copyright (c) 2017-2022 上海思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application
 * designed for 103 debug.
 */
package com.sieyuan.shrcn.tool.pricemanager.views.table;

import org.eclipse.swt.graphics.Color;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.UIConstants;
import com.shrcn.found.ui.enums.EnumCellEditor;
import com.shrcn.found.ui.model.IField;
import com.shrcn.found.ui.model.TableConfig;
import com.shrcn.found.ui.table.RKTableModel;
import com.shrcn.found.ui.table.XOTable;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.views.DetailView;

import de.kupzog.ktable.KTableCellRenderer;
import de.kupzog.ktable.renderers.DefaultCellRenderer;

/**
 * PkgAdjustTableModel
 * 
 * <AUTHOR>
 * @version 1.0, 2020-06-04
 */
public class PkgAdjustTableModel extends RKTableModel {

	public PkgAdjustTableModel(XOTable table, TableConfig config) {
		super(table, config);
	}

	// private FontRenderer fontrenderer = new FontRenderer();

	/**
	 * 设置表单元格描绘
	 * 
	 * @return
	 */
	@Override
	public KTableCellRenderer doGetCellRenderer(int col, int row) {

		DefaultCellRenderer render = null;
		IField field = visibleFields[col];
		if (row < getFixedHeaderRowCount()) {
			return (field.getOrder() > 0) ? m_sortRenderer : m_fixedRenderer;
		} else if (isFixedCell(col, row)) {
			render = m_fixedRenderer;
		} else {
			String editor = field.getEditor();
			String comment = field.getComment();
			String frender = field.getRender();
			if (EnumCellEditor.resovleByType(editor) == EnumCellEditor.CHECK) {
				render = m_checkableRenderer;
			} else if (!StringUtil.isEmpty(comment)) {
				render = "image".equals(frender) ? m_imageRenderComm : m_textRendererComm;
			} else if ("image".equals(frender)) {
				render = m_imageRender;
			} else if ("percent".equals(frender) || "percent".equals(field.getName())) {
				render = m_percentRenderer;
			} else {
				render = m_textRenderer;
			}
		}
		render.setBackground(getRowColor(row));
		
		if (row > 0) {
			PkgAdjust item = (PkgAdjust) items.get(row - 1);
			if (!item.getHasOnlyId()) {
				render.setBackground(UIConstants.YELLOW);
				render.setDefaultBackground(UIConstants.YELLOW);
				return render;
			} 
		}
		
		return render;
	}

	public static Color getRowColor(int row) {
		return row % 2 == 0 ? DetailView.bgcolor1 : DetailView.bgcolor2;
	}

}