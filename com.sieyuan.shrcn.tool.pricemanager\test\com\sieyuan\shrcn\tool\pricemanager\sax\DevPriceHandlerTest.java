/**
 * @Description:TODO
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-10 下午5:05:56
 */
package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.io.InputStream;
import java.util.Map;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.junit.Test;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.file.excel.Xls2007Parser;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;

/**
 * @Description:TODO
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-10 下午5:05:56
 */
public class DevPriceHandlerTest {

    @Test
    public void test() {
        try {
            String path = "d://11111.xlsx";
            OPCPackage xlsxPackage = OPCPackage.open(path, PackageAccess.READ);
            ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
            XSSFReader xssfReader = new XSSFReader(xlsxPackage);
            StylesTable styles = xssfReader.getStylesTable();
            Map<String, CostPrice> priceMap = null;
            XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator)xssfReader.getSheetsData();
            while (iter.hasNext()) {
                InputStream stream = iter.next();
                DevPriceHandler limitHandler = new DevPriceHandler(path, priceMap);
                Xls2007Parser.processSheet(styles, strings, limitHandler, stream);
                limitHandler.getDevPriceList();
                // }
                stream.close();
            }
            xlsxPackage.close();
        } catch (Throwable e) {
            SCTLogger.error(e.getMessage());
        }
    }

}
