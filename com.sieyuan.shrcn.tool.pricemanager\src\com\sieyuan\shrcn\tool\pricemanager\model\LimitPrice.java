package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * @Description:限价信息表
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-27 上午8:25:50
 */
public class LimitPrice {
    private Integer id;//主键
    private Integer count;//数量
    private String limitprice; //价格
    private String product; //物料名称
    private String unit; //单位

    public Integer getCount() {
        return count;
    }

    public Integer getId() {
        return id;
    }

    public String getProduct() {
        return product;
    }

    public String getUnit() {
        return unit;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getLimitprice() {
        return limitprice;
    }

    public void setLimitprice(String limitprice) {
        this.limitprice = limitprice;
    }

}
