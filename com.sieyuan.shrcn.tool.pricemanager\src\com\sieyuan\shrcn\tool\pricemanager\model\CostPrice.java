package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * @Description:价格库
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-17 下午3:56:23
 
 */
public class CostPrice {
    private Integer id;// 主键
    private String area;// 区域
    private String costPrice;// 成本价
    private String devtype;// 元件名称
    private String price;// 对外报价
    private String supply;// 制造商
    private Integer type;// 自产或者外购

    public String getArea() {
        return area;
    }

    public String getCostPrice() {
        return costPrice;
    }

    public String getDevtype() {
        return devtype;
    }

    public Integer getId() {
        return id;
    }

    public String getPrice() {
        return price;
    }

    public String getSupply() {
        return supply;
    }

    public Integer getType() {
        return type;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public void setCostPrice(String costPrice) {
        this.costPrice = costPrice;
    }

    public void setDevtype(String devtype) {
        this.devtype = devtype;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public void setSupply(String supply) {
        this.supply = supply;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}
