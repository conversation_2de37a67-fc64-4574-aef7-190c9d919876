package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.LimitPrice;

/**
 * @Description:限价信息Dao
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:54:39
 */
public class LimitPriceDao {

    public static void saveLimitPricess(List<LimitPrice> limitPrices) {

        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();

        for (LimitPrice limitPrice : limitPrices) {
            String product = limitPrice.getProduct();
            Integer count = limitPrice.getCount();
            String unit = limitPrice.getUnit();
            String price = limitPrice.getLimitprice();

            String sql =
                "INSERT INTO limitprice (product, count, unit, limitprice) " + "VALUES (" + "'" + product + "'" + ", "
                    + count + ", " + "'" + unit + "'" + ", " + "'" + price + "'" + ")";
            sqls.add(sql);
        }
        try {
            String sql1 = "delete from limitprice;";
            String sql2 = "update sqlite_sequence SET seq = 0 where name ='limitprice';";
            sqliteHelper.executeUpdate(sql1);
            sqliteHelper.executeUpdate(sql2);

            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

}
