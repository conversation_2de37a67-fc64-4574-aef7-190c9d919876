package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.SWT;

import com.shrcn.found.ui.action.MenuAction;
import com.shrcn.found.ui.dialog.MessageDialog;

/**
 * @Description:保存工程
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:10:39
 
 */
public class SaveProjectAction extends MenuAction {

    public SaveProjectAction(String text) {
        super(text);
		setAccelerator(SWT.CTRL + 'S');
    }

    @Override
    public void run() {
        MessageDialog.openInformation(this.getShell(), "保存工程", "保存工程成功");
    }
}
