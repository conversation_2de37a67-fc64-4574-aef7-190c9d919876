package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.eclipse.core.runtime.IProgressMonitor;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.file.excel.Xls2007Parser;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgTotalDao;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.exp.PackageExporter;
import com.sieyuan.shrcn.tool.pricemanager.exp.ProductExport;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgTotal;
import com.sieyuan.shrcn.tool.pricemanager.model.TypeRule;
import com.sieyuan.shrcn.tool.pricemanager.sax.PkgPriceHandler;
import com.sieyuan.shrcn.tool.pricemanager.sax.TypeRuleHandler;

/**
 * 导出开标结果
 * 
 * <AUTHOR>
 * 
 */
public class ProductExportUtil {

	private static List<Map<String, Object>> mapList = new ArrayList<>();
	private static String ID = ".id";
	private static String dateTime;

	private static final String PRODUCT_ALL = "开标文件汇总";
	private static TypeRule typeRule;
	private static String applyId;
	private static String desc;

	/**
	 * 导出开标文件数据
	 * 
	 * @param path
	 *            路径
	 * @param selected
	 * @param prsmap
	 * @param pkgmap
	 * @param withId
	 * @param type
	 * @param monitor
	 *            进度信息
	 */
	public static void export(String path, String selected, Boolean withId, Integer type, IProgressMonitor monitor) {

		Map<Integer, List<PkgProduct>> prsmap = new HashMap<>();
		Map<String, List<PkgInfo>> pkgmap = new HashMap<>();

		List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
		List<PkgInfo> pkglsit;
		for (PkgInfo pkgInfo : pkgs) {
			pkglsit = new ArrayList<>();
			if (pkgmap.containsKey(pkgInfo.getName())) {
				pkglsit = pkgmap.get(pkgInfo.getName());
				if (pkglsit == null) {
					pkglsit = new ArrayList<>();
				}
			}
			pkglsit.add(pkgInfo);
			pkgmap.put(pkgInfo.getName(), pkglsit);
		}

		List<PkgTotal> pkgTotals = PkgTotalDao.getPkgTotal();
		Map<String, String> pkgTotalMaps = new HashMap<>();
		for (PkgTotal pkgTotal : pkgTotals) {
			pkgTotalMaps.put(pkgTotal.getPkgname(), pkgTotal.getPrice());
		}

		UIPreferences perference = UIPreferences.newInstance();
		String infopath2 = EnvirmentSettingDialog.class.getName();
		String CURDATE = ".curdate";
		String year = perference.getInfo(infopath2 + CURDATE + "year");
		String month = perference.getInfo(infopath2 + CURDATE + "month");
		String day = perference.getInfo(infopath2 + CURDATE + "day");
		dateTime = year + "年" + (Integer.valueOf(month) + 1) + "月" + day + "日";

		List<PkgProduct> prs = PkgProductDao.getPkgProductByParentid(null);
		List<PkgProduct> prlsit;
		for (PkgProduct pkgProduct : prs) {
			prlsit = new ArrayList<>();
			if (prsmap.containsKey(pkgProduct.getParentid())) {
				prlsit = prsmap.get(pkgProduct.getParentid());
				if (prlsit == null) {
					prlsit = new ArrayList<>();
				}
			}
			prlsit.add(pkgProduct);
			prsmap.put(pkgProduct.getParentid(), prlsit);
		}

		if (selected.equals("全部")) {
			Set<String> pkgnames = pkgmap.keySet(); // 取出所有的key值
			for (String pkgname : pkgnames) {
				List<PkgInfo> list = pkgmap.get(pkgname);
				int count = 0;
				for (PkgInfo pkginfo : list) {
					count++;
					SXSSFWorkbookUtil.reset();
					exportSingle(path, pkginfo, count, pkgmap, prsmap, type, withId);
					monitor.worked(100000 / pkgs.size());
				}
				SXSSFWorkbookUtil.reset();
				String price = pkgTotalMaps.get(pkgname);

				PackageExporter.exportPkgs(path, pkgname, null, pkgmap, price);

			}
		} else {
			List<PkgInfo> allpkgs = PkgInfoDao.getPkgsWithLimitPrice(selected, "", "");
			int count = 0;
			for (PkgInfo pkginfo : allpkgs) {
				count++;
				SXSSFWorkbookUtil.reset();
				exportSingle(path, pkginfo, count, pkgmap, prsmap, type, withId);
				monitor.worked(100000 / allpkgs.size());
			}
			String price = pkgTotalMaps.get(selected);
			PackageExporter.exportPkgs(path, selected, null, pkgmap, price);
		}
	}

	private static void exportSingle(String path, PkgInfo pkgInfo, int count, Map<String, List<PkgInfo>> pkgmap, Map<Integer, List<PkgProduct>> prsmap, Integer type, Boolean withId) {

		mapList = new ArrayList<>();
		List<PkgProduct> prs = prsmap.get(pkgInfo.getId());
		if (prs != null && prs.size() > 0) {
			Double price = 0.00d;
			for (PkgProduct product : prs) {
				try {
					if (product.getPrice() == null || product.getPrice().equals("")) {
						price += 0;
						product.setTotalprice("");
					} else {
						price += (Double.valueOf(product.getOcunnt())) * Double.valueOf(product.getPrice());
						product.setTotalprice(String.format("%.6f", (Double.valueOf(product.getOcunnt())) * Double.valueOf(product.getPrice())));
					}

					mapList.add(FieldUtils.convertToMap(product));
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			String dir = path + File.separator + pkgInfo.getName();
			File dirFolder = new File(dir);
			if (!dirFolder.exists()) {
				dirFolder.mkdirs();
			}
			String filepath = "";
			if (withId) {
				filepath = dir + File.separator + pkgInfo.getApplyId() + ".xlsx";
			} else {
				filepath = dir + File.separator + pkgInfo.getBidNo() + ".xlsx";
			}
			String zbId = UIPreferences.newInstance().getInfo(EnvirmentSettingDialog.class.getName() + ID);
			ProductExport.exportComplex(filepath, pkgInfo, dateTime, zbId, prsmap, type);
		}
	}

	public static void mergeProductFiles(final String output, final List<String> paths, IProgressMonitor monitor) {
		monitor.setTaskName("正在合并数据中，请稍候...");
		monitor.beginTask("正在合并数据中，请稍候...", 10 * (paths.size() + 1));
		unitAllFile(output, paths, monitor);
	}

	private static void unitAllFile(final String output, final List<String> paths, IProgressMonitor monitor) {
		List<String> tempFiles = new ArrayList<>();
		for (String path : paths) {
			String pkgName = new File(path).getName();
			monitor.setTaskName("正在合并" + pkgName + "数据中，请稍候...");
			File[] pathFiles = new File(path).listFiles();
			if (pathFiles == null) {
				continue;
			}
			List<String> fileNames = new ArrayList<String>();
			for (File f : pathFiles) {
				if (f.isFile()) {
					String fileName = f.getName();
					if (fileName.endsWith(ToolConstants.XLSX) && !fileName.contains("包")) {
						fileNames.add(f.getAbsolutePath());
					}
				}
			}
			String destFile = output + File.separator + pkgName + PRODUCT_ALL + ToolConstants.XLSX;
			ExcelMergeUtil.mergeExcelFiles(fileNames, destFile, false);
			tempFiles.add(destFile);
			monitor.worked(10);
		}
		monitor.setTaskName("正在合并汇总数据中，请稍候...");
		String destFile = output + File.separator + PRODUCT_ALL + ToolConstants.XLSX;
		ExcelMergeUtil.mergeExcelFiles(tempFiles, destFile, false);
		monitor.worked(10);
		monitor.done();
	}

	/**
	 * 修改Excel文件
	 * 
	 * @param templateOutput
	 *            输出路径
	 * @param typeMap
	 * @param pkgProductMap
	 * @param pkgMap
	 * @param priceMaps
	 */
	public static void writeExcelPOI(String templateOutput, Map<String, TypeRule> typeMap, Map<String, PkgInfo> pkgMap, Map<String, String> priceMaps) {
		try {
			XSSFWorkbook xwb = new XSSFWorkbook(new FileInputStream(templateOutput));
			XSSFSheet xSheet = xwb.getSheetAt(0); // 获取excel表的第一个sheet
			applyId = "";
			desc = "";
			typeRule = null;
			for (int i = 0; i <= xSheet.getLastRowNum(); i++) { // 遍历所有的行
				if (xSheet.getRow(i) == null) { // 这行为空执行下次循环
					continue;
				}
				// 前两行不处理
				if (i < 1) {
					continue;
				}
				applyId = "";
				desc = "";
				typeRule = null;
				for (int j = 0; j <= xSheet.getRow(i).getPhysicalNumberOfCells(); j++) { // 遍历当前行的所有列
					if (xSheet.getRow(i).getCell(j) == null) {// 为空执行下次循环
						continue;
					}
					XSSFCell xCell = xSheet.getRow(i).getCell(j); // 获取单元格对象，这块不能向上边那两句代码那么写，不能用createXXX，用的话会只把第一列的数据改掉
					XSSFCell headerCell = xSheet.getRow(0).getCell(j);
					String header = headerCell.getStringCellValue();
					
					setExcelCell(templateOutput, typeMap, priceMaps, xCell, header);
				}
			}

			FileOutputStream out = new FileOutputStream(templateOutput);
			xwb.write(out);
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static void setExcelCell(String templateOutput, Map<String, TypeRule> typeMap, Map<String, String> priceMaps, XSSFCell xCell, String header) {
		if (header.contains("物料名称")) { // 读取物料名称
			desc = xCell.getStringCellValue();
		} else if (header.contains("网省采购申请行号")) {
			applyId = xCell.getStringCellValue();
		} else if (header.contains("规格型号")) {
			typeRule = typeMap.get(desc);
			if (typeRule == null) {
				ConsoleManager.getInstance().append(templateOutput + "_" + applyId + "_" + desc + "：在型号品牌填写规则文件中未定义！");
			} else {
				xCell.setCellValue(typeRule.getType());
			}
		} else if (header.contains("制造商")) {
			if (typeRule != null) {
				xCell.setCellValue(typeRule.getManufacturer());
			}
		} else if (header.contains("未含税单价")) {
			if (!StringUtils.isEmpty(priceMaps.get(applyId))) {
				xCell.setCellValue(Double.parseDouble((String) priceMaps.get(applyId)));
			}
		} else if (header.contains("税率")) {
			if (!StringUtils.isEmpty(applyId)) {
				xCell.setCellValue(Double.parseDouble(String.valueOf(UIPreferencesUtil.getTaxRate())));
			}
		}
	}

	/**
	 * 修改Excel文件
	 * 
	 * @param templateOutput
	 *            输出路径
	 * @param priceMaps
	 */
	public static void writeExcelPOI(String templateOutput, Map<String, String> priceMaps) {
		try {
			XSSFWorkbook xwb = new XSSFWorkbook(new FileInputStream(templateOutput));
			XSSFSheet xSheet = xwb.getSheetAt(0); // 获取excel表的第一个sheet
			applyId = "";
			for (int i = 0; i <= xSheet.getLastRowNum(); i++) { // 遍历所有的行
				if (xSheet.getRow(i) == null) { // 这行为空执行下次循环
					continue;
				}
				// 前两行不处理
				if (i < 1) {
					continue;
				}
				applyId = "";
				for (int j = 0; j <= xSheet.getRow(i).getPhysicalNumberOfCells(); j++) { // 遍历当前行的所有列
					if (xSheet.getRow(i).getCell(j) == null) {// 为空执行下次循环
						continue;
					}
					XSSFCell xCell = xSheet.getRow(i).getCell(j); // 获取单元格对象，这块不能向上边那两句代码那么写，不能用createXXX，用的话会只把第一列的数据改掉
					XSSFCell headerCell = xSheet.getRow(0).getCell(j);
					String header = headerCell.getStringCellValue();
					
					setCellValue(priceMaps, xCell, header);
				}
			}

			FileOutputStream out = new FileOutputStream(templateOutput);
			xwb.write(out);
			out.close();

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static void setCellValue(Map<String, String> priceMaps, XSSFCell xCell, String header) {
		if (header.contains("网省采购申请行号")) {
			applyId = xCell.getStringCellValue();
		} else if (header.contains("未含税单价")) {
			if (!StringUtils.isEmpty(priceMaps.get(applyId))) {
				xCell.setCellValue(Double.parseDouble((String) priceMaps.get(applyId)));
			}
		} else if (header.contains("税率")) {
			if (!StringUtils.isEmpty(applyId)) {
				xCell.setCellValue(Double.parseDouble(String.valueOf(UIPreferencesUtil.getTaxRate())));
			}
		}
	}

	// 读取excel中的型号配置
	public static Map<String, TypeRule> parseTypeRule(String typeRulePath) {
		Map<String, TypeRule> map = new HashMap<>();
		try {
			OPCPackage xlsxPackage = OPCPackage.open(typeRulePath, PackageAccess.READ);
			ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
			XSSFReader xssfReader = new XSSFReader(xlsxPackage);
			StylesTable styles = xssfReader.getStylesTable();
			XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
			while (iter.hasNext()) {
				InputStream stream = iter.next();
				TypeRuleHandler hander = new TypeRuleHandler();

				Xls2007Parser.processSheet(styles, strings, hander, stream);
				map = hander.getTypeRuleMap();
				// }
				stream.close();
			}
			xlsxPackage.close();
		} catch (Throwable e) {
			SCTLogger.error(e.getMessage());
		}
		return map;
	}

	/**
	 * 解析物料清单
	 * 
	 * @param pkgs
	 * 
	 * @param filePath
	 *            货物清单路径
	 */
	public static void parsePkg(String pkgpath, List<PkgInfo> pkgs) {
		try {
			OPCPackage xlsxPackage = OPCPackage.open(pkgpath, PackageAccess.READ);
			ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
			XSSFReader xssfReader = new XSSFReader(xlsxPackage);
			StylesTable styles = xssfReader.getStylesTable();
			XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
			while (iter.hasNext()) {
				InputStream stream = iter.next();
				PkgPriceHandler pkgPriceHandler = new PkgPriceHandler();
				Xls2007Parser.processSheet(styles, strings, pkgPriceHandler, stream);
				pkgs.addAll(pkgPriceHandler.getPkgs());
				stream.close();
			}
			xlsxPackage.close();
		} catch (Throwable e) {
			SCTLogger.error(e.getMessage());
		}
	}
}
