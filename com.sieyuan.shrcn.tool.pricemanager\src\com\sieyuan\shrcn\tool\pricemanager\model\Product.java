/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * @Description:组件配置表
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-27 上午8:29:53
 */
public class Product {
    private Integer id;
    private String area = "";
    private String bidno = "";
    private String count = "";
    private String devname = "";
    private String devtype = "";
    private String name = "";
    private String number = "";
    private String ocunnt = "";
    private String odevtype = "";
    private String supply = "";
    private String unit = "";
    private Integer orderid;
    private Integer rowId;
    private String file;

    private String quote;// 是否报价 1=是，2=否
    
    public Product(String file){
    	this.file = file;
    }

    public String getArea() {
        return area;
    }

    public String getBidno() {
        return bidno;
    }

    public String getCount() {
        return count;
    }

    public String getDevname() {
        return devname;
    }

    public String getDevtype() {
        return devtype;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getNumber() {
        return number;
    }

    public String getOcunnt() {
        return ocunnt;
    }

    public String getOdevtype() {
        return odevtype;
    }

    public Integer getOrderid() {
        return orderid;
    }

    public String getSupply() {
        return supply;
    }

    public String getUnit() {
        return unit;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public void setBidno(String bidno) {
        this.bidno = bidno;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public void setDevname(String devname) {
        this.devname = devname;
    }

    public void setDevtype(String devtype) {
        this.devtype = devtype;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public void setOcunnt(String ocunnt) {
        this.ocunnt = ocunnt;
    }

    public void setOdevtype(String odevtype) {
        this.odevtype = odevtype;
    }

    public void setOrderid(Integer orderid) {
        this.orderid = orderid;
    }

    public void setSupply(String supply) {
        this.supply = supply;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getRowId() {
        return rowId;
    }

    public void setRowId(Integer rowId) {
        this.rowId = rowId;
    }

    public String getQuote() {
        return quote;
    }

    public void setQuote(String quote) {
        this.quote = quote;
    }

	public String getFile() {
		return file;
	}

	public void setFile(String file) {
		this.file = file;
	}

	@Override
	public String toString() {
		return "Product [id=" + id + ", area=" + area + ", bidno=" + bidno
				+ ", count=" + count + ", devname=" + devname + ", devtype="
				+ devtype + ", name=" + name + ", number=" + number
				+ ", ocunnt=" + ocunnt + ", odevtype=" + odevtype + ", supply="
				+ supply + ", unit=" + unit + ", orderid=" + orderid
				+ ", rowId=" + rowId + ", quote=" + quote + ", getArea()="
				+ getArea() + ", getBidno()=" + getBidno() + ", getCount()="
				+ getCount() + ", getDevname()=" + getDevname()
				+ ", getDevtype()=" + getDevtype() + ", getId()=" + getId()
				+ ", getName()=" + getName() + ", getNumber()=" + getNumber()
				+ ", getOcunnt()=" + getOcunnt() + ", getOdevtype()="
				+ getOdevtype() + ", getOrderid()=" + getOrderid()
				+ ", getSupply()=" + getSupply() + ", getUnit()=" + getUnit()
				+ ", getRowId()=" + getRowId() + ", getQuote()=" + getQuote()
				+ ", getClass()=" + getClass() + ", hashCode()=" + hashCode()
				+ ", toString()=" + super.toString() + "]";
	}

}
