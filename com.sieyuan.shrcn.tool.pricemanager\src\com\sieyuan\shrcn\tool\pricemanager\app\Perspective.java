/*
 * ${file_name} * Copyright 2010-2013 ABC Inc. All rights reserved.
 */
package com.sieyuan.shrcn.tool.pricemanager.app;

import org.eclipse.ui.IFolderLayout;
import org.eclipse.ui.IPageLayout;
import org.eclipse.ui.IPerspectiveFactory;

import com.shrcn.found.ui.UIConstants;
import com.sieyuan.shrcn.tool.pricemanager.views.DetailViewPart;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatViewPart;

/**
 * @Description:Perspective视图
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Si<PERSON>uan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:47:51
 */
public class Perspective implements IPerspectiveFactory {

    /**
     * The ID of the perspective as specified in the extension.
     */
    public static final String ID = "com.sieyuan.shrcn.tool.pricemanager.app.perspective";

    public void createInitialLayout(IPageLayout layout) {
        String editorArea = layout.getEditorArea();
        layout.setEditorAreaVisible(false);

        // 左侧
        IFolderLayout folderLeft = layout.createFolder("fuctionlist", IPageLayout.LEFT, 0.25f, editorArea);
        folderLeft.addView(NavigatViewPart.ID);
        layout.getViewLayout(NavigatViewPart.ID).setCloseable(false);

        // 右侧
        IFolderLayout folderRight = layout.createFolder("fuctiondetail", IPageLayout.RIGHT, 0.75f, editorArea);
        folderRight.addView(DetailViewPart.ID);
        layout.getViewLayout(DetailViewPart.ID).setCloseable(false);

        // 底部
        IFolderLayout folderRightBottom =
            layout.createFolder("folderrightbottom", IPageLayout.BOTTOM, 0.8f, DetailViewPart.ID);
        folderRightBottom.addView(UIConstants.VIEW_CONSOLE_ID);

    }
}
