package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.excelutils2007.ExcelException;
import net.sf.excelutils2007.ExcelUtils;

import com.shrcn.found.common.util.StringUtil;

/**
 * @Description:Excel导出工具类
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:01:41
 */
public class TemplateExcelExporter {

    /**输出信息 */
    public static final String OUT_TEMPATE = "out_tempate.xlsx";
    /**PKG信息 */
    public static final String PKG_TEMPATE = "pkg_tempate.xlsx";
    /**Product信息 */
    public static final String PRODUCT_TEMPATE = "product_tempate.xlsx";
    
    /**辅参数*/
    public static final String PRICERATE_TEMPATE = "pricerate_template.xlsx";

    /**
     * 导出Excel表
     * @param templateFile BOM表模板文件
     * @param outputFile 输出文件
     * @param params 参数
     * @throws ExcelException 
     * @throws IOException 
     */
    private static void exportExcel(String templateFile, String outputFile, Map<String, Object> params) {

        InputStream is = null;
        FileOutputStream fos = null;
        try {
            // 添加参数
            for (String key : params.keySet()) {
                ExcelUtils.addValue(key, params.get(key));
            }

            // 创建IO
            is = TemplateExcelExporter.class.getResourceAsStream(templateFile);
            fos = new FileOutputStream(outputFile);

            ExcelUtils.export(is, ExcelUtils.getContext(), fos);
            // ConsoleManager.getInstance().append("文件【" + outputFile + "】导出成功！");
        } catch (FileNotFoundException e) {
            // ConsoleManager.getInstance().append("文件【" + outputFile + "】导出失败！");
        } catch (ExcelException e) {
            // ConsoleManager.getInstance().append("文件【" + outputFile + "】导出失败！");
        } finally {
            try {
                if (fos != null)
                    fos.close();
                if (is != null)
                    is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void exportTable(String filePath, List<Map<String, Object>> mapList, String modelName) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("time", StringUtil.getCurrentTime("YYYY-MM-dd HH:mm:ss"));
        params.put("mapList", mapList);
        exportExcel(modelName, filePath, params);
    }

    public static void exportTable(String filePath, Map<String, Object> params, String modelName) {
        exportExcel(modelName, filePath, params);
    }
}
