package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.dialog.LogSettingDialog;

/**
 * @Description:日志设置界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:08:07
 */
public class LogSettingAction extends MenuAction {

    public LogSettingAction(String text) {
        super(text);
		setAccelerator(SWT.CTRL + 'L');
    }

    @Override
    public void run() {
        LogSettingDialog logSettingDialog = new LogSettingDialog(Display.getDefault().getActiveShell());
        logSettingDialog.open();
    }
}
