package com.sieyuan.shrcn.tool.pricemanager.views;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.part.ViewPart;

import com.shrcn.found.common.event.Context;
import com.shrcn.found.common.event.IEventHandler;

/**
 * @Description: NavigatViewPart 导航树ViewPart
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午11:05:04
 */
public class NavigatViewPart extends ViewPart implements IEventHandler {
    public static final String ID = "com.sieyuan.shrcn.tool.pricemanager.app.navigatview";
    private NavigatView listView;

    /**
     * This is a callback that will allow us to create the viewer and initialize
     * it.
     */
    public void createPartControl(Composite parent) {
        listView = new NavigatView(parent);
        // EventManager.getDefault().registEventHandler(this);
    }

    @Override
    public void dispose() {
        // EventManager.getDefault().removeEventHandler(this);
        if (listView != null)
            // listView.dispose();
            super.dispose();
    }

    @Override
    public void execute(Context context) {
        @SuppressWarnings("unused")
        String event = context.getEventName();
        // if (EventConstants.CONNECT.equals(event)) {
        // listView.connect();
        // } else if (EventConstants.DELETEDEVICE.equals(event)) {
        // listView.deleteDevice();
        // } else if (EventConstants.DISCONNECT.equals(event)) {
        // listView.disconnect();
        // } else if (EventConstants.RENAMEDEVICE.equals(event)) {
        // listView.renameDevice();
        // } else if (EventConstants.NEWDEVICE.equals(event)) {
        // listView.newDevice();
        // } else if (EventConstants.RECONNECT.equals(event)) {
        // listView.reConnect();
        // }else if (EventConstants.CONFIGMODIFY.equals(event)) {
        // listView.configModify();
        // }else if (EventConstants.SETTINGRECOVER.equals(event)) {
        // listView.settingRecover();
        // }else if (EventConstants.SETDEVICEID.equals(event)) {
        // listView.setDeviceId();
        // }else if (EventConstants.GETVERID.equals(event)) {
        // listView.getVerId();
        // }
    }

    /**
     * Passing the focus request to the viewer's control.
     */
    public void setFocus() {}
}
