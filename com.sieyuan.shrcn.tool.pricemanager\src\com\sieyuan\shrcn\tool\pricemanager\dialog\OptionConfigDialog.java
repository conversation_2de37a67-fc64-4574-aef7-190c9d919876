/**
 * Copyright (c) 2007, 2008 上海思弘瑞电力控制技术有限公司.
 * All rights reserved. This program is an eclipse Rich Client Application
 * based Visual Device Develop System.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.dialog.MessageDialog;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.utils.UIPreferencesUtil;


/**
 * 选项配置
 * <AUTHOR>
 * @version 1.0, 20-20-12-4
 *
 */
public class OptionConfigDialog extends WrappedDialog {

    private String SINGLE = ".single";
    private String OFFSET = ".offset";
    
    private Button singleCheck;
    private Button offsetCheck;
    
    private UIPreferences perference = UIPreferences.newInstance();

    public OptionConfigDialog(Shell parentShell) {
        super(parentShell);
    }

    @Override
    protected void buttonPressed(int buttonId) {
        if (buttonId == OK) {
            String infopath = getClass().getName();
            boolean singleValue = singleCheck.getSelection();
            perference.setInfo(infopath + SINGLE, String.valueOf(singleValue));
            boolean offsetValue = offsetCheck.getSelection();
            perference.setInfo(infopath + OFFSET, String.valueOf(offsetValue));
            MessageDialog.openInformation(this.getShell(), "选项设置", "选项设置成功");
        }
        super.buttonPressed(buttonId);
    }

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("选项设置");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "确定", true);
        createButton(parent, IDialogConstants.CANCEL_ID, "关闭", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        Composite container = (Composite)super.createDialogArea(parent);
        container.setLayout(new GridLayout(1, false));
        singleCheck =  SwtUtil.createCheckBox(container, "单套分析表导出", new GridData());
        offsetCheck =  SwtUtil.createCheckBox(container, "物资描述" + UIPreferencesUtil.getIdLimit() + "%偏差", new GridData());
        init();
        return container;
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(600, 200);
    }

    private void init() {
        String infopath = getClass().getName();
        String singleValue = perference.getInfo(infopath + SINGLE);
        if (!StringUtils.isEmpty(singleValue)) {
        	singleCheck.setSelection(Boolean.valueOf(singleValue));
        }
        String offsetValue = perference.getInfo(infopath + OFFSET);
        if (!StringUtils.isEmpty(offsetValue)) {
        	offsetCheck.setSelection(Boolean.valueOf(offsetValue));
        }
    }

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}
}

