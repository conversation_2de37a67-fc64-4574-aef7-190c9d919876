package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.ToolBar;
import org.eclipse.swt.widgets.ToolItem;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.ui.app.WrappedTitleAreaDialog;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.ToolFactory;
import com.shrcn.found.ui.util.ToolHandler;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.BidEvaluationModel;
import com.sieyuan.shrcn.tool.pricemanager.model.BidEvaluationResult;
import com.sieyuan.shrcn.tool.pricemanager.model.BidPkgModel;
import com.sieyuan.shrcn.tool.pricemanager.utils.BidEvalUtil;
import com.sieyuan.shrcn.tool.pricemanager.views.table.TableFactory;

/**
 * 开标测算
 * 
 * <AUTHOR>
 * 
 */
public class BidEvalDialog extends WrappedTitleAreaDialog implements ToolHandler {
	
	private static final String N_TEXT = "nText";
	private static final String M_TEXT = "mText";
	private static final String A_TEXT = "aText";
	private static final String BUSINESS_TEXT = "businessText";
	private static final String PRICE_TEXT = "priceText";
	private static final String TECH_TEXT = "techText";

	private RKTable rkTb;

	private Text techText;
	private Text priceText;
	private Text businessText;
	private Text aText;
	private Text mText;
	private Text nText;

	private UIPreferences pref = UIPreferences.newInstance();
	private ToolFactory toolFactory = ToolFactory.getInstance();
	private double tech;
	private double price;
	private double aValue;
	private double mValue;
	private double nValue;
	private Shell shell;

	public BidEvalDialog(Shell parentShell) {
		super(parentShell);
		this.shell = getShell();
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		setTitle("设置相关参数和各厂家报价数据");
		setMessage("设置相关参数和各厂家报价数据，测算出各厂家价格得分。");
		Composite container = new Composite(parent, SWT.FILL);
		container.setLayout(new GridLayout(1, false));
		container.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite topComp = SwtUtil.createComposite(container, new GridData(GridData.FILL_HORIZONTAL), 14);

		ToolBar toolBar = new ToolBar(topComp, SWT.NONE);
		toolBar.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, false, 2, 1));

		techText = SwtUtil.createLabelText(topComp, "技术(%):");
		priceText = SwtUtil.createLabelText(topComp, "价格(%):");
		businessText = SwtUtil.createLabelText(topComp, "商务(%):");
		aText = SwtUtil.createLabelText(topComp, "a(%):");
		mText = SwtUtil.createLabelText(topComp, "m:");
		nText = SwtUtil.createLabelText(topComp, "n:");

		createTool(toolBar);

		Composite tbottomComp = SwtUtil.createComposite(container, new GridData(GridData.FILL_BOTH), 1);

		this.rkTb = TableFactory.getBidEvalTable(tbottomComp);
		rkTb.getTable().setLayoutData(new GridData(GridData.FILL_BOTH));

		initData();

		ModifyListener listener = new ModifyListener() {

			@Override
			public void modifyText(ModifyEvent e) {
				saveTextValuesToPreferences();
			}
		};
		techText.addModifyListener(listener);
		priceText.addModifyListener(listener);
		businessText.addModifyListener(listener);
		aText.addModifyListener(listener);
		mText.addModifyListener(listener);
		nText.addModifyListener(listener);

		return container;
	}

	private void saveTextValuesToPreferences() {
		pref.setInfo(this.getClass().getName() + TECH_TEXT, techText.getText().trim());
		pref.setInfo(this.getClass().getName() + PRICE_TEXT, priceText.getText().trim());
		pref.setInfo(this.getClass().getName() + BUSINESS_TEXT, businessText.getText().trim());
		pref.setInfo(this.getClass().getName() + A_TEXT, aText.getText().trim());
		pref.setInfo(this.getClass().getName() + M_TEXT, mText.getText().trim());
		pref.setInfo(this.getClass().getName() + N_TEXT, nText.getText().trim());
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == IDialogConstants.OK_ID) {
			validateAndCalculate();
			saveTableDataToFile();
			return;
		}
		super.buttonPressed(buttonId);
	}

	@SuppressWarnings("unchecked")
	private List<BidEvaluationModel> loadTableDataFromFile() {
		try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(DirManager.getProjectBidEvalFile()))) {
			return (List<BidEvaluationModel>) ois.readObject();
		} catch (IOException | ClassNotFoundException e) {
			DialogHelper.showAsynError("加载数据时发生错误: " + e.getMessage());
			return new ArrayList<>();
		}
	}

	private void saveTableDataToFile() {
		@SuppressWarnings("unchecked")
		List<BidEvaluationModel> data = (List<BidEvaluationModel>) rkTb.getInput(); // 假设RKTable有一个getData方法返回数据列表
		try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(DirManager.getProjectBidEvalFile()))) {
			oos.writeObject(data);
		} catch (IOException e) {
			DialogHelper.showAsynError("保存数据时发生错误: " + e.getMessage());
		}
	}

	private void validateAndCalculate() {
		// 获取输入值
		String techStr = techText.getText().trim();
		String priceStr = priceText.getText().trim();
		String businessStr = businessText.getText().trim();
		String aStr = aText.getText().trim();
		String mStr = mText.getText().trim();
		String nStr = nText.getText().trim();

		// 校验非空
		if (techStr.isEmpty() || priceStr.isEmpty() || businessStr.isEmpty() || aStr.isEmpty() || mStr.isEmpty() || nStr.isEmpty()) {
			DialogHelper.showAsynError("技术(%)、价格(%)、商务(%)、a、m、n 所有字段都必须填写");
			return;
		}
		double business;
		try {
			tech = Double.parseDouble(techStr);
			price = Double.parseDouble(priceStr);
			business = Double.parseDouble(businessStr);

			aValue = Double.parseDouble(aStr);
			mValue = Double.parseDouble(mStr);
			nValue = Double.parseDouble(nStr);
		} catch (NumberFormatException e) {
			DialogHelper.showAsynError("技术(%)、价格(%)、商务(%)、a、m、n 必须是数字");
			return;
		}

		// 校验总和是否为100
		if (tech + price + business != 100) {
			DialogHelper.showAsynError("技术(%)、价格(%)和商务(%)的总和必须为100");
			return;
		}

		// 继续处理其他逻辑...

		ProgressManager.execute(new IRunnableWithProgress() {
			@Override
			public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
				calValue(monitor);
			}
		});

	}

	@SuppressWarnings("unchecked")
	private void calValue(IProgressMonitor monitor) {

		List<BidEvaluationModel> tableData = (List<BidEvaluationModel>) rkTb.getInput();
		monitor.beginTask("测算投标评估结果", tableData.size());

		final Map<String, BidEvaluationResult[]> ranks = new HashMap<>();
		final List<BidPkgModel> pkgs = new ArrayList<>();
		for (BidEvaluationModel bidModel : tableData) {

			monitor.setTaskName("测算包" + bidModel.getPackageName() + "投标评估结果");

			BidEvaluationResult[] bids = new BidEvaluationResult[] { new BidEvaluationResult(Double.parseDouble(bidModel.getBeijingSF()), "北京四方继保工程技术有限公司"), new BidEvaluationResult(Double.parseDouble(bidModel.getChangyuanSR()), "长园深瑞继保自动化有限公司"),
					new BidEvaluationResult(Double.parseDouble(bidModel.getGuodianNZ()), "国电南京自动化股份有限公司"), new BidEvaluationResult(Double.parseDouble(bidModel.getGuodianNR()), "国电南瑞南京控制系统有限公司"), new BidEvaluationResult(Double.parseDouble(bidModel.getNanruiJB()), "南京南瑞继保工程技术有限公司"),
					new BidEvaluationResult(Double.parseDouble(bidModel.getXuji()), "许继电气股份有限公司"), new BidEvaluationResult(Double.parseDouble(bidModel.getSiyuanHR()), "上海思源弘瑞自动化有限公司") };

			double techWeight = BidEvalUtil.parseAndScale(tech); // 技术分占比
			double priceWeight = BidEvalUtil.parseAndScale(price); // 价格分在总分中的占比

			double[] prices = BidEvalUtil.extractPrices(bids);
			// 计算最终报价A
			double finalBid = BidEvalUtil.calculateFinalBid(prices);
			finalBid = Math.round(finalBid * 100.0) / 100.0; // 保留两位小数

			// 计算基准价B
			double basePrice = finalBid * (1 - aValue);
			basePrice = Math.round(basePrice * 100.0) / 100.0; // 保留两位小数

			// 计算每个报价的得分
			for (BidEvaluationResult bid : bids) {
				bid.setScore(BidEvalUtil.calculateScore(bid.getBid(), basePrice, nValue, mValue));
			}

			// 获取价格得分排名、价格分差、总分差和技术补分
			BidEvaluationResult[] rankings = BidEvalUtil.getRankings(bids, priceWeight, techWeight);
			// 输出结果
			SCTLogger.debug("最终报价A: " + String.format("%.2f", finalBid));
			SCTLogger.debug("基准价B: " + String.format("%.2f", basePrice));
			SCTLogger.debug("价格得分排名及价格分差、总分差、技术补分：");

			ranks.put(bidModel.getPackageName(), rankings);

			pkgs.add(new BidPkgModel(bidModel.getPackageName(), finalBid, basePrice));

			// 1. 定义表头和数据
			List<Object[]> data = new ArrayList<>();

			int index = 0;
			for (BidEvaluationResult ranking : rankings) {
				index++;
				data.add(new Object[] { index, ranking.getCompanyName(), ranking.getBid(), ranking.getScore(), ranking.getRank(), ranking.getPriceDiff(), ranking.getTotalDiff(), ranking.getTechBonus() });
				SCTLogger.debug("厂家 " + ranking.getCompanyName() + " 报价 " + String.format("%.2f", ranking.getBid()) + " 的得分: " + String.format("%.2f", ranking.getScore()) + " 排名: " + ranking.getRank() + " 价格分差: " + String.format("%.2f", ranking.getPriceDiff()) + " 总分差: "
						+ String.format("%.2f", ranking.getTotalDiff()) + " 技术补分: " + String.format("%.2f", ranking.getTechBonus()));
			}
			monitor.worked(1);
		}
		monitor.done();
		Display.getDefault().asyncExec(new Runnable() {
			@Override
			public void run() {
				new BidEvalResultDialog(shell, ranks, pkgs).open();
			}
		});

	}

	private void createTool(ToolBar toolBar) {
		final ToolItem addRow = toolFactory.createTool(toolBar, "NEW", this);
		addRow.setToolTipText("添加");
		final ToolItem delRow = toolFactory.createTool(toolBar, "DEL", this);
		delRow.setToolTipText("删除");

		SelectionAdapter listener = new SelectionAdapter() {
			@Override
			public void widgetSelected(SelectionEvent e) {
				ToolItem toolItem = (ToolItem) e.getSource();
				if (toolItem == addRow) {// 增加
					rkTb.insertRow(new BidEvaluationModel(String.valueOf(rkTb.getItemCount() + 1)));
				} else if (toolItem == delRow) {
					rkTb.handleDelete();
				}
			}
		};
		addRow.addSelectionListener(listener);
		delRow.addSelectionListener(listener);
	}

	private void loadTextValuesFromPreferences() {
		techText.setText(pref.getInfo(this.getClass().getName() + TECH_TEXT));
		priceText.setText(pref.getInfo(this.getClass().getName() + PRICE_TEXT));
		businessText.setText(pref.getInfo(this.getClass().getName() + BUSINESS_TEXT));
		aText.setText(pref.getInfo(this.getClass().getName() + A_TEXT));
		mText.setText(pref.getInfo(this.getClass().getName() + M_TEXT));
		nText.setText(pref.getInfo(this.getClass().getName() + N_TEXT));
	}

	private void initData() {
		// 从UIPreferences加载数据并设置到文本框中
		loadTextValuesFromPreferences();

		String bidEval = DirManager.getProjectBidEvalFile();
		if (new File(bidEval).exists()) {
			List<BidEvaluationModel> data = loadTableDataFromFile();
			if (data != null) {
				rkTb.setInput(data); // 假设RKTable有一个setData方法设置数据列表
			}
		}

	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {

		createButton(parent, IDialogConstants.OK_ID, "测算", true);
		createButton(parent, IDialogConstants.CANCEL_ID, IDialogConstants.CANCEL_LABEL, false);
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("价格测算工具");
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(800, 550);
	}

	@Override
	public void handle(String arg0) {
		SCTLogger.debug(arg0);
	}

}
