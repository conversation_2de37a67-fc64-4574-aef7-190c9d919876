/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.util.List;
import java.util.regex.Pattern;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.UIConstants;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.dialog.MessageDialog;
import com.shrcn.found.ui.util.SwtUtil;
import com.sieyuan.shr.u21.ui.table.Table;
import com.sieyuan.shrcn.tool.pricemanager.dao.CostPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.views.table.PriceInfoTableMode;

import de.kupzog.ktable.KTableCellDoubleClickListener;
import de.kupzog.ktable.KTableCellSelectionListener;

/**
 * @Description:数据文件导入
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 
 */
public class PriceSettingDialog extends WrappedDialog {

    private CostPrice costPrice = null;
    private Boolean open = false;
    private PriceInfoTableMode priceInfoTableMode;
    private Shell shell;

    private Table table;

    public PriceSettingDialog(Shell parentShell) {
        super(parentShell);
        this.shell = parentShell;
    }

    @Override
    protected void buttonPressed(int buttonId) {
        if (buttonId == OK) {
            //
        } else {
            costPrice = null;
        }
        open = false;
        super.buttonPressed(buttonId);
    }

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("价格选型型号设置");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "确定", true);
        createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        open = true;
        Composite container = (Composite)super.createDialogArea(parent);
        container.setLayout(new GridLayout(2, false));
        final Text devtypeText = SwtUtil.createLabelText(container, "价格选型型号:");
        GridData gd_text = new GridData(SWT.LEFT, SWT.LEFT, true, false, 1, 3);
        gd_text.heightHint = 15;
        gd_text.widthHint = 400;
        devtypeText.setLayoutData(gd_text);
        devtypeText.addModifyListener(new ModifyListener() {
            @Override
            public void modifyText(ModifyEvent e) {
                String searchdevType = devtypeText.getText();
                if (!StringUtil.isEmpty(searchdevType)) {
                    List<CostPrice> costPriceList = CostPriceDao.getCostPricesByDevType(searchdevType);
                    priceInfoTableMode.setItems(costPriceList);
                } else {
                    List<CostPrice> costPriceList = CostPriceDao.getCostPrices();
                    priceInfoTableMode.setItems(costPriceList);
                }
                table.redraw();
            }
        });

        table = new Table(container, UIConstants.KTABLE_CELL_STYLE);
        table.setLayout(new GridLayout(2, true));
        GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 2, 1);
        gd_table.widthHint = 479;
        table.setLayoutData(gd_table);
        priceInfoTableMode = new PriceInfoTableMode();
        table.setModel(priceInfoTableMode);
        List<CostPrice> costPriceList = CostPriceDao.getCostPrices();
        priceInfoTableMode.setItems(costPriceList);

        table.addCellSelectionListener(new KTableCellSelectionListener() {
            @Override
            public void cellSelected(int paramInt1, int paramInt2, int paramInt3) {
                CostPrice temp = (CostPrice)priceInfoTableMode.getItems().get(paramInt2 - 1);
                if (!isDouble(temp.getCostPrice()) || !isDouble(temp.getPrice())) {
                    MessageDialog.openWarning(shell, "设置价格选型型号", "成本价格非法！");
                    return;
                }
                costPrice = (CostPrice)priceInfoTableMode.getItems().get(paramInt2 - 1);
            }

            @Override
            public void fixedCellSelected(int paramInt1, int paramInt2, int paramInt3) {}
        });
        table.addCellDoubleClickListener(new KTableCellDoubleClickListener() {
            @Override
            public void cellDoubleClicked(int paramInt1, int paramInt2, int paramInt3) {
                CostPrice temp = (CostPrice)priceInfoTableMode.getItems().get(paramInt2 - 1);
                if (!isDouble(temp.getCostPrice()) || !isDouble(temp.getPrice())) {
                    MessageDialog.openWarning(shell, "设置价格选型型号", "成本价格非法！");
                    return;
                }
                costPrice = (CostPrice)priceInfoTableMode.getItems().get(paramInt2 - 1);
                open = false;
                close();
            }

            @Override
            public void fixedCellDoubleClicked(int paramInt1, int paramInt2, int paramInt3) {}
        });

        return container;
    }

    public CostPrice getCostPrice() {
        return costPrice;
    }
    
    /*  
     * 判断是否为浮点数，包括double和float  
     * @param str 传入的字符串  
     * @return 是浮点数返回true,否则返回false  
    */
    public static boolean isDouble(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[.\\d]*$");
        return pattern.matcher(str).matches();
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(700, 480);
    }

    public Boolean getOpen() {
        return open;
    }

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
