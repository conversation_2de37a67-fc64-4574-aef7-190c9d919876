package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * @Description:包数据信息
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-23 上午11:04:56
 
 */
public class PkgInfo {
    private String bidNo; // 技术规范ID
    private String count; // 数量
    private Integer id; // 主鍵
    private String intelligence; // 是否智能
    private String name; // 包号
    private String product; // 物资名称
    private String productDesc; // 物资描述
    private String projectName; // 项目名称
    private String projectOwner; // 项目单位
    private String limitPrice; // 单个限价
    private String unit; // 单位
    private String voltageGrade; // 电压等级
    private String applyId; // 申请号
    
    private Integer rowId;// 序号
    private String totalLimitPrice; // 总体限价

    private String totalTax; // 含税成本
    private String targetPrice; // 设定目标
    
    private String orgPrice;  // 原始价
    private String realPrice;  // 市场价

    private String totalPrice; // 对外报价
    private String price; // 单个报价

    private String withoutTotalPrice; // 对外报价
    private String withoutTaxPrice; // 单个报价

    //按照id总和求平均
    private String avgRate = ""; // 比例系数
    private String isValid = ""; // 是否超限10%
    private String avg = "";
    //先单个包求平均
    private String pisValid = ""; // 是否超限10%
    private String pavg = "";
    private String valid = "";
    
    //按照名称总和求平均
    private String wavgRate = ""; // 比例系数
    private String wisValid = ""; // 是否超限10%
    private String wavg = "";
    //先单个包求平均
    private String wpisValid = ""; // 是否超限10%
    private String wpavg = "";
    private String wvalid = "";
    
    
    private String isSameId; // 是否为相同ID
    private String pricerate = ""; // 辅参报价基准值

    public String getBidNo() {
        return bidNo;
    }

    public void setBidNo(String bidNo) {
        this.bidNo = bidNo;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIntelligence() {
        return intelligence;
    }

    public void setIntelligence(String intelligence) {
        this.intelligence = intelligence;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getProductDesc() {
        return productDesc;
    }

    public void setProductDesc(String productDesc) {
        this.productDesc = productDesc;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectOwner() {
        return projectOwner;
    }

    public void setProjectOwner(String projectOwner) {
        this.projectOwner = projectOwner;
    }

    public String getLimitPrice() {
        return limitPrice;
    }

    public void setLimitPrice(String limitPrice) {
        this.limitPrice = limitPrice;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getVoltageGrade() {
        return voltageGrade;
    }

    public void setVoltageGrade(String voltageGrade) {
        this.voltageGrade = voltageGrade;
    }

    public Integer getRowId() {
        return rowId;
    }

    public void setRowId(Integer rowId) {
        this.rowId = rowId;
    }

    public String getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(String totalTax) {
        this.totalTax = totalTax;
    }

    public String getTotalLimitPrice() {
        return totalLimitPrice;
    }

    public void setTotalLimitPrice(String totalLimitPrice) {
        this.totalLimitPrice = totalLimitPrice;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getTargetPrice() {
        return targetPrice;
    }

    public void setTargetPrice(String targetPrice) {
        this.targetPrice = targetPrice;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getWithoutTotalPrice() {
        return withoutTotalPrice;
    }

    public void setWithoutTotalPrice(String withoutTotalPrice) {
        this.withoutTotalPrice = withoutTotalPrice;
    }

    public String getWithoutTaxPrice() {
        return withoutTaxPrice;
    }

    public void setWithoutTaxPrice(String withoutTaxPrice) {
        this.withoutTaxPrice = withoutTaxPrice;
    }

    public String getAvgRate() {
        return avgRate;
    }

    public void setAvgRate(String avgRate) {
        this.avgRate = avgRate;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public String getAvg() {
        return avg;
    }

    public void setAvg(String avg) {
        this.avg = avg;
    }

	public String getPisValid() {
		return pisValid;
	}

	public void setPisValid(String pisValid) {
		this.pisValid = pisValid;
	}

	public String getPavg() {
		return pavg;
	}

	public void setPavg(String pavg) {
		this.pavg = pavg;
	}

	public String getValid() {
		return valid;
	}

	public void setValid(String valid) {
		this.valid = valid;
	}

	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}

	public String getIsSameId() {
		return isSameId;
	}

	public void setIsSameId(String isSameId) {
		this.isSameId = isSameId;
	}

	public String getWavgRate() {
		return wavgRate;
	}

	public void setWavgRate(String wavgRate) {
		this.wavgRate = wavgRate;
	}

	public String getWisValid() {
		return wisValid;
	}

	public void setWisValid(String wisValid) {
		this.wisValid = wisValid;
	}

	public String getWavg() {
		return wavg;
	}

	public void setWavg(String wavg) {
		this.wavg = wavg;
	}

	public String getWpisValid() {
		return wpisValid;
	}

	public void setWpisValid(String wpisValid) {
		this.wpisValid = wpisValid;
	}

	public String getWpavg() {
		return wpavg;
	}

	public void setWpavg(String wpavg) {
		this.wpavg = wpavg;
	}

	public String getWvalid() {
		return wvalid;
	}

	public void setWvalid(String wvalid) {
		this.wvalid = wvalid;
	}

	public String getPricerate() {
		return pricerate;
	}

	public void setPricerate(String pricerate) {
		this.pricerate = pricerate;
	}

	public String getRealPrice() {
		return realPrice;
	}

	public void setRealPrice(String realPrice) {
		this.realPrice = realPrice;
	}

	public String getOrgPrice() {
		return orgPrice;
	}

	public void setOrgPrice(String orgPrice) {
		this.orgPrice = orgPrice;
	}

}
