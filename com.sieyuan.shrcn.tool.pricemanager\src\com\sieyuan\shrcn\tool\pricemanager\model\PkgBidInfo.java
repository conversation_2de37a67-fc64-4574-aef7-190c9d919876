package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * @Description:技术规范ID
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-27 上午8:27:16
 */
public class PkgBidInfo {

    private String bidNo;
    private String pkgName;
    private String product;

    private String limitprice;
    private String count;
    private String price;
    private String totalprice;
    
    // 总和的最大值、最小值
    private String max;
    private String min;
    private String avg;
    
    // 单价的最大值、最小值
    private String pmax;
    private String pmin;
    private String pavg;
    
    // 按照id
    private String isValid;
    private String pisValid;
    private String valid;
    
    // 按照名称
    private String wisValid;
    private String wpisValid;
    private String wvalid;
    
    // 参考价
    private String orgPrice;
    
    private String isSameId;
    
    // 参考价 / 总价
    private String realRate;

    public String getBidNo() {
        return bidNo;
    }

    public void setBidNo(String bidNo) {
        this.bidNo = bidNo;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }

    public String getLimitprice() {
        return limitprice;
    }

    public void setLimitprice(String limitprice) {
        this.limitprice = limitprice;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getTotalprice() {
        return totalprice;
    }

    public void setTotalprice(String totalprice) {
        this.totalprice = totalprice;
    }

    public String getAvg() {
        return avg;
    }

    public void setAvg(String avg) {
        this.avg = avg;
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

	public String getMax() {
		return max;
	}

	public void setMax(String max) {
		this.max = max;
	}

	public String getMin() {
		return min;
	}

	public void setMin(String min) {
		this.min = min;
	}

	public String getPmax() {
		return pmax;
	}

	public void setPmax(String pmax) {
		this.pmax = pmax;
	}

	public String getPmin() {
		return pmin;
	}

	public void setPmin(String pmin) {
		this.pmin = pmin;
	}

	public String getPisValid() {
		return pisValid;
	}

	public void setPisValid(String pisValid) {
		this.pisValid = pisValid;
	}

	public String getValid() {
		return valid;
	}

	public void setValid(String valid) {
		this.valid = valid;
	}

	public String getPavg() {
		return pavg;
	}

	public void setPavg(String pavg) {
		this.pavg = pavg;
	}

	public String getIsSameId() {
		return isSameId;
	}

	public void setIsSameId(String isSameId) {
		this.isSameId = isSameId;
	}

	public String getWisValid() {
		return wisValid;
	}

	public void setWisValid(String wisValid) {
		this.wisValid = wisValid;
	}

	public String getWpisValid() {
		return wpisValid;
	}

	public void setWpisValid(String wpisValid) {
		this.wpisValid = wpisValid;
	}

	public String getWvalid() {
		return wvalid;
	}

	public void setWvalid(String wvalid) {
		this.wvalid = wvalid;
	}

	public String getProduct() {
		return product;
	}

	public void setProduct(String product) {
		this.product = product;
	}

	public String getOrgPrice() {
		return orgPrice;
	}

	public void setOrgPrice(String orgPrice) {
		this.orgPrice = orgPrice;
	}

	public String getRealRate() {
		return realRate;
	}

	public void setRealRate(String realRate) {
		this.realRate = realRate;
	}

}
