/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgAdjustDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgTotalDao;
import com.sieyuan.shrcn.tool.pricemanager.data.AdjustPriceHistory;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgTotal;
import com.sieyuan.shrcn.tool.pricemanager.utils.CalcUtil;
import com.sieyuan.shrcn.tool.pricemanager.utils.LogUtils;
import com.sieyuan.shrcn.tool.pricemanager.utils.PriceResolver;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;
import com.sieyuan.shrcn.tool.pricemanager.views.table.TableFactory;


public class ProjectAdjustPriceDialog extends WrappedDialog {

	private UIPreferences perference = UIPreferences.newInstance();

	private String ID_LIMIT = "idLimit";

	private PriceResolver priceResolver;
	private String idLimit;
	private RKTable pkgTb;
	
	// 是否保留历史
	private Button checkBtn;
	private Text onlyIdRateText; // 独有ID占比输入框

	public ProjectAdjustPriceDialog(Shell parentShell) {
		super(parentShell);
	}

	@SuppressWarnings("unchecked")
	@Override
	protected void buttonPressed(int buttonId) {
		idLimit = perference.getInfo(EnvirmentSettingDialog.class.getName() + ID_LIMIT);
		if (StringUtils.isEmpty(idLimit)) {
			idLimit = ToolConstants.LIMIT;
		}
		// 新增：保存独有ID占比
		String onlyIdRateValue = onlyIdRateText != null ? onlyIdRateText.getText() : "0.0";
		perference.setInfo(EnvirmentSettingDialog.class.getName() + ".onlyIdRate", onlyIdRateValue);
		if (buttonId == IDialogConstants.ABORT_ID) {
			clearHistory();
			pkgTb.refresh();
		} if (buttonId == IDialogConstants.BACK_ID) {
			final List<PkgAdjust> adjsutList = (List<PkgAdjust>) pkgTb.getInput();
			for (PkgAdjust pkgadjust : adjsutList) {
				pkgadjust.setTargetprice(pkgadjust.getTargetprice().trim());
				if (StringUtils.isEmpty(pkgadjust.getTargetprice())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入设定目标价，请检查！");
					return;
				}
				if (!CalcUtil.isDouble(pkgadjust.getTargetprice())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的设定目标价不合法，请检查！");
					return;
				}

				if (Double.valueOf(pkgadjust.getTargetprice()) < 0) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的设定目标价不合法，请检查！");
					return;
				}

				if (StringUtils.isEmpty(pkgadjust.getRate())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入110KV设定折扣，请检查！");
					return;
				}
				if (StringUtils.isEmpty(pkgadjust.getRate2())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入220KV设定折扣，请检查！");
					return;
				}
				if (!CalcUtil.isDouble(pkgadjust.getRate())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的设定折扣不合法，请检查！");
					return;
				}

				if (Double.valueOf(pkgadjust.getRate()) < 0) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的设定折扣不合法，请检查！");
					return;
				}
				if (StringUtils.isEmpty(pkgadjust.getRor())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入110KV微调系数，请检查！");
					return;
				}
				if (StringUtils.isEmpty(pkgadjust.getRor2())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入220KV微调系数，请检查！");
					return;
				}
				if (!CalcUtil.isDouble(pkgadjust.getRor())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的110KV微调系数不合法，请检查！");
					return;
				}

				if (Double.valueOf(pkgadjust.getRor()) < 0) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的110KV微调系数不合法，请检查！");
					return;
				}
				
				if (!CalcUtil.isDouble(pkgadjust.getRor2())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的220KV微调系数不合法，请检查！");
					return;
				}

				if (Double.valueOf(pkgadjust.getRor2()) < 0) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的220KV微调系数不合法，请检查！");
					return;
				}
			}
			ProgressManager.execute(new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
					monitor.beginTask("正在统计独有ID占比数据中，请稍候...", 100);
					priceResolver = new PriceResolver();
					priceResolver.calOnlyRate(adjsutList);
					monitor.done();
				}
			});	
			pkgTb.refresh();
		} else if (buttonId == OK) {
			final List<PkgAdjust> adjsutList = (List<PkgAdjust>) pkgTb.getInput();
			final List<PkgAdjust> selectedAdjust = new ArrayList<>();

			for (PkgAdjust pkgadjust : adjsutList) {

				pkgadjust.setTargetprice(pkgadjust.getTargetprice().trim());
				if (StringUtils.isEmpty(pkgadjust.getTargetprice())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入设定目标价，请检查！");
					return;
				}
				if (!CalcUtil.isDouble(pkgadjust.getTargetprice())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的设定目标价不合法，请检查！");
					return;
				}

				if (Double.valueOf(pkgadjust.getTargetprice()) < 0) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的设定目标价不合法，请检查！");
					return;
				}

				if (StringUtils.isEmpty(pkgadjust.getRate())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入110KV设定折扣，请检查！");
					return;
				}
				if (StringUtils.isEmpty(pkgadjust.getRate2())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入220KV设定折扣，请检查！");
					return;
				}
				if (!CalcUtil.isDouble(pkgadjust.getRate())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的设定折扣不合法，请检查！");
					return;
				}

				if (Double.valueOf(pkgadjust.getRate()) < 0) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的设定折扣不合法，请检查！");
					return;
				}
				if (StringUtils.isEmpty(pkgadjust.getRor())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入110KV微调系数，请检查！");
					return;
				}
				if (StringUtils.isEmpty(pkgadjust.getRor2())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "未输入220KV微调系数，请检查！");
					return;
				}
				if (!CalcUtil.isDouble(pkgadjust.getRor())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的110KV微调系数不合法，请检查！");
					return;
				}

				if (Double.valueOf(pkgadjust.getRor()) < 0) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的110KV微调系数不合法，请检查！");
					return;
				}
				
				if (!CalcUtil.isDouble(pkgadjust.getRor2())) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的220KV微调系数不合法，请检查！");
					return;
				}

				if (Double.valueOf(pkgadjust.getRor2()) < 0) {
					DialogHelper.showAsynError(pkgadjust.getPkgname() + "输入的220KV微调系数不合法，请检查！");
					return;
				}
				
				selectedAdjust.add(pkgadjust);
			}

			if (selectedAdjust == null || selectedAdjust.size() == 0) {
				DialogHelper.showAsynWarning("请选择需要计算的包号");
				return;
			}

			final List<String> warns = new ArrayList<>();
			final boolean selection = checkBtn.getSelection();

			ProgressManager.execute(new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
					monitor.beginTask("正在计算数据中，请稍候...", 100);

					// 清除上一次的调价数据
					PkgInfoDao.deletePkgInfo();
					PkgProductDao.deletePkgProductPrice();

					priceResolver = new PriceResolver();

					List<String> pkgNames = new ArrayList<>();
					for (PkgAdjust pkgadjust : adjsutList) {
						pkgNames.add(pkgadjust.getPkgname());
					}

					priceResolver.adjustPrices(adjsutList, monitor);
					priceResolver.updateCalResult();
					priceResolver.updateResult(adjsutList);
					warns.addAll(priceResolver.getWarnings());

					for (PkgAdjust pkgadjust : selectedAdjust) {
						// BigDecimal percent = new
						// BigDecimal(pkgadjust.getTargetprice()).divide(new
						// BigDecimal(pkgadjust.getLimitprice()),
						// ToolConstants.pPrice, BigDecimal.ROUND_DOWN);
						// 查询总数，插入数据库中
						List<PkgTotal> pkgTotals = PkgInfoDao.getPkgAnalysis(pkgadjust.getPkgname());
						PkgTotalDao.deletePkgTotals(pkgTotals);
						for (PkgTotal pkg : pkgTotals) {
							pkg.setTargetprice(String.valueOf(CalcUtil.getBigDecimal(pkgadjust.getTargetprice())));
							pkg.setRate(pkgadjust.getRate());
							pkg.setRate2(pkgadjust.getRate2());
							pkg.setPrice(String.valueOf(CalcUtil.getBigDecimalString(new BigDecimal(pkg.getPrice()))));
							// (对外报价-成本)/税率
							BigDecimal contribution = (new BigDecimal(pkg.getPrice()).subtract(new BigDecimal(pkg.getCostTax()))).divide(ToolConstants.taxRate, ToolConstants.pPrice, BigDecimal.ROUND_DOWN);
							pkg.setContribution(CalcUtil.getBigDecimalString(contribution));
							pkg.setCostTax(String.valueOf(CalcUtil.getBigDecimalString(new BigDecimal(pkg.getCostTax()))));
							pkg.setLimitprice(String.valueOf(CalcUtil.getBigDecimalString(new BigDecimal(pkg.getLimitprice()))));
						}
						PkgTotalDao.savePkgTotals(pkgTotals);
					}

					List<String> warnings = new ArrayList<>();
					// 检查是否超20%
					List<PkgInfo> pkgs = PkgInfoDao.getPkgs();
					CalcUtil.checkAvageValues(warnings, pkgNames, pkgs, idLimit);

					// 是否配置按照名称检查偏差
					boolean offsetCheckValue = false;
					String offsetValue = perference.getInfo(OptionConfigDialog.class.getName() + ".offset");
					if (!StringUtils.isEmpty(offsetValue)) {
						offsetCheckValue = Boolean.valueOf(offsetValue);
					}
					if (offsetCheckValue) {
						CalcUtil.checkProductNameAvageValues(warnings, pkgNames, pkgs);
					}

					PkgInfoDao.updatePkgValid(pkgs);
					warns.addAll(warnings);
					PkgAdjustDao.saveNewPkgAdjusts(selectedAdjust);
					NavigatView.refreshTree();
					// 记录调价历史
				
					if (selection) {
						new AdjustPriceHistory().savePriceHistory(perference.getInfo("com.shrcn.pricemangertool.curentprojectname"));
					}
					// 保存配置
					monitor.worked(10);
					monitor.done();
				}
			});

			// 弹出窗口告警提示
			if (warns != null && warns.size() > 0) {
				DialogHelper.showAsynError("谈价失败，详细原因请参考控制台或日志记录检查！");
				LogUtils.recordErrInfo(warns);
			} else {
				DialogHelper.showInformation("调价成功！");
			}

			pkgTb.refresh();
			return;
		}
		super.buttonPressed(buttonId);
	}

	@SuppressWarnings("unchecked")
	private void clearHistory() {
		List<PkgAdjust> sList = (List<PkgAdjust>) pkgTb.getInput();
		for (PkgAdjust adjust : sList) {
			adjust.setRate("");
			adjust.setRate2("");
			adjust.setResult("");
			adjust.setTargetprice("");
		}
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("调价计算");
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		((GridLayout) parent.getLayout()).numColumns++;
		checkBtn = SwtUtil.createCheckBox(parent, "保留调价历史", new GridData());
		createButton(parent, IDialogConstants.BACK_ID, "自动计算优先级", false);
		createButton(parent, IDialogConstants.ABORT_ID, "清空", false);
		createButton(parent, IDialogConstants.OK_ID, "执行", true);
		createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		parent.setLayout(SwtUtil.getGridLayout(1));

		// 新增：独有ID占比输入框
		Composite topComp = new Composite(parent, SWT.NONE);
		topComp.setLayout(new GridLayout(3, false));
		GridData gd = new GridData(SWT.LEFT, SWT.LEFT, false, false, 2, 1);
		gd.widthHint = 300;
		onlyIdRateText = SwtUtil.createLabelText(topComp, "独有ID微调系数：", gd);
		String onlyIdRate = perference.getInfo(EnvirmentSettingDialog.class.getName() + ".onlyIdRate");
		if (onlyIdRate == null || onlyIdRate.isEmpty()) {
			onlyIdRate = "1.0";
		}
		onlyIdRateText.setText(onlyIdRate);

		this.pkgTb = TableFactory.getPkgAdjustTable(parent);
		pkgTb.getTable().setLayoutData(new GridData(GridData.FILL_BOTH));
		pkgTb.addPropertyChangeListener(new PropertyChangeListener() {

			@Override
			public void propertyChange(PropertyChangeEvent event) {
				if (event.getOldValue().equals(event.getNewValue())) {
					return;
				}
				if (!CalcUtil.isDouble(event.getNewValue().toString())) {
					DialogHelper.showErrorAsyn(event.getNewValue() + "非法的设定目标价！");
					return;
				}
			}
		});
		initData();
		return parent;
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(1120, 700);
	}

	private void initData() {
		List<PkgAdjust> sList = PkgAdjustDao.getNewPkgAdjust();

		Collections.sort(sList, new Comparator<PkgAdjust>() {
			public int compare(PkgAdjust obj1, PkgAdjust obj2) {
				Integer pkg1 = Integer.valueOf(obj1.getPkgname().replace("包", ""));
				Integer pkg2 = Integer.valueOf(obj2.getPkgname().replace("包", ""));
				// 升序排序
				return Integer.compare(pkg1, pkg2);
			}
		});
		pkgTb.setInput(sList);
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
