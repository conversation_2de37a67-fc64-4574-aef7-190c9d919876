/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.SwtUtil;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.sax.BaseInfoParser;

/**
 * @Description:包选择界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 
 */
public class PkgSelectDialog extends WrappedDialog {

    private org.eclipse.swt.widgets.List lsPackages;
    private String pkgPath;
    private String[] pkgs;

    public PkgSelectDialog(Shell parentShell, String root) {
        super(parentShell);
        this.pkgPath = root;
    }

    @Override
    protected void buttonPressed(int buttonId) {
        if (buttonId == OK) {
            pkgs = lsPackages.getSelection();
        } else {
            pkgs = null;
        }
        super.buttonPressed(buttonId);
    }

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("请选择需要导入的包");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "确定", true);
        createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        Composite container = (Composite)super.createDialogArea(parent);
        SwtUtil.createLabel(container, "包号：", null);
        GridData listData = new GridData(GridData.FILL_BOTH);
        lsPackages = SwtUtil.createMultiList(container, listData);
        init();
        return container;
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(400, 500);
    }

    public String[] getPkgs() {
        return pkgs;
    }

    private void init() {
        BaseInfoParser importDirectoryParser = new BaseInfoParser(pkgPath);
        importDirectoryParser.parseAllPkg();
        List<PkgInfo> allFile = importDirectoryParser.getAllPkgs();
        Comparator<String> comparator = new Comparator<String>() {
            public int compare(String obj1, String obj2) {
                // 升序排序
				Integer pkg1 = Integer.valueOf(obj1.replace("包", ""));
				Integer pkg2 = Integer.valueOf(obj2.replace("包", ""));
				// 升序排序
				return Integer.compare(pkg1, pkg2);
            }
        };

        TreeSet<String> pkgNames = new TreeSet<>(comparator);
        for (PkgInfo pkgInfo : allFile) {
            if (!pkgNames.contains(pkgInfo.getName())) {
                pkgNames.add(pkgInfo.getName());
            }
        }
        lsPackages.setItems(pkgNames.toArray(new String[pkgNames.size()]));
        lsPackages.selectAll();
    }

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
