package com.sieyuan.shrcn.tool.pricemanager.views.table;

import java.awt.Toolkit;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.util.List;

import org.eclipse.swt.widgets.Composite;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.UIConstants;
import com.shrcn.found.ui.model.TableConfig;
import com.shrcn.found.ui.table.DefaultKTable;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.table.XOTable;
import com.shrcn.found.ui.table.action.CopyAction;
import com.shrcn.found.ui.table.action.TableAction;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ImageConstants;
import com.shrcn.found.ui.util.ImgDescManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;

/**
 * @Description:单个包价格调整
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午5:01:27
 
 */
public class PkgAdjustTable extends RKTable {

	class CopyValueAction extends CopyAction {

		public CopyValueAction(XOTable table) {
			super(table);
		}

		public void run() {
			List<Object> list = getSelections();
			int col = getSelectColNum();
			StringBuffer textBuff = new StringBuffer();
			for (Object obj : list) {
				PkgAdjust adjust = (PkgAdjust) obj;
				if (col == 1) {
					textBuff.append(adjust.getPkgname());
				} else if (col == 2) {
					textBuff.append(adjust.getLimitprice());
				} else if (col == 3) {
					textBuff.append(adjust.getRate());
				} else if (col == 4) {
					textBuff.append(adjust.getRate2());
				} else if (col == 5) {
					textBuff.append(adjust.getRealPrice());
				} else if (col == 6) {
					textBuff.append(adjust.getRor());
				} else if (col == 7) {
					textBuff.append(adjust.getRor2());
				} else if (col == 8) {
					textBuff.append(adjust.getHasOnlyId());
				} else if (col == 9) {
					textBuff.append(adjust.getTargetprice());
				} else if (col == 10) {
					textBuff.append(adjust.getResult());
				} else if (col == 11) {
					textBuff.append(adjust.getIsPriority());
				} else if (col == 12) {
					textBuff.append(adjust.getSeq());
				}
				textBuff.append("\r\n"); // 添加一行的结束，回车
			}
			String text = new String(textBuff);
			Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
			StringSelection str = new StringSelection(text);
			clipboard.setContents(str, null);
		}
	}
	
	class PasteRorAction extends TableAction {

		public PasteRorAction(XOTable table) {
			super(table);
			setText("粘贴110KV微调系数");
			setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
		}

		@SuppressWarnings("unchecked")
		@Override
		public void run() {
			String clipBoardContent = SwtUtil.getClipBoardContent();
			if (StringUtil.isEmpty(clipBoardContent)) {
				DialogHelper.showWarning("复制参数为空！");
				return;
			}
			String[] str = clipBoardContent.split("\\r\\n");
			List<PkgAdjust> input = (List<PkgAdjust>) table.getInput();
			if (input.size() != str.length) {
				DialogHelper.showWarning("复制条数与表格内条数不一致！");
				return;
			}
			for (int i = 0; i < input.size(); i++) {
				PkgAdjust pkgAdjust = input.get(i);
				pkgAdjust.setRor(str[i].trim());

			}
			table.setInput(input);
			table.refresh();
		}
	}
	
	class PasteRor2Action extends TableAction {

		public PasteRor2Action(XOTable table) {
			super(table);
			setText("粘贴220KV微调系数");
			setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
		}

		@SuppressWarnings("unchecked")
		@Override
		public void run() {
			String clipBoardContent = SwtUtil.getClipBoardContent();
			if (StringUtil.isEmpty(clipBoardContent)) {
				DialogHelper.showWarning("复制参数为空！");
				return;
			}
			String[] str = clipBoardContent.split("\\r\\n");
			List<PkgAdjust> input = (List<PkgAdjust>) table.getInput();
			if (input.size() != str.length) {
				DialogHelper.showWarning("复制条数与表格内条数不一致！");
				return;
			}
			for (int i = 0; i < input.size(); i++) {
				PkgAdjust pkgAdjust = input.get(i);
				pkgAdjust.setRor2(str[i].trim());

			}
			table.setInput(input);
			table.refresh();
		}
	}

	class PasteAction extends TableAction {

		public PasteAction(XOTable table) {
			super(table);
			setText("粘贴目标价");
			setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
		}

		@SuppressWarnings("unchecked")
		@Override
		public void run() {
			String clipBoardContent = SwtUtil.getClipBoardContent();
			if (StringUtil.isEmpty(clipBoardContent)) {
				DialogHelper.showWarning("复制参数为空！");
				return;
			}
			String[] str = clipBoardContent.split("\\r\\n");
			List<PkgAdjust> input = (List<PkgAdjust>) table.getInput();
			if (input.size() != str.length) {
				DialogHelper.showWarning("复制条数与表格内条数不一致！");
				return;
			}
			for (int i = 0; i < input.size(); i++) {
				PkgAdjust pkgAdjust = input.get(i);
				pkgAdjust.setTargetprice(str[i].trim());

			}
			table.setInput(input);
			table.refresh();
		}
	}

	class PasteSeqAction extends TableAction {

		public PasteSeqAction(XOTable table) {
			super(table);
			setText("粘贴优先级");
			setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
		}

		@SuppressWarnings("unchecked")
		@Override
		public void run() {
			String clipBoardContent = SwtUtil.getClipBoardContent();
			if (StringUtil.isEmpty(clipBoardContent)) {
				DialogHelper.showWarning("复制参数为空！");
				return;
			}
			String[] str = clipBoardContent.split("\\r\\n");
			List<PkgAdjust> input = (List<PkgAdjust>) table.getInput();
			if (input.size() != str.length) {
				DialogHelper.showWarning("复制条数与表格内条数不一致！");
				return;
			}
			for (int i = 0; i < input.size(); i++) {
				PkgAdjust pkgAdjust = input.get(i);
				pkgAdjust.setSeq(Integer.valueOf(str[i].trim()));

			}
			table.setInput(input);
			table.refresh();
		}
	}
	class PastePercentAction extends TableAction {

		public PastePercentAction(XOTable table) {
			super(table);
			setText("粘贴110KV折扣");
			setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
		}

		@SuppressWarnings("unchecked")
		@Override
		public void run() {
			String clipBoardContent = SwtUtil.getClipBoardContent();
			if (StringUtil.isEmpty(clipBoardContent)) {
				DialogHelper.showWarning("复制参数为空！");
				return;
			}
			String[] str = clipBoardContent.split("\\r\\n");
			List<PkgAdjust> input = (List<PkgAdjust>) table.getInput();
			if (input.size() != str.length) {
				DialogHelper.showWarning("复制条数与表格内条数不一致！");
				return;
			}
			for (int i = 0; i < input.size(); i++) {
				PkgAdjust pkgAdjust = input.get(i);
				pkgAdjust.setRate(str[i].trim());
			}
			table.setInput(input);
			table.refresh();
		}
	}
	
	class PastePercent2Action extends TableAction {

		public PastePercent2Action(XOTable table) {
			super(table);
			setText("粘贴220KV折扣");
			setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
		}

		@SuppressWarnings("unchecked")
		@Override
		public void run() {
			String clipBoardContent = SwtUtil.getClipBoardContent();
			if (StringUtil.isEmpty(clipBoardContent)) {
				DialogHelper.showWarning("复制参数为空！");
				return;
			}
			String[] str = clipBoardContent.split("\\r\\n");
			List<PkgAdjust> input = (List<PkgAdjust>) table.getInput();
			if (input.size() != str.length) {
				DialogHelper.showWarning("复制条数与表格内条数不一致！");
				return;
			}
			for (int i = 0; i < input.size(); i++) {
				PkgAdjust pkgAdjust = input.get(i);
				pkgAdjust.setRate2(str[i].trim());
			}
			table.setInput(input);
			table.refresh();
		}
	}

	public PkgAdjustTable(Composite parent, TableConfig config) {
		super(parent, config);
	}

	@Override
	public void setCellValue(Object element, String property, Object newValue) {
		PkgAdjust adjust = (PkgAdjust) element;
		if (property.equals("isPriority")) {
			if (!adjust.getHasOnlyId()) {
				DialogHelper.showAsynError("不能取消此项优先级设置");
				return;
			}
		}
		super.setCellValue(element, property, newValue);
	}

	@Override
	protected void initUI() {
		tablemodel = new PkgAdjustTableModel(this, config);
		table = new DefaultKTable(parent, UIConstants.KTABLE_CELL_STYLE, tablemodel);
	}

	@Override
	protected void initData() {
	}

	@Override
	protected void initOthers() {

		actions.add(new CopyValueAction(this));
		// actions.add(new SetOrderAction(this));
		// actions.add(new SetRuleAction(this));
		actions.add(new PastePercentAction(this));
		actions.add(new PastePercent2Action(this));
		actions.add(new PasteRorAction(this));
		actions.add(new PasteRor2Action(this));
		actions.add(new PasteAction(this));
		actions.add(new PasteSeqAction(this));
	}

}
