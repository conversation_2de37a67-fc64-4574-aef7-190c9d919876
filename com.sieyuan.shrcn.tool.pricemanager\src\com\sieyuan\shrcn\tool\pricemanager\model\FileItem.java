/*
 * @(#) FileItem.java
 * 
 * Copyright (c) 2007 - 2013 上海思源弘瑞电力自动化有限公司. All rights reserved. 基于 Eclipse E4 a next generation platform (e.g., the
 * CSS styling, dependency injection, Modeled UI) Rich Client Application 开发的U21继电保护装置平台工具软件. 此系列工具软件包括装置调试下载工具、装置配置工具.
 * 103通信调试工具、 自动化装置测试工具。
 */
package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * @Description: 文件基本属性
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 
 */
public class FileItem {

    private String absFileName;

    /** 状态 */

    /** 文件是否选择 */
    private boolean checked = false;
    private String fileName;

    private String fileSize;
    private String fileType;
    /** 文件选择行号 */
    private int row = 0;
    private String status = "";
    public FileItem() {

    }

    public String getAbsFileName() {
        return absFileName;
    }

    public String getFileName() {
        return fileName;
    }

    public String getFileSize() {
        return fileSize;
    }

    public String getFileType() {
        return fileType;
    }

    public int getRow() {
        return row;
    }

    public String getStatus() {
        return status;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setAbsFileName(String absFileName) {
        this.absFileName = absFileName;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public void setStatus(String status) {
        this.status = status;
    }

}
