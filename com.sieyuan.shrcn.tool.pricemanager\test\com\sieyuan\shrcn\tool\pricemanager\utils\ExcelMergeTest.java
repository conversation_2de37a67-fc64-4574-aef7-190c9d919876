package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;

public class ExcelMergeTest {

	@Test
	public void mergeExcel() throws Exception {
		List<String> files = new ArrayList<>();
		files.add("D:\\开标文件\\包21\\1_A458-500008896-00230_001375087200020.xlsx");
		files.add("D:\\开标文件\\包21\\2_A458-500008896-00228_001374654500220.xlsx");
		files.add("D:\\开标文件\\包21\\3_A442-500008896-00156_001375170500020.xlsx");
		String destFileName = "D://1.xlsx";
		ExcelMergeUtil.mergeExcel(files, destFileName, true);
		ExcelMergeUtil.mergeExcelFiles(files, destFileName, true);
	}
	
	@Test
	public void mergeExcel2() throws Exception {
		List<String> files = new ArrayList<>();
		files.add("D:\\开标文件\\包21\\001375087200020.xlsx");
		files.add("D:\\开标文件\\包21\\001374654500220.xlsx");
		files.add("D:\\开标文件\\包21\\001375170500020.xlsx");
		String destFileName = "D://1.xlsx";
		ExcelMergeUtil.mergeExcel(files, destFileName, true);
		ExcelMergeUtil.mergeExcelFiles(files, destFileName, true);
	}
}
