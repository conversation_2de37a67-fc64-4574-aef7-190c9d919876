package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.io.InputStream;
import java.util.List;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.junit.Test;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.file.excel.Xls2007Parser;
import com.sieyuan.shrcn.tool.pricemanager.model.PriceRate;

public class PriceRateHandlerTest {

	private String priceRatepath = "D://1.xlsx";

	@Test
	public void testParse() {
		try {
			OPCPackage xlsxPackage = OPCPackage.open(priceRatepath, PackageAccess.READ);
			ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
			XSSFReader xssfReader = new XSSFReader(xlsxPackage);
			StylesTable styles = xssfReader.getStylesTable();
			XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
			while (iter.hasNext()) {
				InputStream stream = iter.next();
				PriceRateHandler priceHandler = new PriceRateHandler();
				Xls2007Parser.processSheet(styles, strings, priceHandler, stream);
				List<PriceRate> priceRatePrices = priceHandler.getPriceRateList();
				System.out.println(priceRatePrices.size());
				stream.close();
			}
			xlsxPackage.close();
		} catch (Throwable e) {
			SCTLogger.error(e.getMessage());
		}
	}

}
