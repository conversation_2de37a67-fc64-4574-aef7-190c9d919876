package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.util.List;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.SwtUtil;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgTotalDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgTotal;
import com.sieyuan.shrcn.tool.pricemanager.views.table.TableFactory;

/**
 * @Description:统计结果
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON><PERSON><PERSON>
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-18 上午10:01:29
 */
public class AnalysisByNameDialog extends WrappedDialog {

    private RKTable pkgTb;

    public AnalysisByNameDialog(Shell parentShell) {
        super(parentShell);
    }

    @Override
    protected void buttonPressed(int buttonId) {
        if (buttonId == OK) {
            pkgTb.exportExcel("报价结果");
            DialogHelper.showAsynInformation("导出文件成功");
            return;
        }
        super.buttonPressed(buttonId);
    }

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("按包号统计結果");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "导出", true);
        createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {

        parent.setLayout(SwtUtil.getGridLayout(1));

        this.pkgTb = TableFactory.getPkgTotalTable(parent);
        pkgTb.getTable().setLayoutData(new GridData(GridData.FILL_BOTH));

        initData();
        return parent;
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(850, 500);
    }

    private void initData() {
        List<PkgTotal> sList = PkgTotalDao.getPkgTotal();
        pkgTb.setInput(sList);
    }
    
	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}


}
