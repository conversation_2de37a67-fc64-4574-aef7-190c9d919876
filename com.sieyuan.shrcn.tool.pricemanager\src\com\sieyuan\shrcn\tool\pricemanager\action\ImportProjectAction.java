package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.dialog.ProjectImportDialog;

/**
 * @Description:导入工程
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:10:39
 
 */
public class ImportProjectAction extends MenuAction {

    public ImportProjectAction(String text) {
        super(text);
		setAccelerator(SWT.CTRL + 'P');
    }

    @Override
    public void run() {
		ProjectImportDialog expDlg = new ProjectImportDialog(Display.getDefault().getActiveShell());
		expDlg.open();
    }
}
