/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.sql.SQLException;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.TimeCounter;
import com.shrcn.found.file.util.FileManipulate;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.dao.ProjectDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.SqliteHelper;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.Project;
import com.sieyuan.shrcn.tool.pricemanager.utils.UIPreferencesUtil;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;

/**
 * @Description:导入工程
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 */
public class ProjectImportDialog extends WrappedDialog {
	
	private Text prjNameText;
	private Text prjFileText;
    private String ID = ".id";
    private UIPreferences perference = UIPreferences.newInstance();

    public ProjectImportDialog(Shell parentShell) {
        super(parentShell);
    }

    @Override
    protected void buttonPressed(int buttonId) {

        if (buttonId == OK) {
        	
			final String prjName = prjNameText.getText();
			final String prjFile = prjFileText.getText();
			if (StringUtils.isEmpty(prjName)) {
				DialogHelper.showAsynWarning("工程名称不能为空！");
				return;
			}
			if (StringUtils.isEmpty(prjFile)) {
				DialogHelper.showAsynWarning("数据文件不能为空！");
				return;
			}
			if (!new File(prjFile).exists()) {
				DialogHelper.showAsynWarning("数据文件不存在！");
				return;
			}
			
            ProgressManager.execute(new IRunnableWithProgress() {
                @Override
                public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
                    monitor.beginTask("正在导入工程数据中，请稍候...", 100);
                    TimeCounter.begin();
                    importPrj(prjName, prjFile, monitor);
                    TimeCounter.end("导出总耗时");
                    ConsoleManager.getInstance().append("导入工程完成！");
                    monitor.done();
                }

            });
        }
        super.buttonPressed(buttonId);
    }
    
    /**
     * 创建工程
     * @param prjName 工程名称
     * @param prjFile 工程文件
     * @param monitor 进度
     */
	private void importPrj(String prjName, String prjFile, IProgressMonitor monitor) {
        String zbId = perference.getInfo(EnvirmentSettingDialog.class.getName() + ID);
		Project project = new Project();
		project.setProjectName(prjName);
		project.setZbId(zbId);
		project.setTaxRate(String.valueOf(UIPreferencesUtil.getTaxRate()));
		ProjectDao.addProject(project);
		perference.setInfo("com.shrcn.pricemangertool.curentprojectname", prjName);
		String dbPath = DirManager.getProjectFile(prjName);
		FileManipulate.copyByChannel(prjFile, dbPath);

		SqliteHelper sqliteHelper = null;
		try {
			sqliteHelper = new SqliteHelper(dbPath);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		GlobalData.getInstance().setSqliteHelper(sqliteHelper);
		monitor.done();
		NavigatView.refreshTree();
	}

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("导入工程");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "确定", true);
        createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
    }

    @Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		container.setLayout(new GridLayout(3, false));
		prjNameText = SwtUtil.createLabelText(container, "工程名称：");
		GridData layoutData = new GridData();
		layoutData.horizontalSpan = 2;
		layoutData.widthHint = 200;
		prjNameText.setLayoutData(layoutData);
		prjFileText = SwtUtil.createFileSelector(container, "数据文件：", "*.db");
		return container;
	}

    /**
     * 对话框的尺寸.
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(550, 210);
    }


	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
