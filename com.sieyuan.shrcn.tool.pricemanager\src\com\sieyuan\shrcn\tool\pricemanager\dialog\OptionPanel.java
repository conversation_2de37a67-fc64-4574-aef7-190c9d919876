/**
 * Copyright (c) 2007-2010 上海思弘瑞电力控制技术有限公司.
 * All rights reserved. This program is an eclipse Rich Client Application
 * based on IEC61850 SCT.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.FormAttachment;
import org.eclipse.swt.layout.FormData;
import org.eclipse.swt.widgets.Composite;

import com.shrcn.found.ui.UIConstants;

/**
 * 选项配置
 * 
 * <AUTHOR>
 * @version 1.0, 2020-12-2
 * 
 */
public abstract class OptionPanel extends Composite {
	
	public OptionPanel(Composite parent) {
		super(parent, SWT.NONE);
		init();
		setBackground(UIConstants.CONTENT_BG);
	}
	
	/**
	 * 界面初始化
	 */
	public void init() {
		createContent();
		addListeners();
		layout();
		FormData formData = new FormData();
		formData.left = new FormAttachment(0, 0);
		formData.top = new FormAttachment(0, 0);
		formData.right = new FormAttachment(100, 0);
		formData.bottom = new FormAttachment(100, 0);
		setLayoutData(formData);
		setVisible(false);
	}
	
	protected abstract void createContent();

	protected abstract void addListeners();
	
}
