package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.spire.xls.ExcelVersion;
import com.spire.xls.Workbook;
import com.spire.xls.Worksheet;
import com.spire.xls.WorksheetCopyType;
import com.spire.xls.collections.WorksheetsCollection;

/**
 * 
 * Excel 合并多个excel到多个Sheet
 * 
 * <AUTHOR> (mailto:<EMAIL>)
 * @version 1.0, 2020-4-21
 */
public class ExcelMergeUtil {
	
	private static Map<String, XSSFCellStyle> styleMap;
	private static Map<String, Font> fontMap;


	/**
	 * 合并excel至多个sheet
	 * 
	 * @param files
	 * @param destFileName
	 */
	public static void mergeExcel(List<String> files, String destFileName, boolean replaceSheetName) {
		styleMap = new HashMap<>();
		fontMap = new HashMap<>();
		XSSFWorkbook newExcelCreat = new XSSFWorkbook();
		InputStream in = null;
		XSSFWorkbook fromExcel = null;
		FileOutputStream fileOut = null;

		try {
			for (String fromExcelName : files) { // 遍历每个源excel文件，TmpList为源文件的名称集合
				String[] fileArray = new File(fromExcelName).getName().replace(ToolConstants.XLSX, "").split("_");
				String applyId = "";
				String bid = "";
				if (fileArray.length == 3) {
					bid = fileArray[1];
					applyId = fileArray[2];
				} else {
					continue;
				}
				in = new FileInputStream(fromExcelName);
				fromExcel = new XSSFWorkbook(in);

				String sheetName = "";
				int length = fromExcel.getNumberOfSheets();
				if (length <= 1) { // 长度为1时
					XSSFSheet oldSheet = fromExcel.getSheetAt(0);
					if (replaceSheetName) {
						// sheetName = bid + "(" + applyId + ")";
						sheetName = applyId;
					} else {
						sheetName = oldSheet.getSheetName();
					}
					XSSFSheet newSheet = newExcelCreat.createSheet(sheetName);
					copySheet(newExcelCreat, oldSheet, newSheet);
				}
				else {
					for (int i = 0; i < length; i++) {// 遍历每个sheet
						XSSFSheet oldSheet = fromExcel.getSheetAt(i);
						if (replaceSheetName) {
							sheetName = bid + "(" + applyId + ")";
						} else {
							sheetName = oldSheet.getSheetName();
						}
						XSSFSheet newSheet = newExcelCreat.createSheet(sheetName);
						copySheet(newExcelCreat, oldSheet, newSheet);
					}
				}
				in.close();
				fromExcel.close();
			}

			fileOut = new FileOutputStream(destFileName);
			newExcelCreat.write(fileOut);
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				if (in != null) {
					in.close();
				}
				if (fromExcel != null) {
					fromExcel.close();
				}
				if (fileOut != null) {
					fileOut.flush();
				}
				if (fileOut != null) {
					fileOut.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	public static void copyCellStyle(XSSFCellStyle fromStyle, XSSFCellStyle toStyle) {
		toStyle.cloneStyleFrom(fromStyle);// 此一行代码搞定
	}

	/**
	 * 合并单元格
	 * 
	 * @param fromSheet
	 * @param toSheet
	 */
	public static void mergeSheetAllRegion(XSSFSheet fromSheet, XSSFSheet toSheet) {
		int num = fromSheet.getNumMergedRegions();
		CellRangeAddress cellR = null;
		for (int i = 0; i < num; i++) {
			cellR = fromSheet.getMergedRegion(i);
			toSheet.addMergedRegion(cellR);
		}
	}

	/**
	 * 复制单元格
	 * 
	 * @param wb
	 * @param fromCell
	 * @param toCell
	 * @param rowNum
	 * @param rowNum
	 * @param allRow
	 */
	@SuppressWarnings("deprecation")
	public static void copyCell(XSSFWorkbook wb, XSSFCell fromCell, XSSFCell toCell, int rowNum, int allRow) {
		XSSFCellStyle newstyle = null;
		// if (rowNum > 6) {
		XSSFCellStyle style = fromCell.getCellStyle();
		String key = wb.hashCode() + "," + style.getAlignment() + "," + style.getFont().getBold() + "," + style.getFont().getFontHeightInPoints() + "," + style.getAlignmentEnum() + "," + style.getVerticalAlignment() + "," + style.getBorderBottom();
		newstyle = styleMap.get(key);
		if (newstyle == null) {
			newstyle = wb.createCellStyle();
			styleMap.put(key, newstyle);
		}
		if (rowNum >= 6) {
			if (rowNum < allRow - 3) {
				if (rowNum == 6) {
					Font font10 = fontMap.get("contentheader");
					if (font10 == null) {
						font10 = wb.createFont();
						font10.setFontName("宋体");// 设置字体
						font10.setFontHeightInPoints((short) 10);// 设置字体大小
						fontMap.put("contentheader", font10);
						font10.setBold(false);
						newstyle.setFont(font10);
					}
				} else if (rowNum > 6) {
					Font font10 = fontMap.get("normal");
					if (font10 == null) {
						font10 = wb.createFont();
						font10.setFontName("宋体");// 设置字体
						font10.setFontHeightInPoints((short) 10);// 设置字体大小
						fontMap.put("normal", font10);
						font10.setBold(false);
						newstyle.setFont(font10);
					}
				}

				newstyle.setAlignment(HorizontalAlignment.CENTER); // 居中// 左对齐
																	// 居中 右对齐
				newstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直
				newstyle.setBorderBottom(BorderStyle.THIN); // 下边框
				newstyle.setBorderLeft(BorderStyle.THIN);// 左边框
				newstyle.setBorderTop(BorderStyle.THIN);// 上边框
				newstyle.setBorderRight(BorderStyle.THIN);// 右边框
				newstyle.setWrapText(true);// 设置自动换行
			} else {
				Font font10 = fontMap.get("content");
				if (font10 == null) {
					font10 = wb.createFont();
					font10.setFontName("宋体");// 设置字体
					font10.setFontHeightInPoints((short) 10);// 设置字体大小
					font10.setBold(false);
					fontMap.put("content", font10);
				}
				newstyle.setFont(font10);
				newstyle.setVerticalAlignment(VerticalAlignment.CENTER);// 垂直
			}
		}

		if (rowNum == 0) {
			Font font16 = fontMap.get("header");
			if (font16 == null) {
				font16 = wb.createFont();
				font16.setFontName("宋体");// 设置字体
				font16.setFontHeightInPoints((short) 16);// 设置字体大小
				font16.setBold(true);
				fontMap.put("header", font16);
			}
			newstyle.setFont(font16);
			newstyle.setAlignment(HorizontalAlignment.CENTER);// 左对齐 居中 右对齐
		}

		if (rowNum > 0 && rowNum < 6) {
			Font font10 = fontMap.get("normal");
			if (font10 == null) {
				font10 = wb.createFont();
				font10.setFontName("宋体");// 设置字体
				font10.setFontHeightInPoints((short) 10);// 设置字体大小
				font10.setBold(false);
				fontMap.put("normal", font10);
			}
			newstyle.setFont(font10);
			if (rowNum == 4) {
				newstyle.setAlignment(HorizontalAlignment.RIGHT);// 左对齐 居中 右对齐
			} else {
				newstyle.setAlignment(HorizontalAlignment.LEFT);// 左对齐 居中 右对齐
			}
		}

		// }

		// 样式
		toCell.setCellStyle(newstyle);
		if (fromCell.getCellComment() != null) {
			toCell.setCellComment(fromCell.getCellComment());
		}
		// 不同数据类型处理
		int fromCellType = fromCell.getCellType();
		toCell.setCellType(fromCellType);
		if (fromCellType == XSSFCell.CELL_TYPE_NUMERIC) {
			if (XSSFDateUtil.isCellDateFormatted(fromCell)) {
				toCell.setCellValue(fromCell.getDateCellValue());
			} else {
				toCell.setCellValue(fromCell.getNumericCellValue());
			}
		} else if (fromCellType == XSSFCell.CELL_TYPE_STRING) {
			// System.out.println(fromCell.getRichStringCellValue());
			toCell.setCellValue(fromCell.getRichStringCellValue());
		} else if (fromCellType == XSSFCell.CELL_TYPE_BLANK) {
		} else if (fromCellType == XSSFCell.CELL_TYPE_BOOLEAN) {
			toCell.setCellValue(fromCell.getBooleanCellValue());
		} else if (fromCellType == XSSFCell.CELL_TYPE_ERROR) {
			toCell.setCellErrorValue(fromCell.getErrorCellValue());
		} else if (fromCellType == XSSFCell.CELL_TYPE_FORMULA) {
			toCell.setCellFormula(fromCell.getCellFormula());
		} else {
		}

	}

	/**
	 * 行复制功能
	 * 
	 * @param wb
	 * @param oldRow
	 * @param toRow
	 * @param allRow
	 */
	@SuppressWarnings("rawtypes")
	public static void copyRow(XSSFWorkbook wb, XSSFRow oldRow, XSSFRow toRow, int allRow) {
		for (Iterator cellIt = oldRow.cellIterator(); cellIt.hasNext();) {
			XSSFCell tmpCell = (XSSFCell) cellIt.next();
			XSSFCell newCell = toRow.createCell(tmpCell.getColumnIndex());
	
			copyCell(wb, tmpCell, newCell, oldRow.getRowNum(), allRow);
		}
		if (oldRow.getRowNum() == 0) {
			toRow.setHeightInPoints(20);// 设置行高
		}
	}

	/**
	 * Sheet复制
	 * 
	 * @param wb
	 * @param fromSheet
	 * @param toSheet
	 */
	@SuppressWarnings("rawtypes")
	public static void copySheet(XSSFWorkbook wb, XSSFSheet fromSheet, XSSFSheet toSheet) {
		mergeSheetAllRegion(fromSheet, toSheet);
		// 设置列宽
		int cellNum = 0;
		toSheet.setColumnWidth(0, 2500);// 设置行宽
		toSheet.setColumnWidth(1, 7500);
		toSheet.setColumnWidth(2, 8000);
		toSheet.setColumnWidth(3 + cellNum, 2500);
		toSheet.setColumnWidth(4 + cellNum, 2500);
		toSheet.setColumnWidth(5 + cellNum, 2500);
		toSheet.setColumnWidth(6 + cellNum, 3500);
		toSheet.setColumnWidth(7 + cellNum, 3500);
		toSheet.setColumnWidth(8 + cellNum, 2500);
		toSheet.setColumnWidth(9 + cellNum, 2500);

		for (Iterator rowIt = fromSheet.rowIterator(); rowIt.hasNext();) {
			int allRow = fromSheet.getPhysicalNumberOfRows();
			XSSFRow oldRow = (XSSFRow) rowIt.next();
			XSSFRow newRow = toSheet.createRow(oldRow.getRowNum());
			copyRow(wb, oldRow, newRow, allRow);
		}

	}

	public class XSSFDateUtil extends DateUtil {

	}
	
	
	public static void mergeExcelFiles(List<String> files, String destFileName, boolean replaceSheetName) {

		// 创建一个新的Excel文档
		Workbook newBook = new Workbook();
		// 清除默认的3张工作表
		newBook.getWorksheets().clear();

		// 创建另一个Excel文档
		Workbook tempBook = new Workbook();

		// 遍历数组，依次加载每个Excel文档并将文档中的所有工作表复制到新建的Excel文档中
		for (int i = 0; i < files.size(); i++) {
			String file = files.get(i);
			String fileId = new File(file).getName().replace(ToolConstants.XLSX, "");
			String applyId = fileId;
			String sheetName = applyId;
			tempBook.loadFromFile(file);
			WorksheetsCollection worksheets = tempBook.getWorksheets();
			for (int j = 0; j < worksheets.size(); j++) {
				Worksheet sheet = worksheets.get(j);
				newBook.getWorksheets().addCopy(sheet, WorksheetCopyType.CopyAll);
				if (replaceSheetName) {
					Worksheet worksheet = newBook.getWorksheets().get(i);
					worksheet.setName(sheetName);
					// worksheet.setTabColor(Color.CYAN);
				}
			}
		}
		// 保存
		newBook.saveToFile(destFileName, ExcelVersion.Version2013);
	}	

}
