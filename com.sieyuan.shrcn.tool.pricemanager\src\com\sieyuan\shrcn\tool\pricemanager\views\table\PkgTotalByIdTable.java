package com.sieyuan.shrcn.tool.pricemanager.views.table;

import org.eclipse.swt.widgets.Composite;

import com.shrcn.found.ui.model.TableConfig;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.table.XOTable;
import com.shrcn.found.ui.table.action.TableAction;
import com.shrcn.found.ui.util.ImageConstants;
import com.shrcn.found.ui.util.ImgDescManager;

public class PkgTotalByIdTable extends RKTable {

    class ExportAction extends TableAction {

        public ExportAction(XOTable table) {
            super(table);
            setText("导出(&E)");
            setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.EDIT_EXPORT));
        }

        @Override
        public void run() {
            table.exportExcel("报价信息");
        }
    }

    class PasteAction extends TableAction {

        public PasteAction(XOTable table) {
            super(table);
            // setText("粘贴(&P)");
            // setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
        }

        @Override
        public void run() {
            // String clipBoardContent = SwtUtil.getClipBoardContent();
            // if (StringUtil.isEmpty(clipBoardContent)) {
            // DialogHelper.showWarning("复制参数为空！");
            // return;
            // }
            // String[] str = clipBoardContent.split("\\r\\n");
            // List<PkgTotal> input = (List<PkgTotal>) table.getInput();
            // if (input.size() != str.length) {
            // DialogHelper.showWarning("复制条数与表格内条数不一致！");
            // return;
            // }
            // for (int i = 0; i < input.size(); i++) {
            // PkgTotal pkgTotal = input.get(i);
            // pkgTotal.setTargetprice(str[i].trim());
            // }
            // table.setInput(input);
            // table.refresh();
        }
    }

    public PkgTotalByIdTable(Composite parent, TableConfig config) {
        super(parent, config);
    }

    @Override
    public int handleDelete() {
        int rows = super.handleDelete();
        if (getInput() != null && getInput().size() > 0) {
            // List<PkgTotal> pkgTotals = (List<PkgTotal>) getInput();
            // PersisitUtil.savePkgs(pkgTotals);
            // PriceBase db = PersisitBaseUtil.getDBBase();
            // Map<String, Map<String, PkgInfo>> newTotalBase = new HashMap<>();
            // Map<String, Map<String, Product>> newPriceTotalBase = new HashMap<>();
            // Map<String, Map<String, String>> newLimitTotalBase = new HashMap<>();
            // Map<String, Map<String, PkgInfo>> totalBase = db.getTotalBase();
            // Map<String, Map<String, Product>> priceTotalBase = db.getPriceTotalBase();
            // Map<String, Map<String, String>> limitTotalBase = db.getLimitTotalBase();
            // for (PkgTotal pkgTotal : pkgTotals) {
            // String pkgName = pkgTotal.getPkgname();
            // newTotalBase.put(pkgName, totalBase.get(pkgName));
            // newPriceTotalBase.put(pkgName, priceTotalBase.get(pkgName));
            // newLimitTotalBase.put(pkgName, limitTotalBase.get(pkgName));
            // }
            // db.setTotalBase(newTotalBase);
            // db.setPriceTotalBase(newPriceTotalBase);
            // db.setLimitTotalBase(newLimitTotalBase);
            // PersisitBaseUtil.saveDBBase();
        } else {
            // PersisitUtil.reset();
            // PersisitBaseUtil.reset();
        }
        return rows;
    }

    @Override
    protected void initData() {}

    @Override
    protected void initOthers() {
        // actions.add(new DeleteAction(this));
        // actions.add(new PasteAction(this));
        actions.add(new ExportAction(this));
    }

}
