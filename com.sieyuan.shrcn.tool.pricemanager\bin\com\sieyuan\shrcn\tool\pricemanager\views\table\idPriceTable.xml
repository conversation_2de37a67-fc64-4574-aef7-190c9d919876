<?xml version="1.0" encoding="UTF-8"?>
<table name="IdPriceInfo" desc="ID报价" class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.model.IdPriceSetting" 
tableClass="com.shrcn.found.ui/com.shrcn.found.ui.table.RKTable" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
  <field fixed="true" desc="序号" editor="none" name="index" width="80" default=""/>  
  <field desc="选择" editor="checkbox" name="selected" width="100" default=""/>
  <field desc="ID" editor="none" name="bidNo" width="200" default=""/>
  <field desc="实际报价" editor="none" name="price" width="200" default=""/> 
  <field desc="设定报价" editor="text" name="settingPrice" width="200" default=""/> 
</table>