package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.SQLException;

import org.junit.Test;

public class SqliteHelperTest {

    @Test
    public void testHelper() {
        try {
            SqliteHelper h = new SqliteHelper("testHelper.db");

            h.executeUpdate("drop table if exists costprice;");
            h.executeUpdate("drop table if exists limitprice;");
            h.executeUpdate("drop table if exists pkginfo;");
            h.executeUpdate("drop table if exists product;");

            h.executeUpdate("CREATE TABLE 'product' (" + "'id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                + "'parentid'  INTEGER NOT NULL," + "'devname'  TEXT(100)," + "'devtype'  TEXT(100),"
                + "'unit'  TEXT(100) NOT NULL," + "'count'  INTEGER NOT NULL," + "'odevtype'  TEXT(100),"
                + "'ocunnt'  INTEGER NOT NULL," + "'supply'  TEXT(100)," + "'area'  TEXT(100),"
                + "'searchdevtype'  TEXT(100)," + "'costprice'  TEXT(100)," + "'price' TEXT(100)" + ");");

            h.executeUpdate("CREATE TABLE 'pkginfo' (" + "'id' INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                + "'name' TEXT (100) NOT NULL," + "'project_owner' TEXT (100) NOT NULL,"
                + "'project_name' TEXT (100) NOT NULL," + "'product' TEXT (100) NOT NULL,"
                + "'product_desc' TEXT (100)," + "'unit' TEXT (100) NOT NULL," + "'count' INTEGER NOT NULL,"
                + "'intelligence' TEXT (100) NOT NULL," + "'voltage_grade' TEXT (100)," + "'bidno' TEXT (100));");

            h.executeUpdate("CREATE TABLE 'limitprice' (" + "'id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                + "'product'  TEXT(100) NOT NULL," + "'count'  INTEGER NOT NULL," + "'unit'  TEXT(100) NOT NULL,"
                + "'price'  TEXT(100) NOT NULL);");

            h.executeUpdate("CREATE TABLE 'costprice' ('id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL," +
            		"'devtype'  TEXT(100) NOT NULL," +
            		"'type'  INTEGER NOT NULL,'price'  TEXT(100) NOT NULL);");

            // h.executeUpdate("insert into test values('sqliteHelper test');");
            // List<String> sList = h.executeQuery("select name from test", new RowMapper<String>() {
            // @Override
            // public String mapRow(ResultSet rs, int index)
            // throws SQLException {
            // return rs.getString("name");
            // }
            // });
            // System.out.println(sList.get(0));
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
