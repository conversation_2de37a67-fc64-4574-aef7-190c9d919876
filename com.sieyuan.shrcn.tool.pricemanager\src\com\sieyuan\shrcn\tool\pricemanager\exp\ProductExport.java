package com.sieyuan.shrcn.tool.pricemanager.exp;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dialog.OptionConfigDialog;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.ExportInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.utils.CalcUtil;
import com.sieyuan.shrcn.tool.pricemanager.utils.SXSSFWorkbookUtil;

public class ProductExport {
	
	// 按照模板导出
	public static void exportComplex(String fileName, PkgInfo pkgInfo, String curDate, String zbId, Map<Integer, List<PkgProduct>> pkgProductMaps, Integer type) {

		// 默认行报价比例系数为1
		String rate = "1";
		String priceRate = pkgInfo.getPricerate(); // 基准价
		if (!StringUtils.isEmpty(priceRate)) {
			rate = String.valueOf(CalcUtil.divide(priceRate, pkgInfo.getPrice()));
		} else {
			priceRate = pkgInfo.getPrice();
		}

		List<ExportInfo> list = new ArrayList<ExportInfo>();

		String priceValue = CalcUtil.getBigDecimalStringDown(new BigDecimal(priceRate));
		BigDecimal allPrice = CalcUtil.getBigDecimal();

		// 是否按照单价导出
		String infopath = OptionConfigDialog.class.getName();
		String singleValue = UIPreferences.newInstance().getInfo(infopath + ".single");
		Boolean exportSingle = false;
		if (!StringUtils.isEmpty(singleValue)) {
			exportSingle = Boolean.valueOf(singleValue);
		}

		if ((type == 1 || type == 2) && exportSingle) {
			rate = "1";
			priceRate = pkgInfo.getPrice();
		}

		List<PkgProduct> prsList = pkgProductMaps.get(pkgInfo.getId());
		for (PkgProduct product : prsList) {
			ExportInfo exportInfo = new ExportInfo();
			exportInfo.setId(product.getNumber());
			exportInfo.setName(product.getDevname());
			exportInfo.setArea(product.getArea());
			exportInfo.setFactory(product.getSupply());
			exportInfo.setUnit(product.getUnit());
			exportInfo.setDesc("");
			if (type == 3 || exportSingle) {
				String count = "";
				if (StringUtils.isEmpty(product.getOcunnt()) || (!NumberUtils.isNumber(product.getOcunnt())) || product.getOdevtype().contains("运输培训设计联络现场服务等") || product.getOcunnt().trim().equals("0")) {
					count = product.getOcunnt();
					exportInfo.setCount(count);
				} else {
					count = CalcUtil.getBigDecimalStringDown(CalcUtil.divide(product.getOcunnt(), pkgInfo.getCount()));
					exportInfo.setCount(count);
				}
				String perPrice = "";
				BigDecimal price = CalcUtil.getBigDecimal();
				if (product.getOdevtype().contains("运输培训设计联络现场服务等")) {
					price = CalcUtil.getBigDecimal(new BigDecimal(priceRate).subtract(allPrice));
					perPrice = String.valueOf(price);
					exportInfo.setPerprice(perPrice);
				} else {
					perPrice = CalcUtil.getPriceMulti(product.getPrice(), rate);
					exportInfo.setPerprice(perPrice);
				}
				if (StringUtils.isEmpty(product.getTotalprice()) || StringUtils.isEmpty(product.getOcunnt()) || (!NumberUtils.isNumber(product.getOcunnt())) || product.getOcunnt().trim().equals("0")) {
					exportInfo.setPrice(product.getTotalprice());
				} else {
					if (product.getOdevtype().contains("运输培训设计联络现场服务等")) {
						exportInfo.setPerprice(perPrice);
					} else {
						String elsePrice = CalcUtil.getPriceMulti(count, perPrice);
						allPrice = allPrice.add(new BigDecimal(elsePrice));
						exportInfo.setPerprice(elsePrice);
					}
				}
			} else {
				exportInfo.setCount(product.getOcunnt());
				exportInfo.setPerprice(product.getPrice());
				exportInfo.setPrice(product.getTotalprice());
			}

			list.add(exportInfo);
		}

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("zbId", zbId);
		map.put("pkgName", pkgInfo.getName());
		map.put("prjName", pkgInfo.getProjectName());

		if (type == 3 || exportSingle) {
			map.put("totalPrice", priceValue);
		} else {
			map.put("totalPrice", pkgInfo.getPrice());
		}

		if (type == 3 || exportSingle) {
			map.put("allPrice", priceValue);
		} else {
			// 单套分析表导出
			map.put("allPrice", pkgInfo.getTotalPrice());
		}

		List<ExportInfo> exportList = new ArrayList<ExportInfo>();
		for (ExportInfo exp : list) {
			if (exp.getName().contains("运输培训设计联络现场服务等")) {
				map.put("area", exp.getArea());
				map.put("factory", exp.getFactory());
				map.put("unit", exp.getUnit());
				map.put("count", exp.getCount());
				map.put("perprice", exp.getPerprice());
				map.put("price", exp.getPrice());
				map.put("desc", exp.getDesc());
				continue;
			} else if (exp.getName().contains("其他组件材料")) {
				continue;
			}
			exportList.add(exp);
		}
		map.put("sheetname", pkgInfo.getApplyId());
		ExcelWriter excelWriter = EasyExcel.write(fileName).withTemplate(DirManager.getPkgTemplateFile())
				.registerWriteHandler(new CustomTemplateSheetStrategy(0, pkgInfo.getApplyId())).autoCloseStream(Boolean.TRUE).build();
		WriteSheet writeSheet = EasyExcel.writerSheet().build();

		FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
		excelWriter.fill(exportList, fillConfig, writeSheet);
		excelWriter.fill(map, writeSheet);
		excelWriter.finish();

	}

	public static void export(String fileName, PkgInfo pkgInfo, String curDate, String zbId, boolean isCheck, Map<Integer, List<PkgProduct>> pkgProductMaps, Integer type) {

		// 默认行报价比例系数为1
		String rate = "1";
		String priceRate = pkgInfo.getPricerate(); // 基准价
		if (!StringUtils.isEmpty(priceRate)) {
			rate = String.valueOf(CalcUtil.divide(priceRate, pkgInfo.getPrice()));
		} else {
			priceRate = pkgInfo.getPrice();
		}

		String priceValue = CalcUtil.getBigDecimalStringDown(new BigDecimal(priceRate));
		BigDecimal allPrice = CalcUtil.getBigDecimal();

		// 是否按照单价导出
		String infopath = OptionConfigDialog.class.getName();
		String singleValue = UIPreferences.newInstance().getInfo(infopath + ".single");
		Boolean exportSingle = false;
		if (!StringUtils.isEmpty(singleValue)) {
			exportSingle = Boolean.valueOf(singleValue);
		}

		if ((type == 1 || type == 2) && exportSingle) {
			rate = "1";
			priceRate = pkgInfo.getPrice();
		}

		SXSSFWorkbook wb = new SXSSFWorkbook();
		int rowNum = 0;
		Sheet sheet;
		if (isCheck) {
			sheet = wb.createSheet("product-ck");
		} else {
			sheet = wb.createSheet("product");
		}
		int cellNum = 0;
		if (!isCheck) {
			cellNum = 1;
		}
		sheet.setColumnWidth(0, 2500);// 设置行宽
		if (!isCheck) {
			sheet.setColumnWidth(1, 7500);
			sheet.setColumnWidth(1 + cellNum, 8000);
		} else {
			sheet.setColumnWidth(1 + cellNum, 5200);
		}
		sheet.setColumnWidth(2 + cellNum, 2500);
		sheet.setColumnWidth(3 + cellNum, 2500);
		sheet.setColumnWidth(4 + cellNum, 2500);
		sheet.setColumnWidth(5 + cellNum, 2500);
		sheet.setColumnWidth(6 + cellNum, 3500);
		sheet.setColumnWidth(7 + cellNum, 3500);
		sheet.setColumnWidth(8 + cellNum, 2500);
		// 标题
		Row titleRow = sheet.createRow(rowNum);
		titleRow.setHeightInPoints(20);// 设置行高
		rowNum++;
		SXSSFWorkbookUtil.createCell(titleRow, 0, "货物清单单价分析表", wb, true, (short) 16);
		Row titleRow1 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(titleRow1, 0, "招标编号：" + zbId, wb, false, false, false, false, HorizontalAlignment.LEFT);
		Row titleRow2 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(titleRow2, 0, "包号：" + pkgInfo.getName(), wb, false, false, false, false, HorizontalAlignment.LEFT);
		Row titleRow3 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(titleRow3, 0, "项目名称：" + (pkgInfo.getProjectName().equals(ToolConstants.DEFAULT_PRJ) ? "" : pkgInfo.getProjectName()), wb, false, false, false, false, HorizontalAlignment.LEFT);
		Row titleRow4 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(titleRow4, 0, "金额单位：人民币万元", wb, false, false, false, false, HorizontalAlignment.RIGHT);

		rowNum++;// 空一行
		Row titleRow5 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(titleRow5, 0, "序号", wb, true, (short) 10);
		if (!isCheck) {
			SXSSFWorkbookUtil.createCell(titleRow5, cellNum, "", wb, true, (short) 10);
		}
		SXSSFWorkbookUtil.createCell(titleRow5, 1 + cellNum, "组件材料项目名称", wb, true, (short) 10);
		SXSSFWorkbookUtil.createCell(titleRow5, 2 + cellNum, "产地", wb, true, (short) 10);
		SXSSFWorkbookUtil.createCell(titleRow5, 3 + cellNum, "制造商", wb, true, (short) 10);
		SXSSFWorkbookUtil.createCell(titleRow5, 4 + cellNum, "单位", wb, true, (short) 10);
		SXSSFWorkbookUtil.createCell(titleRow5, 5 + cellNum, "数量", wb, true, (short) 10);
		SXSSFWorkbookUtil.createCell(titleRow5, 6 + cellNum, "单价", wb, true, (short) 10);
		SXSSFWorkbookUtil.createCell(titleRow5, 7 + cellNum, "合价", wb, true, (short) 10);
		SXSSFWorkbookUtil.createCell(titleRow5, 8 + cellNum, "备注", wb, true, (short) 10);

		// 合并单元格
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8 + cellNum));
		sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 8 + cellNum));
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 8 + cellNum));
		sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 8 + cellNum));
		sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 8 + cellNum));

		List<PkgProduct> prsList = pkgProductMaps.get(pkgInfo.getId());
		for (PkgProduct product : prsList) {
			Row row = sheet.createRow(rowNum);
			rowNum++;
			SXSSFWorkbookUtil.createCellNumberic(row, 0, product.getNumber(), wb, false, (short) 10);
			if (!isCheck) {
				SXSSFWorkbookUtil.createCell(row, cellNum, product.getDevname(), wb, false, (short) 10);
			}
			if (product.getOdevtype().contains("运输培训设计联络现场服务等")) {
				SXSSFWorkbookUtil.createCell(row, 1 + cellNum, "", wb, false, (short) 10);
			} else {
				SXSSFWorkbookUtil.createCell(row, 1 + cellNum, product.getOdevtype(), wb, false, (short) 10);
			}
			SXSSFWorkbookUtil.createCell(row, 2 + cellNum, product.getArea(), wb, false, (short) 10);
			SXSSFWorkbookUtil.createCell(row, 3 + cellNum, product.getSupply(), wb, false, (short) 10);
			SXSSFWorkbookUtil.createCell(row, 4 + cellNum, product.getUnit(), wb, false, (short) 10);

			if (type == 3 || exportSingle) {
				String count = "";
				if (StringUtils.isEmpty(product.getOcunnt()) || (!NumberUtils.isNumber(product.getOcunnt())) || product.getOdevtype().contains("运输培训设计联络现场服务等") || product.getOcunnt().trim().equals("0")) {
					count = product.getOcunnt();
					SXSSFWorkbookUtil.createCellNumberic(row, 5 + cellNum, count, wb, false, (short) 10);
				} else {
					count = CalcUtil.getBigDecimalStringDown(CalcUtil.divide(product.getOcunnt(), pkgInfo.getCount()));
					SXSSFWorkbookUtil.createCellNumberic(row, 5 + cellNum, count, wb, false, (short) 10);
				}
				String perPrice = "";
				BigDecimal price = CalcUtil.getBigDecimal();
				if (product.getOdevtype().contains("运输培训设计联络现场服务等")) {
					price = CalcUtil.getBigDecimal(new BigDecimal(priceRate).subtract(allPrice));
					perPrice = String.valueOf(price);
					SXSSFWorkbookUtil.createCellNumberic(row, 6 + cellNum, perPrice, wb, false, (short) 10);
				} else {
					perPrice = CalcUtil.getPriceMulti(product.getPrice(), rate);
					SXSSFWorkbookUtil.createCellNumberic(row, 6 + cellNum, perPrice, wb, false, (short) 10);
				}
				if (StringUtils.isEmpty(product.getTotalprice()) || StringUtils.isEmpty(product.getOcunnt()) || (!NumberUtils.isNumber(product.getOcunnt())) || product.getOcunnt().trim().equals("0")) {
					SXSSFWorkbookUtil.createCellNumberic(row, 7 + cellNum, product.getTotalprice(), wb, false, (short) 10);
				} else {
					if (product.getOdevtype().contains("运输培训设计联络现场服务等")) {
						SXSSFWorkbookUtil.createCellNumberic(row, 7 + cellNum, perPrice, wb, false, (short) 10);
					} else {
						String elsePrice = CalcUtil.getPriceMulti(count, perPrice);
						allPrice = allPrice.add(new BigDecimal(elsePrice));
						SXSSFWorkbookUtil.createCellNumberic(row, 7 + cellNum, elsePrice, wb, false, (short) 10);
					}
				}
			} else {
				SXSSFWorkbookUtil.createCellNumberic(row, 5 + cellNum, product.getOcunnt(), wb, false, (short) 10);
				SXSSFWorkbookUtil.createCellNumberic(row, 6 + cellNum, product.getPrice(), wb, false, (short) 10);
				SXSSFWorkbookUtil.createCellNumberic(row, 7 + cellNum, product.getTotalprice(), wb, false, (short) 10);
			}
			SXSSFWorkbookUtil.createCell(row, 8 + cellNum, "", wb, false, (short) 10);
		}
		Row downRow1 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(downRow1, 0, "", wb, false, (short) 10);
		if (!isCheck) {
			SXSSFWorkbookUtil.createCell(downRow1, cellNum, "合计含税单价", wb, false, (short) 10);
			SXSSFWorkbookUtil.createCell(downRow1, 1 + cellNum, "", wb, false, (short) 10);
		} else {
			SXSSFWorkbookUtil.createCell(downRow1, 1 + cellNum, "合计含税单价", wb, false, (short) 10);
		}
		if (type == 3 || exportSingle) {
			SXSSFWorkbookUtil.createCellNumberic(downRow1, 2 + cellNum, priceValue, wb, false, (short) 10);
		} else {
			SXSSFWorkbookUtil.createCellNumberic(downRow1, 2 + cellNum, pkgInfo.getPrice(), wb, false, (short) 10);
		}
		SXSSFWorkbookUtil.createCell(downRow1, 3 + cellNum, "", wb, false, (short) 10);
		SXSSFWorkbookUtil.createCell(downRow1, 4 + cellNum, "", wb, false, (short) 10);
		SXSSFWorkbookUtil.createCell(downRow1, 5 + cellNum, "", wb, false, (short) 10);
		SXSSFWorkbookUtil.createCell(downRow1, 6 + cellNum, "", wb, false, (short) 10);
		SXSSFWorkbookUtil.createCell(downRow1, 7 + cellNum, "", wb, true, false, true, false, HorizontalAlignment.CENTER);
		SXSSFWorkbookUtil.createCell(downRow1, 8 + cellNum, "万元", wb, true, false, true, true,  HorizontalAlignment.CENTER);
		if (!isCheck) {
			sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 1, 2));
		}
		sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 2 + cellNum, 7 + cellNum));

		Row downRow2 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(downRow2, 0, "", wb, false, (short) 10);

		if (!isCheck) {
			SXSSFWorkbookUtil.createCell(downRow2, cellNum, "总价", wb, false, (short) 10);
			SXSSFWorkbookUtil.createCell(downRow2, 1 + cellNum, "", wb, false, (short) 10);
		} else {
			SXSSFWorkbookUtil.createCell(downRow2, 1 + cellNum, "总价", wb, false, (short) 10);
		}
		if (type == 3 || exportSingle) {
			SXSSFWorkbookUtil.createCellNumberic(downRow2, 2 + cellNum, priceValue, wb, false, (short) 10);
		} else {
			// 单套分析表导出
			SXSSFWorkbookUtil.createCellNumberic(downRow2, 2 + cellNum, pkgInfo.getTotalPrice(), wb, false, (short) 10);
		}
		SXSSFWorkbookUtil.createCell(downRow2, 3 + cellNum, "", wb, false, (short) 10);
		SXSSFWorkbookUtil.createCell(downRow2, 4 + cellNum, "", wb, false, (short) 10);
		SXSSFWorkbookUtil.createCell(downRow2, 5 + cellNum, "", wb, false, (short) 10);
		SXSSFWorkbookUtil.createCell(downRow2, 6 + cellNum, "", wb, false, (short) 10);
		SXSSFWorkbookUtil.createCell(downRow2, 7 + cellNum, "", wb, true, false, true, false,  HorizontalAlignment.CENTER);
		SXSSFWorkbookUtil.createCell(downRow2, 8 + cellNum, "万元", wb, true, false, true, true,  HorizontalAlignment.CENTER);

		if (!isCheck) {
			sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 1, 2));
		}

		sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 2 + cellNum, 7 + cellNum));

		rowNum = rowNum + 2;
		Row downRow3 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(downRow3, 4 + cellNum, "投标人全称：（盖章）", wb, false, false, false, false, HorizontalAlignment.LEFT);
		sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 4 + cellNum, 7 + cellNum));

		Row downRow4 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(downRow4, 4 + cellNum, "法定代表人或其委托代理人（签名）", wb, false, false, false, false, HorizontalAlignment.LEFT);
		sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 4 + cellNum, 7 + cellNum));

		Row downRow5 = sheet.createRow(rowNum);
		rowNum++;
		SXSSFWorkbookUtil.createCell(downRow5, 4 + cellNum, curDate, wb, false, false, false, false, HorizontalAlignment.LEFT);
		sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 4 + cellNum, 7 + cellNum));

		FileOutputStream out = null;
		try {
			out = new FileOutputStream(fileName);
			wb.write(out);
		} catch (IOException e) {
			SCTLogger.error(e.getMessage());
		} finally {
			try {
				if (out != null)
					out.close();
			} catch (IOException e) {
				SCTLogger.error(e.getMessage());
			}
		}
	}

}
