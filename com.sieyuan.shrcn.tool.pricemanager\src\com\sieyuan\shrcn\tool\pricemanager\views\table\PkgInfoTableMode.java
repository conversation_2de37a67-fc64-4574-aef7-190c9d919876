package com.sieyuan.shrcn.tool.pricemanager.views.table;

import java.util.ArrayList;
import java.util.List;

import com.sieyuan.shr.u21.ui.table.TableModel;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.views.DetailView;

import de.kupzog.ktable.KTableCellEditor;
import de.kupzog.ktable.KTableCellRenderer;
import de.kupzog.ktable.SWTX;

public class PkgInfoTableMode extends TableModel {

    private List<Integer> noLimitPriceList;

    /**
     * Initialize the base implementation.
     */
    public PkgInfoTableMode() {
        super();
        noLimitPriceList = new ArrayList<>();
    }

    /**
     * 设置表单元格编辑器
     * 
     * @return
     */
    public KTableCellEditor doGetCellEditor(int col, int row) {
        return null;
    }

    public KTableCellRenderer doGetCellRenderer(int col, int row) {
        m_fixedRenderer.setAlignment(SWTX.ALIGN_HORIZONTAL_CENTER | SWTX.ALIGN_VERTICAL_CENTER);
        m_textRenderer.setAlignment(SWTX.ALIGN_HORIZONTAL_CENTER | SWTX.ALIGN_VERTICAL_CENTER);
        if (row % 2 == 0) {
            m_checkableRenderer.setDefaultBackground(DetailView.bgcolor1);
            m_textRenderer.setDefaultBackground(DetailView.bgcolor1);
        } else {
            m_checkableRenderer.setDefaultBackground(DetailView.bgcolor2);
            m_textRenderer.setDefaultBackground(DetailView.bgcolor2);
        }

        if (noLimitPriceList.contains(row) && (col == 12 || col == 13)) {
            m_textRenderer.setDefaultBackground(DetailView.bgcolor4);
            return m_textRenderer;
        }
        return isFixedCell(col, row) ? m_fixedRenderer : m_textRenderer;
    }

    /**
     * 取得表格列个数
     */
    public int doGetColumnCount() {
        return getHead().length;
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.shrcn.vdd.ui.table.model.TableModel#doGetContentAt(int, int)
     */
    public Object doGetContentAt(int col, int row) {
        // 第一行是标题行
        if (row == 0) {
            return getHead()[col];
        }
        PkgInfo item = (PkgInfo)items.get(row - 1);
        if (GlobalData.getInstance().getViewType() == 1) {
            switch (col) {
                case 0:
                    return row;
                case 1:
                    return item.getName();
                case 2:
                    return item.getProjectOwner();
                case 3:
                    return item.getProjectName();
                case 4:
                    return item.getProduct();
                case 5:
                    return item.getProductDesc();
                case 6:
                    return item.getUnit();
                case 7:
                    return item.getCount();
                case 8:
                    return item.getIntelligence() == null ? "" : item.getIntelligence();
                case 9:
                    return item.getVoltageGrade() == null ? "" : item.getVoltageGrade();
                case 10:
                    return item.getBidNo();
                case 11:
                    return item.getApplyId() == null ? "" : item.getApplyId();
                case 12:
                    return item.getLimitPrice() == null ? "" : item.getLimitPrice();
                case 13:
                    return item.getTotalLimitPrice() == null ? "" : item.getTotalLimitPrice();
                default:
                    return "";
            }
        } else {
            switch (col) {
                case 0:
                    return row;
                case 1:
                    return item.getBidNo();
                case 2:
                    return item.getName();
                case 3:
                    return item.getProjectOwner();
                case 4:
                    return item.getProjectName();
                case 5:
                    return item.getProduct();
                case 6:
                    return item.getProductDesc();
                case 7:
                    return item.getUnit();
                case 8:
                    return item.getCount();
                case 9:
                    return item.getIntelligence() == null ? "" : item.getIntelligence();
                case 10:
                    return item.getVoltageGrade() == null ? "" : item.getVoltageGrade();
                case 11:
                    return item.getApplyId() == null ? "" : item.getApplyId();
                case 12:
                    return item.getLimitPrice() == null ? "" : item.getLimitPrice();
                case 13:
                    return item.getTotalLimitPrice() == null ? "" : item.getTotalLimitPrice();
                case 14:
                    return item.getTotalTax() == null ? "" : item.getTotalTax();
                case 15:
                    return item.getPrice() == null ? "" : item.getPrice();
                case 16:
                    return item.getTotalPrice() == null ? "" : item.getTotalPrice();
                case 17:
                    return item.getAvg() == null ? "" : item.getAvg();
                case 18:
                    return item.getPavg() == null ? "" : item.getPavg();
                case 19:
                    return item.getValid() == null ? "" : item.getValid().equals("1") ? "是" : "否";
                default:
                    return "";
            }
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.shrcn.vdd.ui.table.model.TableModel#doSetContentAt(int, int,
     * java.lang.Object)
     */
    public void doSetContentAt(int col, int row, Object value) {
        if (row - 1 >= 0) {
            PkgInfo item = (PkgInfo)items.get(row - 1);
            if (value == null) {
                value = "";
            }
            if (GlobalData.getInstance().getViewType() == 1) {
                switch (col) {
                    case 1:
                        item.setName((String)value);
                        break;
                    case 2:
                        item.setProjectOwner((String)value);
                        break;
                    case 3:
                        item.setProjectName((String)value);
                        break;
                    case 4:
                        item.setProduct((String)value);
                        break;
                    case 5:
                        item.setProductDesc((String)value);
                        break;
                    case 6:
                        item.setUnit((String)value);
                        break;
                    case 7:
                        item.setCount((String)value);
                        break;
                    case 8:
                        item.setIntelligence((String)value);
                        break;
                    case 9:
                        item.setVoltageGrade((String)value);
                        break;
                    case 10:
                        item.setBidNo((String)value);
                        break;
                    case 11:
                        item.setApplyId((String)value);
                        break;
                    case 12:
                        item.setLimitPrice((String)value);
                        break;
                    case 13:
                        item.setTotalLimitPrice((String)value);
                        break;

                }
            } else {

                switch (col) {
                    case 1:
                        item.setBidNo((String)value);
                    case 2:
                        item.setName((String)value);
                        break;
                    case 3:
                        item.setProjectOwner((String)value);
                        break;
                    case 4:
                        item.setProjectName((String)value);
                        break;
                    case 5:
                        item.setProduct((String)value);
                        break;
                    case 6:
                        item.setProductDesc((String)value);
                        break;
                    case 7:
                        item.setUnit((String)value);
                        break;
                    case 8:
                        item.setCount((String)value);
                        break;
                    case 9:
                        item.setIntelligence((String)value);
                        break;
                    case 10:
                        item.setVoltageGrade((String)value);
                        break;
                    case 11:
                        item.setApplyId((String)value);
                        break;
                    case 12:
                        item.setLimitPrice((String)value);
                        break;
                    case 13:
                        item.setTotalLimitPrice((String)value);
                        break;
                    case 14:
                        item.setTotalTax((String)value);
                        break;
                    case 15:
                        item.setPrice((String)value);
                        break;
                    case 16:
                        item.setTotalPrice((String)value);
                        break;
                    case 17:
                        item.setAvg((String)value);
                        break;
                    case 18:
                        item.setPavg((String)value);
                        break;
                    case 19:
                        item.setIsValid((String)value);
                        break;
                }

            }
        }
    }

    public String[] getHead() {
        if (GlobalData.getInstance().getViewType() == 1) {
            return ITableMessage.PACKAGE_DATA_HEAD;
        } else {
            return ITableMessage.PACKAGE_DATA_ID_HEAD;
        }
    }

    public int[] getHeadWidth() {
        if (GlobalData.getInstance().getViewType() == 1) {
            return ITableMessage.PACKAGE_DATA_WIDTH;
        } else {
            return ITableMessage.PACKAGE_DATA_ID_WIDTH;
        }
    }

    /**
     * 得到初始化行宽度
     */
    public int getInitialColumnWidth(int col) {
        return (col < getHeadWidth().length) ? getHeadWidth()[col] : super.getInitialColumnWidth(col);
    }

    public List<Integer> getNoLimitPriceList() {
        return noLimitPriceList;
    }

    public void setNoLimitPriceList(List<Integer> noLimitPriceList) {
        this.noLimitPriceList = noLimitPriceList;
    }

}
