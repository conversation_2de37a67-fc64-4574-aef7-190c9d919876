package com.sieyuan.shrcn.tool.pricemanager.dialog;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.ui.app.WrappedTitleAreaDialog;
import com.shrcn.found.ui.table.RKTable;
import com.sieyuan.shrcn.tool.pricemanager.views.table.TableFactory;


/**
 * @Description:设置排序规则
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2022-11-17 上午11:11:12
 
 */
public class SettingOrderDialog extends WrappedTitleAreaDialog {
	
	
    private RKTable table;

	public SettingOrderDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == IDialogConstants.OK_ID) {
			return;
		}
		super.buttonPressed(buttonId);
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("设置调价顺序");
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, IDialogConstants.OK_LABEL, true);
		createButton(parent, IDialogConstants.CANCEL_ID, IDialogConstants.CANCEL_LABEL, false);
	}

	@SuppressWarnings("unused")
	@Override
	protected Control createDialogArea(Composite parent) {

		setTitle("设置调价顺序");
		setMessage("设置不包含独有ID的包调价顺序。");
		Composite container = new Composite(parent, SWT.FILL);
		container.setLayout(new GridLayout(1, false));
		container.setLayoutData(new GridData(GridData.FILL_BOTH));
		
		table = TableFactory.getPkgOrderTable(container);
		table.getTable().setLayoutData(new GridData(GridData.FILL_BOTH));
		GridData gridData = new GridData(GridData.FILL_BOTH);
		return container;
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(600, 450);
	}

}
