package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.dialog.ProductOutTemplateDialog;

/**
 * @Description:生成开标文件模板
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:40:18
 */
public class ProductTemplateAction extends MenuAction {

    public ProductTemplateAction(String text) {
        super(text);
		setAccelerator(SWT.CTRL + 'M');
    }

    @Override
    public void run() {
        ProductOutTemplateDialog expDlg = new ProductOutTemplateDialog(Display.getDefault().getActiveShell());
        expDlg.open();
    }

}
