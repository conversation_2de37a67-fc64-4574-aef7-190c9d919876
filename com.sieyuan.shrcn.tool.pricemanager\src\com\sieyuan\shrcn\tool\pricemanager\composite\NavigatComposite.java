package com.sieyuan.shrcn.tool.pricemanager.composite;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.viewers.TreeViewer;
import org.eclipse.swt.SWT;
import org.eclipse.swt.dnd.Clipboard;
import org.eclipse.swt.dnd.TextTransfer;
import org.eclipse.swt.dnd.Transfer;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.events.MouseAdapter;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Menu;
import org.eclipse.swt.widgets.MenuItem;
import org.eclipse.swt.widgets.Text;
import org.eclipse.swt.widgets.Tree;
import org.eclipse.swt.widgets.TreeItem;

import com.shrcn.found.common.event.EventManager;
import com.shrcn.found.ui.UIConstants;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.EventConstants;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgErrInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.ProjectDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.SqliteHelper;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.data.IconManager;
import com.sieyuan.shrcn.tool.pricemanager.dialog.CalImportDialog;
import com.sieyuan.shrcn.tool.pricemanager.dialog.CalOutputDialog;
import com.sieyuan.shrcn.tool.pricemanager.dialog.OneKeyProductOutputDialog;
import com.sieyuan.shrcn.tool.pricemanager.dialog.PkgProductOutputDialog;
import com.sieyuan.shrcn.tool.pricemanager.dialog.ProductOutputDialog;
import com.sieyuan.shrcn.tool.pricemanager.dialog.ProjectAdjustPriceDialog;
import com.sieyuan.shrcn.tool.pricemanager.dialog.ProjectAdjustPriceHsiDialog;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgErrInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.TreeData;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.views.DetailView;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;

/**
 * 
 * @Description:导航菜单
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-18 下午1:47:49
 
 */
public class NavigatComposite extends Composite {
    public static final Image idItemImg = IconManager.idItemImg;
    public static final Image nameItemImg = IconManager.nameItemImg;
    public static final Image oidItemImg = IconManager.oidItemImg;
    public static final Image onameItemImg = IconManager.onameItemImg;
    public static final Image productItemImg = IconManager.productItemImg;
    // 图片资源
    public static final Image projectImg = IconManager.projectImg;
    public static final Image workItemImg = IconManager.workItemImg;

    // 颜色样式等
    private Color bgcolor = new Color(this.getShell().getDisplay(), 255, 0, 0);
    private final Clipboard cb = new Clipboard(this.getShell().getDisplay());
    private Menu menu; // 菜单

    private Font newFont;
    private FontData newFontData;
    private TreeItem odbIdItem; // 技术id节点
    // perference和剪切板
    private UIPreferences perference = UIPreferences.newInstance();
    private Map<Integer, List<PkgErrInfo>> pkgErrInfoMap;
    private TreeItem pkgItem; // 包号节点

    private Map<String, List<PkgInfo>> pkgMapById;// 按照id
    private Map<String, List<PkgInfo>> pkgMapByIdAndName;// 按照包号
    private Map<String, List<PkgInfo>> pkgMapByName;// 按照名称
    private Map<String, List<PkgInfo>> pkgMapByProjectName;// 按照工程名称

    private Map<String, List<PkgInfo>> pkgMapByProjectNameAndProduct;// 按照物料
    private Tree tree; // 左树
    private TreeViewer treeViewer;// viewer
    private Text searchText; // 搜索框

    /**
     * Create the composite.
     * 
     * @param parent
     * @param style
     */
    public NavigatComposite(Composite parent, int style) {
        super(parent, SWT.NONE);
        setLayout(new GridLayout(1, false));
        
	    searchText = SwtUtil.createText(this, new GridData(SWT.FILL, SWT.FILL, true, false, 1, 1));
		searchText.setToolTipText(ToolConstants.SEARCH);
        
        treeViewer = new TreeViewer(this, SWT.BORDER | SWT.H_SCROLL | SWT.V_SCROLL);
        tree = treeViewer.getTree();
        // tree = new Tree(this, SWT.BORDER);
        tree.setBackground(UIConstants.WHITE);
        // 树点击事件
        tree.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseDown(MouseEvent e) {
                new Point(e.x, e.y);
                TreeItem[] selected = tree.getSelection();
                if (selected != null && selected.length > 0 && e.button == 3) {
                    treeRightClick(selected[0]);
                    treeLeftClick(selected[0]);
                } else {
                    if (selected != null && selected.length > 0 && e.button == 1) {
                        treeLeftClick(selected[0]);
                    }
                }
            }
        });
        tree.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
        // 修复初次启动时，无法显示右键菜单的错误
        menu = new Menu(tree);
        tree.setMenu(menu);
        
        addListeners();
        intNagat();
    }

    /**
     * 添加事件监听
     */
    private void addListeners() {
    	searchText.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				intNagat();
			}
		});
	}

	/**
     * 查询数据
     * @param pkgs
     * @param errList
	 * @param search 
     */
    private void buildMap(List<PkgInfo> pkgs, List<PkgErrInfo> errList, String search) {
        Comparator<String> comparator = new Comparator<String>() {
            public int compare(String obj1, String obj2) {
                // 升序排序
                return obj1.compareTo(obj2);
            }
        };
		Comparator<String> pkgComparator = new Comparator<String>() {
			public int compare(String obj1, String obj2) {
				Integer pkg1 = Integer.valueOf(obj1.replace("包", ""));
				Integer pkg2 = Integer.valueOf(obj2.replace("包", ""));
				// 升序排序
				return Integer.compare(pkg1, pkg2);
			}
		};
        pkgMapById = new TreeMap<>(comparator);
        pkgMapByIdAndName = new TreeMap<>(comparator);
        pkgMapByName = new TreeMap<>(pkgComparator);
        pkgMapByProjectName = new TreeMap<>(comparator);
        pkgMapByProjectNameAndProduct = new TreeMap<>(comparator);
        pkgErrInfoMap = new HashMap<>();
        List<PkgInfo> pkgInfosById;
        List<PkgInfo> pkgInfosByIdAndName;
        List<PkgInfo> pkgInfosByName;
        List<PkgInfo> pkgInfosByProjectName;
        List<PkgInfo> pkgInfosByProjectNameAndProduct;
        for (PkgInfo pkg : pkgs) {
            pkgInfosById = new ArrayList<>();
            pkgInfosByName = new ArrayList<>();
            String bidNo = pkg.getBidNo();
            String name = pkg.getName();
            
			if (!StringUtils.isEmpty(search)) {
				if (!bidNo.contains(search) && !name.contains(search)
						&& !pkg.getProjectOwner().contains(search)
						&& !pkg.getProjectName().contains(search)
						&& !pkg.getApplyId().contains(search)
						&& !pkg.getProduct().contains(search)) {
					continue;
				}
			}
            
            if (pkgMapById.containsKey(bidNo)) {
                pkgInfosById = pkgMapById.get(bidNo);
                if (pkgInfosById == null) {
                    pkgInfosById = new ArrayList<>();
                }
            }
            pkgInfosById.add(pkg);

            pkgMapById.put(bidNo, pkgInfosById);

            if (pkgMapByName.containsKey(name)) {
                pkgInfosByName = pkgMapByName.get(name);
                if (pkgInfosByName == null) {
                    pkgInfosByName = new ArrayList<>();
                }
            }
            pkgInfosByName.add(pkg);
            pkgMapByName.put(name, pkgInfosByName);

            pkgInfosByProjectName = new ArrayList<>();
            String productNameKey = name + ToolConstants.CONNECT + pkg.getProjectName();
            if (pkgMapByProjectName.containsKey(productNameKey)) {
                pkgInfosByProjectName = pkgMapByProjectName.get(productNameKey);
                if (pkgInfosByProjectName == null) {
                    pkgInfosByProjectName = new ArrayList<>();
                }

            }
            pkgInfosByProjectName.add(pkg);
            pkgMapByProjectName.put(productNameKey, pkgInfosByProjectName);

            pkgInfosByProjectNameAndProduct = new ArrayList<>();
            String productNameAndProductKey =
                name + ToolConstants.CONNECT + pkg.getProjectName() + ToolConstants.CONNECT + pkg.getProduct() + "_"
                    + pkg.getCount() + "_" + pkg.getBidNo() + "_" + "Row" + pkg.getRowId();
            if (pkgMapByProjectNameAndProduct.containsKey(productNameAndProductKey)) {
                pkgInfosByProjectNameAndProduct = pkgMapByProjectNameAndProduct.get(productNameAndProductKey);
                if (pkgInfosByProjectNameAndProduct == null) {
                    pkgInfosByProjectNameAndProduct = new ArrayList<>();
                }
            }
            pkgInfosByProjectNameAndProduct.add(pkg);
            pkgMapByProjectNameAndProduct.put(productNameAndProductKey, pkgInfosByProjectNameAndProduct);

            pkgInfosByIdAndName = new ArrayList<>();
            String idAndNameKey = bidNo + ToolConstants.CONNECT + pkg.getName();
            if (pkgMapByIdAndName.containsKey(idAndNameKey)) {
                pkgInfosByIdAndName = pkgMapByIdAndName.get(idAndNameKey);
                if (pkgInfosByIdAndName == null) {
                    pkgInfosByIdAndName = new ArrayList<>();
                }
            }
            pkgInfosByIdAndName.add(pkg);
            pkgMapByIdAndName.put(idAndNameKey, pkgInfosByIdAndName);
        }
        List<PkgErrInfo> errListTmp = null;
        for (PkgErrInfo errinfo : errList) {
            errListTmp = new ArrayList<>();
            if (pkgErrInfoMap.containsKey(errinfo.getParentid())) {
                errListTmp = pkgErrInfoMap.get(errinfo.getParentid());
                if (errListTmp == null) {
                    errListTmp = new ArrayList<>();
                }
            }
            errListTmp.add(errinfo);
            pkgErrInfoMap.put(errinfo.getParentid(), errListTmp);
        }
    }

    private void buildWarnColr(final TreeItem productItem, Integer id) {
        if (pkgErrInfoMap.containsKey(id)) {
            changeColor(productItem);
            // 设置父节点颜色
            TreeItem projectItem = productItem.getParentItem();
            changeColor(projectItem);
            TreeItem nameItem = projectItem.getParentItem();
            changeColor(nameItem);
        }
    }

    private boolean changeColor(TreeItem treeItem) {
        newFontData.setStyle(SWT.BOLD);
        treeItem.setFont(newFont);
        treeItem.setForeground(bgcolor);
        return true;
    }

    /***
     * 添加项目
     * 
     * @param device
     */
    public void intNagat() {
        String dbname = perference.getInfo("com.shrcn.pricemangertool.curentprojectname");
        if (dbname == null || dbname.equals("")) {
            return;
        }
        SqliteHelper sqliteHelper = null;
        try {
            sqliteHelper = new SqliteHelper(DirManager.getProjectFile(dbname));
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        GlobalData.getInstance().setSqliteHelper(sqliteHelper);
        GlobalData.getInstance().setProjectName(dbname);
        String search = searchText.getText();
        refreshNagat(dbname, search);
    }

    /**
     * 刷新导航树
     * @param rootname
     */
    private void refreshNagat(String rootname, String search) {
        tree.removeAll();
        newFontData = tree.getFont().getFontData()[0];
        newFont = new Font(this.getShell().getDisplay(), newFontData);
        TreeData data = new TreeData();
        TreeItem toolItem = new TreeItem(tree, SWT.NONE);
        data = new TreeData();
        toolItem.setText(rootname);
        data.setType(0);
        toolItem.setData(data);
        toolItem.setImage(projectImg);
        tree.setSelection(toolItem);

        pkgItem = new TreeItem(toolItem, SWT.NONE);
        pkgItem.setText("包号");
        pkgItem.setImage(nameItemImg);
        data = new TreeData();
        data.setType(1);
        pkgItem.setData(data);

        odbIdItem = new TreeItem(toolItem, SWT.NONE);
        odbIdItem.setText("技术规范ID");
        odbIdItem.setImage(idItemImg);
        data = new TreeData();
        data.setType(2);
        odbIdItem.setData(data);

        toolItem.setExpanded(true);

        data = new TreeData();
        TreeItem pkgNameItem;
        TreeItem projectNameItem;
        TreeItem productItem;

        treeViewer.setAutoExpandLevel(4);

        List<PkgInfo> sList = PkgInfoDao.getPkgs();
        List<PkgErrInfo> errList = PkgErrInfoDao.getPkgErrInfo();
        buildMap(sList, errList, search);

        Set<String> names = pkgMapByName.keySet(); // 取出所有的key值
        for (String key : names) {
            pkgNameItem = new TreeItem(pkgItem, SWT.NONE);
            data = new TreeData();
            data.setType(3);
            data.setKey(key);
            pkgNameItem.setData(data);
            pkgNameItem.setImage(onameItemImg);
            Set<String> projectName = pkgMapByProjectName.keySet(); // 取出所有的key值
            for (String projectkey : projectName) {
                if (projectkey.startsWith(key + ToolConstants.CONNECT)) {
                    projectNameItem = new TreeItem(pkgNameItem, SWT.NONE);
                    projectNameItem.setExpanded(true);
                    projectNameItem.setText(projectkey.replace(key + ToolConstants.CONNECT, ""));
                    data = new TreeData();
                    data.setType(4);
                    data.setKey(projectkey);
                    projectNameItem.setData(data);
                    projectNameItem.setImage(workItemImg);
                    Set<String> projectNameAndProduct = pkgMapByProjectNameAndProduct.keySet(); // 取出所有的key值
                    for (String productkey : projectNameAndProduct) {
                        if (productkey.startsWith(projectkey + ToolConstants.CONNECT)) {
                            productItem = new TreeItem(projectNameItem, SWT.NONE);
                            productItem.setText(productkey.replace(projectkey + ToolConstants.CONNECT, ""));
                            data = new TreeData();
                            data.setType(7);
                            data.setKey(pkgMapByProjectNameAndProduct.get(productkey).get(0).getId().toString()
                                + ToolConstants.CONNECT
                                + pkgMapByProjectNameAndProduct.get(productkey).get(0).getBidNo().toString());
                            data.setData(pkgMapByProjectNameAndProduct.get(productkey).get(0));
                            productItem.setData(data);
                            productItem.setImage(productItemImg);
                            buildWarnColr(productItem, pkgMapByProjectNameAndProduct.get(productkey).get(0).getId());
                        }
                    }
                }
            }
            pkgNameItem.setText(key);
            // pkgNameItem.setExpanded(true);
        }
        pkgItem.setExpanded(true);

        TreeItem pkgIdItem;
        TreeItem pkgIdAndNameItem;
        Set<String> ids = pkgMapById.keySet(); // 取出所有的key值
        for (String key : ids) {
            pkgIdItem = new TreeItem(odbIdItem, SWT.NONE);
            pkgIdItem.setText(key);
            data = new TreeData();
            data.setType(5);
            data.setKey(key);
            pkgIdItem.setData(data);
            pkgIdItem.setImage(oidItemImg);
            Set<String> idAndName = pkgMapByIdAndName.keySet(); // 取出所有的key值
            for (String namekey : idAndName) {
                if (namekey.startsWith(key + ToolConstants.CONNECT)) {
                    pkgIdAndNameItem = new TreeItem(pkgIdItem, SWT.NONE);
                    pkgIdAndNameItem.setText(namekey.replace(key + ToolConstants.CONNECT, ""));
                    data = new TreeData();
                    data.setType(6);
                    data.setKey(namekey);
                    pkgIdAndNameItem.setData(data);
                    pkgIdAndNameItem.setImage(onameItemImg);
                    List<PkgInfo> bidPkgs = pkgMapByIdAndName.get(namekey);

                    if (bidPkgs != null && bidPkgs.size() > 0) {
                        if (bidPkgs.get(0).getValid() != null && bidPkgs.get(0).getValid().equals("2")) {
                            changeColor(pkgIdAndNameItem);
                            TreeItem projectItem = pkgIdAndNameItem.getParentItem();
                            changeColor(projectItem);
                        }
                    }

                }
            }
        }

        tree.redraw();
    }

    // 树左键处理
    private void treeLeftClick(TreeItem treeItem) {
        GlobalData.getInstance().setCurrentTreeItem(treeItem);
        TreeData treeData = (TreeData)treeItem.getData();
        if (treeData.getType() == 1 || treeData.getType() == 3 || treeData.getType() == 4) {
            GlobalData.getInstance().setViewType(1);
        } else if (treeData.getType() == 2 || treeData.getType() == 5 || treeData.getType() == 6) {
            GlobalData.getInstance().setViewType(2);
        }
        DetailView.changeComposite(treeData, treeItem);

    }

    // 树右键处理
    private void treeRightClick(TreeItem treeItem) {
        TreeItem temp = treeItem;
        final String name = temp.getText();
        TreeData data = (TreeData)temp.getData();
        if (data.getType() == 3) {
            menu = new Menu(tree);
            MenuItem deleteStation = new MenuItem(menu, SWT.NONE);
            deleteStation.setText("删除");
            deleteStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
					Boolean delete = DialogHelper.showConfirm("确认删除" + name + "数据吗？");
					if (delete) {
						PkgProductDao.deletePkg(name);
						PkgErrInfoDao.savePkgErrInfo();
						NavigatView.refreshTree();
					}
                }
            });
            MenuItem exportStation = new MenuItem(menu, SWT.NONE);
            exportStation.setText("导出开标文件");
            exportStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    ProductOutputDialog expDlg = new ProductOutputDialog(Display.getDefault().getActiveShell(), 2, name);
                    expDlg.open();
                }
            });
            tree.setMenu(menu);
            
    
            
        } else if (data.getType() == 0) {
            menu = new Menu(tree);
            MenuItem delStation = new MenuItem(menu, SWT.NONE);
            delStation.setText("删除工程");
            delStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    if (DialogHelper.showConfirm("确定删除所选工程？", "是", "否")) {
                        String projectName = perference.getInfo("com.shrcn.pricemangertool.curentprojectname");
                        ProjectDao.deleteProject(projectName);
                        perference.setInfo("com.shrcn.pricemangertool.curentprojectname", "");
                        tree.removeAll();
                        FieldUtils.delDir(new File(DirManager.getProjectDir(projectName)));
                    }
                }
            });
            MenuItem refreshStation = new MenuItem(menu, SWT.NONE);
            refreshStation.setText("刷新工程");
            refreshStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    intNagat();
                }
            });
//            MenuItem adjustAStation = new MenuItem(menu, SWT.NONE);
//            adjustAStation.setText("调价计算A");
//            adjustAStation.addSelectionListener(new SelectionAdapter() {
//                public void widgetSelected(SelectionEvent e) {
//                    ProjectAdjustPriceDialog expDlg =
//                        new ProjectAdjustPriceDialog(Display.getDefault().getActiveShell());
//                    expDlg.open();
//                }
//            });
//            MenuItem adjustBStation = new MenuItem(menu, SWT.NONE);
//            adjustBStation.setText("调价计算B");
//            adjustBStation.addSelectionListener(new SelectionAdapter() {
//                public void widgetSelected(SelectionEvent e) {
//                	NewProjectAdjustPriceDialog expDlg =
//                        new NewProjectAdjustPriceDialog(Display.getDefault().getActiveShell());
//                    expDlg.open();
//                }
//            });
            
            MenuItem adjustCStation = new MenuItem(menu, SWT.NONE);
            adjustCStation.setText("调价计算");
            adjustCStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                	ProjectAdjustPriceDialog expDlg =
                        new ProjectAdjustPriceDialog(Display.getDefault().getActiveShell());
                    expDlg.open();
                }
            });
			
			// 调价历史
            MenuItem adjustHis = new MenuItem(menu, SWT.NONE);
            adjustHis.setText("调价历史");
            adjustHis.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                	ProjectAdjustPriceHsiDialog expDlg =
                        new ProjectAdjustPriceHsiDialog(Display.getDefault().getActiveShell());
                    expDlg.open();
                }
            });
            MenuItem exportPriceStation = new MenuItem(menu, SWT.NONE);
            exportPriceStation.setText("导出计算表");
            exportPriceStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    CalOutputDialog expDlg = new CalOutputDialog(Display.getDefault().getActiveShell(), 1, name);
                    expDlg.open();
                }
            });
            MenuItem importPriceStation = new MenuItem(menu, SWT.NONE);
            importPriceStation.setText("导入计算表");
            importPriceStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    CalImportDialog expDlg = new CalImportDialog(Display.getDefault().getActiveShell(), true, null);
                    expDlg.open();
                }
            });

            MenuItem exportStation = new MenuItem(menu, SWT.NONE);
            exportStation.setText("导出开标文件");
            exportStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    ProductOutputDialog expDlg = new ProductOutputDialog(Display.getDefault().getActiveShell(), 1, "全部");
                    expDlg.open();
                }
            });
            
            MenuItem onekeyExport = new MenuItem(menu, SWT.NONE);
            onekeyExport.setText("一键导出开标");
            onekeyExport.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    OneKeyProductOutputDialog expDlg = new OneKeyProductOutputDialog(Display.getDefault().getActiveShell());
                    expDlg.open();
                }
            });
            
            MenuItem exportPrice = new MenuItem(menu, SWT.NONE);
            exportPrice.setText("导出组件材料表");
            exportPrice.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    PkgProductOutputDialog expDlg = new PkgProductOutputDialog(Display.getDefault().getActiveShell(), 1, name);
                    expDlg.open();
                }
            });
            
            tree.setMenu(menu);
        } else if (data.getType() == 4) {
            menu = new Menu(tree);
            MenuItem refresheStation = new MenuItem(menu, SWT.NONE);
            refresheStation.setText("复制名称");
            refresheStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    Object[] data = new Object[] {name};
                    Transfer[] types = new Transfer[] {TextTransfer.getInstance()};
                    cb.setContents(data, types);
                }
            });
            tree.setMenu(menu);
        } else if (data.getType() == 7) {
            menu = new Menu(tree);
            MenuItem importPriceStation = new MenuItem(menu, SWT.NONE);
            importPriceStation.setText("导入计算表");
            importPriceStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                	String[] nameArray = name.split("_");
                	Integer rowId = Integer.valueOf(nameArray[nameArray.length-1].replace("Row", ""));
                    CalImportDialog expDlg = new CalImportDialog(Display.getDefault().getActiveShell(), false, rowId);
                    expDlg.open();
                }
            });
            
            MenuItem exportPriceStation = new MenuItem(menu, SWT.NONE);
            exportPriceStation.setText("导出组件材料表");
            exportPriceStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
    				EventManager.getDefault().notify(EventConstants.EXPORTPRODUCT, null);
                }
            });
            
            MenuItem refresheStation = new MenuItem(menu, SWT.NONE);
            refresheStation.setText("复制ID");
            refresheStation.addSelectionListener(new SelectionAdapter() {
                public void widgetSelected(SelectionEvent e) {
                    String[] bidNo = name.split("_");
                    Object[] data = new Object[] {bidNo[2]};
                    Transfer[] types = new Transfer[] {TextTransfer.getInstance()};
                    cb.setContents(data, types);
                }
            });
            tree.setMenu(menu);
        } else {
            tree.setMenu(null);
        }
    }
}
