package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.jface.wizard.WizardDialog;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.wizard.ProjectWizard;

/**
 * @Description:新建工程界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:08:51
 */
public class NewProjectAction extends MenuAction {

    public NewProjectAction(String text) {
        super(text);
		setAccelerator(SWT.CTRL + 'N');
    }

    @Override
    public void run() {
        WizardDialog dialog = new WizardDialog(Display.getDefault().getActiveShell(), new ProjectWizard());
        dialog.setPageSize(480, 250); // dialog大小,-1是指让宽度自动调整
        dialog.open();
    }
}
