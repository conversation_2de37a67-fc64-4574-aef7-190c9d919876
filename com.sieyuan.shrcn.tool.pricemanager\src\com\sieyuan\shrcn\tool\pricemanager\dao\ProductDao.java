package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.Product;

/**
 * @Description:组件配置表
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:58:43
 
 */
public class ProductDao {

    public static void saveProductlist(List<Product> productlist) {

        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();

        for (Product product : productlist) {
            Integer orderid = product.getOrderid();
            String number = product.getNumber();
            String name = product.getName();
            String bidno = product.getBidno();
            String devname = product.getDevname();
            String devtype = product.getDevtype();
            String unit = product.getUnit();
            String count = product.getCount();
            String odevtype = product.getOdevtype();
            String ocunnt = product.getOcunnt();
            String supply = product.getSupply();
            String area = product.getArea();
            String quote = product.getQuote();
            Integer rowId = product.getRowId();

            String sql =
                "INSERT INTO product (orderid, number, name, bidno, devname, devtype, unit, count,odevtype, ocunnt,supply, area, quote,prowid) "
                    + "VALUES ("
                    + orderid
                    + ", "
                    + "'"
                    + number
                    + "'"
                    + ", "
                    + "'"
                    + name
                    + "'"
                    + ", "
                    + "'"
                    + bidno
                    + "'"
                    + ", "
                    + "'"
                    + devname
                    + "'"
                    + ", "
                    + "'"
                    + devtype
                    + "'"
                    + ", "
                    + "'"
                    + unit
                    + "'"
                    + ", "
                    + "'"
                    + count
                    + "'"
                    + ", "
                    + "'"
                    + odevtype
                    + "'"
                    + ", "
                    + "'"
                    + ocunnt
                    + "'"
                    + ", "
                    + "'"
                    + supply
                    + "'"
                    + ", "
                    + "'" + area + "'" + ", '" + quote + "'," + rowId + ")";
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
}
