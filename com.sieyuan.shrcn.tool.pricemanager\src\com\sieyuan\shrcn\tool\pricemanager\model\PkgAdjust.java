/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * @Description: 调价
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2022-11-18 上午8:26:23
 
 */
public class PkgAdjust {

    private Integer id; // 序号
    private String pkgname; // 包号
    private String rate; // 110KV折扣
    private String rate2; // 220KV折扣
    
    private String limitprice; // 国网限价
    private String targetprice; // 设定目标价
    
    private String realPrice; // 市场参考价
    private String ror; // 110KV微调系数
    private String ror2; // 220KV微调系数
    private Boolean hasOnlyId; // 是否包含独有ID
    private Boolean isPriority; // 是否有限调整包 
    
    private String result; // 调价结果
    
    private int seq;

    public PkgAdjust() {}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPkgname() {
		return pkgname;
	}

	public void setPkgname(String pkgname) {
		this.pkgname = pkgname;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}

	public String getLimitprice() {
		return limitprice;
	}

	public void setLimitprice(String limitprice) {
		this.limitprice = limitprice;
	}

	public String getTargetprice() {
		return targetprice;
	}

	public void setTargetprice(String targetprice) {
		this.targetprice = targetprice;
	}

	public String getRealPrice() {
		return realPrice;
	}

	public void setRealPrice(String realPrice) {
		this.realPrice = realPrice;
	}

	public Boolean getHasOnlyId() {
		return hasOnlyId;
	}

	public void setHasOnlyId(Boolean hasOnlyId) {
		this.hasOnlyId = hasOnlyId;
	}

	public Boolean getIsPriority() {
		return isPriority;
	}

	public void setIsPriority(Boolean isPriority) {
		this.isPriority = isPriority;
	}

	public String getRor() {
		return ror;
	}

	public void setRor(String ror) {
		this.ror = ror;
	}

	public String getRate2() {
		return rate2;
	}

	public void setRate2(String rate2) {
		this.rate2 = rate2;
	}

	public String getRor2() {
		return ror2;
	}

	public void setRor2(String ror2) {
		this.ror2 = ror2;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public int getSeq() {
		return seq;
	}

	public void setSeq(int seq) {
		this.seq = seq;
	}

	
}
