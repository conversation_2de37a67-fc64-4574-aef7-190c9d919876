package com.sieyuan.shrcn.tool.pricemanager.action;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.FileDialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgBidInfo;
import com.sieyuan.shrcn.tool.pricemanager.utils.ExcelExportUtil;

/**
 * @Description:ID统计
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON><PERSON><PERSON>
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:38:27
 
 */
public class AnalysisByIdAction extends MenuAction {

    public AnalysisByIdAction(String title) {
        super(title);
		setAccelerator(SWT.CTRL + 'I');
    }

    /**
     * 导出数据
     * @param path
     */
    private void export(String path) {
        List<PkgBidInfo> bidInfoList = PkgInfoDao.getPkgBidInfos();
        ExcelExportUtil.exportIDReport(path, bidInfoList);
    }

    @Override
    public void run() {
        final String path = FileDialogHelper.selectExcelFile(getShell());
        if (path != null && !"".equals(path)) {
            IRunnableWithProgress openProgress = new IRunnableWithProgress() {
                @Override
                public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
                    monitor.setTaskName("正在导出数据，请稍后......");
                    Display.getDefault().asyncExec(new Runnable() {
                        @Override
                        public void run() {
                            export(path);
                        }
                    });
                }
            };
            ProgressManager.execute(openProgress, false);
            DialogHelper.showAsynInformation("导出ID检查统计结果成功！");
        }
    }
}
