package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * BidEvaluationModel
 * <AUTHOR>
 *
 */
/**
 * 投标评估结果模型类
 */
public class BidEvaluationResult {

	private double bid; // 报价
	private double score; // 价格得分
	private String companyName; // 厂家索引
	private int rank; // 排名
	private double priceDiff; // 价格分差
	private double totalDiff; // 总分差
	private double techBonus; // 技术补分

	public BidEvaluationResult(double bid, double score, String companyName) {
		this.bid = bid;
		this.score = score;
		this.companyName = companyName;
	}

	public BidEvaluationResult(double bid, String companyName) {
		this.bid = bid;
		this.companyName = companyName;
	}

	public double getBid() {
		return bid;
	}

	public void setBid(double bid) {
		this.bid = bid;
	}

	public double getScore() {
		return score;
	}

	public void setScore(double score) {
		this.score = score;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

	public double getPriceDiff() {
		return priceDiff;
	}

	public void setPriceDiff(double priceDiff) {
		this.priceDiff = priceDiff;
	}

	public double getTotalDiff() {
		return totalDiff;
	}

	public void setTotalDiff(double totalDiff) {
		this.totalDiff = totalDiff;
	}

	public double getTechBonus() {
		return techBonus;
	}

	public void setTechBonus(double techBonus) {
		this.techBonus = techBonus;
	}

}
