Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: PriceManager
Bundle-SymbolicName: com.sieyuan.shrcn.tool.pricemanager;singleton:=true
Bundle-Version: 1.0.0.qualifier
Bundle-Activator: com.sieyuan.shrcn.tool.pricemanager.BundleActivatorImpl
Bundle-Vendor: SIEYUAN
Require-Bundle: org.eclipse.ui,
 org.eclipse.core.runtime,
 com.shrcn.found.ui;bundle-version="1.0.0",
 com.shrcn.found.common;bundle-version="1.0.0",
 com.sieyuan.shr.u21.ui;bundle-version="1.0.0",
 com.shrcn.found.library;bundle-version="1.0.0",
 com.shrcn.found.das;bundle-version="1.0.0"
Bundle-ActivationPolicy: lazy
Bundle-Localization: plugin
Export-Package: com.jacob.activeX,
 com.jacob.com,
 com.sieyuan.shrcn.tool.pricemanager,
 com.sieyuan.shrcn.tool.pricemanager.action,
 com.sieyuan.shrcn.tool.pricemanager.app,
 com.sieyuan.shrcn.tool.pricemanager.composite,
 com.sieyuan.shrcn.tool.pricemanager.dao,
 com.sieyuan.shrcn.tool.pricemanager.data,
 com.sieyuan.shrcn.tool.pricemanager.dialog,
 com.sieyuan.shrcn.tool.pricemanager.dir,
 com.sieyuan.shrcn.tool.pricemanager.exp,
 com.sieyuan.shrcn.tool.pricemanager.sax,
 com.sieyuan.shrcn.tool.pricemanager.utils,
 com.sieyuan.shrcn.tool.pricemanager.views,
 com.sieyuan.shrcn.tool.pricemanager.views.table,
 com.sieyuan.shrcn.tool.pricemanager.wizard,
 org.sqlite,
 org.sqlite.core,
 org.sqlite.date,
 org.sqlite.javax,
 org.sqlite.jdbc3,
 org.sqlite.jdbc4,
 org.sqlite.util
Bundle-ClassPath: .,
 lib/sqlite-jdbc-3.27.2.1.jar,
 lib/jacob.jar,
 lib/spire.xls.free-3.9.2.jar,
 lib/easyexcel-2.2.6.jar



