<?xml version="1.0" encoding="UTF-8"?>
<?pde version="3.5"?>

<product name="报价管理工具" uid="com.sieyuan.shrcn.tool.pricemanager.product" id="com.sieyuan.shrcn.tool.pricemanager.product" application="com.sieyuan.shrcn.tool.pricemanager.application" version="1.0.0.qualifier" useFeatures="false" includeLaunchers="true">

   <configIni use="default">
   </configIni>

   <launcherArgs>
      <programArgs>-clearPersistedState</programArgs>
      <vmArgs>-Xms512m -Xmx512m -Duser.language=zh -Ddebug=true</vmArgs>
      <vmArgsMac>-XstartOnFirstThread -Dorg.eclipse.swt.internal.carbon.smallFonts</vmArgsMac>
   </launcherArgs>

   <windowImages i16="icons/autoTest16.png" i32="icons/autoTest32.png"/>

   <splash
      location="com.sieyuan.shrcn.tool.pricemanager" />
   <launcher name="PriceManagerTool">
      <solaris/>
      <win useIco="true">
         <ico path="/com.sieyuan.shrcn.tool.pricemanager/pricemanager.ico"/>
         <bmp/>
      </win>
   </launcher>

   <vm>
   </vm>

   <plugins>
      <plugin id="com.ibm.icu"/>
      <plugin id="com.shrcn.found.common"/>
      <plugin id="com.shrcn.found.common.nl1" fragment="true"/>
      <plugin id="com.shrcn.found.das"/>
      <plugin id="com.shrcn.found.library"/>
      <plugin id="com.shrcn.found.ui"/>
      <plugin id="com.shrcn.found.ui.nl1" fragment="true"/>
      <plugin id="com.sieyuan.shr.u21.ui"/>
      <plugin id="com.sieyuan.shrcn.tool.pricemanager"/>
      <plugin id="org.eclipse.compare"/>
      <plugin id="org.eclipse.compare.core"/>
      <plugin id="org.eclipse.compare.core.nl_es" fragment="true"/>
      <plugin id="org.eclipse.compare.core.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.compare.core.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.compare.nl_es" fragment="true"/>
      <plugin id="org.eclipse.compare.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.compare.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.commands"/>
      <plugin id="org.eclipse.core.commands.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.commands.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.commands.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.contenttype"/>
      <plugin id="org.eclipse.core.contenttype.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.contenttype.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.contenttype.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.databinding"/>
      <plugin id="org.eclipse.core.databinding.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.databinding.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.databinding.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.databinding.observable"/>
      <plugin id="org.eclipse.core.databinding.observable.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.databinding.observable.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.databinding.observable.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.databinding.property"/>
      <plugin id="org.eclipse.core.databinding.property.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.databinding.property.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.databinding.property.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.expressions"/>
      <plugin id="org.eclipse.core.expressions.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.expressions.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.expressions.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.filebuffers"/>
      <plugin id="org.eclipse.core.filebuffers.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.filebuffers.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.filebuffers.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.filesystem"/>
      <plugin id="org.eclipse.core.filesystem.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.filesystem.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.filesystem.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.filesystem.win32.x86" fragment="true"/>
      <plugin id="org.eclipse.core.jobs"/>
      <plugin id="org.eclipse.core.jobs.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.jobs.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.jobs.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.resources"/>
      <plugin id="org.eclipse.core.resources.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.resources.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.resources.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.resources.win32.x86" fragment="true"/>
      <plugin id="org.eclipse.core.runtime"/>
      <plugin id="org.eclipse.core.runtime.compatibility.registry" fragment="true"/>
      <plugin id="org.eclipse.core.runtime.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.runtime.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.runtime.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.core.variables"/>
      <plugin id="org.eclipse.core.variables.nl_es" fragment="true"/>
      <plugin id="org.eclipse.core.variables.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.core.variables.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.draw2d"/>
      <plugin id="org.eclipse.equinox.app"/>
      <plugin id="org.eclipse.equinox.app.nl_es" fragment="true"/>
      <plugin id="org.eclipse.equinox.app.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.equinox.app.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.equinox.common"/>
      <plugin id="org.eclipse.equinox.common.nl_es" fragment="true"/>
      <plugin id="org.eclipse.equinox.common.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.equinox.common.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.equinox.p2.core"/>
      <plugin id="org.eclipse.equinox.p2.engine"/>
      <plugin id="org.eclipse.equinox.p2.metadata"/>
      <plugin id="org.eclipse.equinox.p2.metadata.repository"/>
      <plugin id="org.eclipse.equinox.p2.repository"/>
      <plugin id="org.eclipse.equinox.preferences"/>
      <plugin id="org.eclipse.equinox.preferences.nl_es" fragment="true"/>
      <plugin id="org.eclipse.equinox.preferences.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.equinox.preferences.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.equinox.registry"/>
      <plugin id="org.eclipse.equinox.registry.nl_es" fragment="true"/>
      <plugin id="org.eclipse.equinox.registry.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.equinox.registry.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.equinox.security"/>
      <plugin id="org.eclipse.equinox.security.nl_es" fragment="true"/>
      <plugin id="org.eclipse.equinox.security.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.equinox.security.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.equinox.security.win32.x86" fragment="true"/>
      <plugin id="org.eclipse.help"/>
      <plugin id="org.eclipse.help.nl_es" fragment="true"/>
      <plugin id="org.eclipse.help.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.help.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.jface"/>
      <plugin id="org.eclipse.jface.databinding"/>
      <plugin id="org.eclipse.jface.databinding.nl_es" fragment="true"/>
      <plugin id="org.eclipse.jface.databinding.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.jface.databinding.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.jface.nl_es" fragment="true"/>
      <plugin id="org.eclipse.jface.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.jface.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.jface.text"/>
      <plugin id="org.eclipse.jface.text.nl_es" fragment="true"/>
      <plugin id="org.eclipse.jface.text.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.jface.text.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.osgi"/>
      <plugin id="org.eclipse.osgi.nl_es" fragment="true"/>
      <plugin id="org.eclipse.osgi.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.osgi.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.swt"/>
      <plugin id="org.eclipse.swt.nl_es" fragment="true"/>
      <plugin id="org.eclipse.swt.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.swt.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.swt.win32.win32.x86" fragment="true"/>
      <plugin id="org.eclipse.text"/>
      <plugin id="org.eclipse.text.nl_es" fragment="true"/>
      <plugin id="org.eclipse.text.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.text.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.ui"/>
      <plugin id="org.eclipse.ui.console"/>
      <plugin id="org.eclipse.ui.console.nl_es" fragment="true"/>
      <plugin id="org.eclipse.ui.console.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.ui.console.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.ui.editors"/>
      <plugin id="org.eclipse.ui.editors.nl_es" fragment="true"/>
      <plugin id="org.eclipse.ui.editors.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.ui.editors.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.ui.forms"/>
      <plugin id="org.eclipse.ui.forms.nl_es" fragment="true"/>
      <plugin id="org.eclipse.ui.forms.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.ui.forms.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.ui.ide"/>
      <plugin id="org.eclipse.ui.ide.nl_es" fragment="true"/>
      <plugin id="org.eclipse.ui.ide.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.ui.ide.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.ui.nl_es" fragment="true"/>
      <plugin id="org.eclipse.ui.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.ui.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.ui.views"/>
      <plugin id="org.eclipse.ui.views.nl_es" fragment="true"/>
      <plugin id="org.eclipse.ui.views.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.ui.views.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.ui.win32" fragment="true"/>
      <plugin id="org.eclipse.ui.workbench"/>
      <plugin id="org.eclipse.ui.workbench.nl_es" fragment="true"/>
      <plugin id="org.eclipse.ui.workbench.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.ui.workbench.nl_zh" fragment="true"/>
      <plugin id="org.eclipse.ui.workbench.texteditor"/>
      <plugin id="org.eclipse.ui.workbench.texteditor.nl_es" fragment="true"/>
      <plugin id="org.eclipse.ui.workbench.texteditor.nl_ru" fragment="true"/>
      <plugin id="org.eclipse.ui.workbench.texteditor.nl_zh" fragment="true"/>
   </plugins>


</product>
