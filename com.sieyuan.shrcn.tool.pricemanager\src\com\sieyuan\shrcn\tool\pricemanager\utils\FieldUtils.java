package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.spire.ms.System.Collections.ArrayList;

public class FieldUtils {
    public static HashMap<String, Object> convertToMap(Object obj) throws Exception {
        HashMap<String, Object> map = new HashMap<String, Object>();
        Field[] fields = obj.getClass().getDeclaredFields();
        for (int i = 0, len = fields.length; i < len; i++) {
            String varName = fields[i].getName();
            boolean accessFlag = fields[i].isAccessible();
            fields[i].setAccessible(true);
            Object o = fields[i].get(obj);
            if (o != null)
                map.put(varName, o.toString());
            fields[i].setAccessible(accessFlag);
        }
        return map;
    }

    public static void copyDir(String sourcePath, String newPath) throws IOException {
        File file = new File(sourcePath);
        String[] filePath = file.list();

        if (!(new File(newPath)).exists()) {
            (new File(newPath)).mkdir();
        }
        for (int i = 0; i < filePath.length; i++) {
            if ((new File(sourcePath + File.separator + filePath[i])).isDirectory()) {
                copyDir(sourcePath + File.separator + filePath[i], newPath + File.separator + filePath[i]);
            }
            if (new File(sourcePath + File.separator + filePath[i]).isFile()) {
                copyFile(sourcePath + File.separator + filePath[i], newPath + File.separator + filePath[i]);
            }
        }
    }

    public static void copyFile(String oldPath, String newPath) throws IOException {
        File oldFile = new File(oldPath);
        File file = new File(newPath);
        FileInputStream in = new FileInputStream(oldFile);
        FileOutputStream out = new FileOutputStream(file);;
        byte[] buffer = new byte[2097152];
        int readByte = 0;
        while ((readByte = in.read(buffer)) != -1) {
            out.write(buffer, 0, readByte);
        }
        in.close();
        out.close();
    }

    public static void delDir(File file) {
        if (file.isDirectory()) {
            File zFiles[] = file.listFiles();
            for (File file2 : zFiles) {
                delDir(file2);
            }
            file.delete();
        } else {
            file.delete();
        }
    }

    public static List<String> getAllFilePaths(File filePath, List<String> filePaths) {
        File[] files = filePath.listFiles();
        if (files == null) {
            return filePaths;
        }
        for (File f : files) {
            if (f.isDirectory()) {
                filePaths.add(f.getPath());
                getAllFilePaths(f, filePaths);
            } else {
                filePaths.add(f.getPath());
            }
        }
        return filePaths;
    }

    public static String getFileId(String fileName) {
        // String content = "9906-500113832-00051 智能变电站监控系统,AC110kV";
        // 正则表达式，用于匹配非数字串，+号用于匹配出多个非数字串
        String regEx = "([0-9a-zA-Z]{4})-([0-9a-zA-Z]{9})-([0-9a-zA-Z]{5})";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(fileName);
        // 用定义好的正则表达式拆分字符串，把字符串中的数字留出来
        if (matcher.find()) {
            String matchId = matcher.group();
            return matchId;
        } else {
            return "";
        }
    }

    public static String getFileSize(File file) {
        String size = "";
        if (file.exists() && file.isFile()) {
            long fileS = file.length();
            DecimalFormat df = new DecimalFormat("#.00");
            if (fileS < 1024) {
                size = df.format((double)fileS) + "BT";
            } else if (fileS < 1048576) {
                size = df.format((double)fileS / 1024) + "KB";
            } else if (fileS < 1073741824) {
                size = df.format((double)fileS / 1048576) + "MB";
            } else {
                size = df.format((double)fileS / 1073741824) + "GB";
            }
        } else if (file.exists() && file.isDirectory()) {
            size = "";
        } else {
            size = "0BT";
        }
        return size;
    }

    // 截取数字
    public static String getNumbers(String content) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            return matcher.group(0);
        }
        return "";
    }

    public static String getPkgName(String fileName) {
        // String content = "D:\Eclipse_Indigo\eclipse\cfg\包应答\包12\(9906-500027186-00005 无功自动投切装置,AC35kV.docx";
        // 正则表达式，用于匹配非数字串，+号用于匹配出多个非数字串
        String regEx = "包([0-9]{1,3})";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(fileName);
        // 用定义好的正则表达式拆分字符串，把字符串中的数字留出来
        if (matcher.find()) {
            String matchId = matcher.group();
            return matchId;
        } else {
            return "";
        }
    }

    public static String getSubStr(String str, int num) {
        String result = "";
        int i = 0;
        while (i < num) {
            int lastFirst = str.lastIndexOf(File.separator);
            result = str.substring(lastFirst) + result;
            str = str.substring(0, lastFirst);
            i++;
        }
        return File.separator + result.substring(1);
    }

    public static void main(String[] args) {
        String content = "D:\\Eclipse_Indigo\\eclipse\\cfg\\包应答\\包12\\9906-500027186-00005 无功自动投切装置,AC35kV.docx";
        getPkgName(content);
    }	
    
    // 查询是否包含独有ID
    public static Map<String, Boolean> getBidCountMap() {
		List<PkgInfo> pkgs = PkgInfoDao.getPkgs();
		Map<String, Set<String>> map = new HashMap<>();
		for (PkgInfo pkgInfo : pkgs) {
			if (map.containsKey(pkgInfo.getName())) {
				Set<String> bidNos = map.get(pkgInfo.getName());
				bidNos.add(pkgInfo.getBidNo());
			} else {
				Set<String> bidNos = new HashSet<>();
				bidNos.add(pkgInfo.getBidNo());
				map.put(pkgInfo.getName(), bidNos);
			}
		}
		Set<String> onlyIds = new HashSet<>();
		Map<String, Integer> bidCountMap = new HashMap<>();
		for (Map.Entry<String, Set<String>> entry : map.entrySet()) {
			Set<String> set = entry.getValue();
			for (String bid : set) {
				if (bidCountMap.containsKey(bid)) {
					Integer num = bidCountMap.get(bid);
					num = num + 1;
					bidCountMap.put(bid, num);
					onlyIds.remove(bid);
				} else {
					bidCountMap.put(bid, 1);
					onlyIds.add(bid);
				}
			}
		}
		Map<String, Boolean> onlyIdMap = new HashMap<>();
		for (PkgInfo pkgInfo : pkgs) {
			if (onlyIds.contains(pkgInfo.getBidNo())) {
				onlyIdMap.put(pkgInfo.getName(), true);
				continue;
			}
		}

		return onlyIdMap;
	}
    
    // 查询是否包含独有ID
    @SuppressWarnings("unchecked")
	public static Set<String> getOnlyBidSet(List<String> pkgNames) {
		List<PkgInfo> pkgs = PkgInfoDao.getPkgs();
		List<PkgInfo> pkgList = new ArrayList();
		for(PkgInfo pkg : pkgs){
			if(pkgNames.contains(pkg.getName())){
				pkgList.add(pkg);
			}
		}
		Map<String, Set<String>> map = new HashMap<>();
		for (PkgInfo pkgInfo : pkgList) {
			if (map.containsKey(pkgInfo.getName())) {
				Set<String> bidNos = map.get(pkgInfo.getName());
				bidNos.add(pkgInfo.getBidNo());
			} else {
				Set<String> bidNos = new HashSet<>();
				bidNos.add(pkgInfo.getBidNo());
				map.put(pkgInfo.getName(), bidNos);
			}
		}
		Set<String> onlyIds = new HashSet<>();
		Map<String, Integer> bidCountMap = new HashMap<>();
		for (Map.Entry<String, Set<String>> entry : map.entrySet()) {
			Set<String> set = entry.getValue();
			for (String bid : set) {
				if (bidCountMap.containsKey(bid)) {
					Integer num = bidCountMap.get(bid);
					num = num + 1;
					bidCountMap.put(bid, num);
					onlyIds.remove(bid);
				} else {
					bidCountMap.put(bid, 1);
					onlyIds.add(bid);
				}
			}
		}

		return onlyIds;
	}
    
    // 查询是否包含独有ID
    public static Set<String> getOnlyBidSet() {
		List<PkgInfo> pkgs = PkgInfoDao.getPkgs();
		Map<String, Set<String>> map = new HashMap<>();
		for (PkgInfo pkgInfo : pkgs) {
			if (map.containsKey(pkgInfo.getName())) {
				Set<String> bidNos = map.get(pkgInfo.getName());
				bidNos.add(pkgInfo.getBidNo());
			} else {
				Set<String> bidNos = new HashSet<>();
				bidNos.add(pkgInfo.getBidNo());
				map.put(pkgInfo.getName(), bidNos);
			}
		}
		Set<String> onlyIds = new HashSet<>();
		Map<String, Integer> bidCountMap = new HashMap<>();
		for (Map.Entry<String, Set<String>> entry : map.entrySet()) {
			Set<String> set = entry.getValue();
			for (String bid : set) {
				if (bidCountMap.containsKey(bid)) {
					Integer num = bidCountMap.get(bid);
					num = num + 1;
					bidCountMap.put(bid, num);
					onlyIds.remove(bid);
				} else {
					bidCountMap.put(bid, 1);
					onlyIds.add(bid);
				}
			}
		}

		return onlyIds;
	}

}
