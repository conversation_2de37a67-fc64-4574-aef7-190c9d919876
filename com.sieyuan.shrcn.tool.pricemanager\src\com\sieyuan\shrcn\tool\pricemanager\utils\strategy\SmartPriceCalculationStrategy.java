package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.utils.CalcUtil;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;

/**
 * 智能价格计算策略
 * 优化了原有算法的逻辑，提高计算精度和性能
 */
public class SmartPriceCalculationStrategy implements PriceCalculationStrategy {

    @Override
    public PriceCalculationResult calculatePrices(List<PkgInfo> pkgList, 
                                                 PkgAdjust adjust, 
                                                 PriceConfiguration config, 
                                                 Map<String, BigDecimal> priceMap) {
        
        String pkgName = adjust.getPkgname();
        BigDecimal targetPrice = new BigDecimal(adjust.getTargetprice());
        
        // 获取独有ID集合
        List<String> pkgNames = new ArrayList<>();
        pkgNames.add(pkgName);
        Set<String> onlyBidSet = FieldUtils.getOnlyBidSet(pkgNames);
        
        // 初始化计算结果
        PriceCalculationResult result = new PriceCalculationResult();
        result.setTargetPrice(targetPrice);
        
        // 第一轮：初始价格分配
        BigDecimal totalInitialPrice = allocateInitialPrices(pkgList, adjust, config, priceMap, onlyBidSet, result);
        
        // 第二轮：价格调整
        adjustPricesToTarget(pkgList, targetPrice, totalInitialPrice, onlyBidSet, priceMap, result);
        
        // 第三轮：最终价格计算
        calculateFinalPrices(pkgList, adjust, config, result);
        
        return result;
    }
    
    /**
     * 初始价格分配
     */
    private BigDecimal allocateInitialPrices(List<PkgInfo> pkgList, 
                                           PkgAdjust adjust, 
                                           PriceConfiguration config, 
                                           Map<String, BigDecimal> priceMap, 
                                           Set<String> onlyBidSet,
                                           PriceCalculationResult result) {
        
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal rate = new BigDecimal(adjust.getRate()).divide(new BigDecimal(100));
        BigDecimal rate2 = new BigDecimal(adjust.getRate2()).divide(new BigDecimal(100));
        BigDecimal ror = new BigDecimal(adjust.getRor());
        BigDecimal ror2 = new BigDecimal(adjust.getRor2());
        
        for (PkgInfo pkgInfo : pkgList) {
            BigDecimal initialPrice = calculateInitialPrice(pkgInfo, adjust, config, priceMap, 
                                                          onlyBidSet, rate, rate2, ror, ror2);
            
            pkgInfo.setTargetPrice(initialPrice.toString());
            totalPrice = totalPrice.add(initialPrice);
            
            result.addPackagePrice(pkgInfo.getBidNo(), initialPrice);
        }
        
        return totalPrice;
    }
    
    /**
     * 计算初始价格
     */
    private BigDecimal calculateInitialPrice(PkgInfo pkgInfo, 
                                           PkgAdjust adjust, 
                                           PriceConfiguration config, 
                                           Map<String, BigDecimal> priceMap, 
                                           Set<String> onlyBidSet,
                                           BigDecimal rate, 
                                           BigDecimal rate2, 
                                           BigDecimal ror, 
                                           BigDecimal ror2) {
        
        // 已确定价格的ID
        if (priceMap.containsKey(pkgInfo.getBidNo())) {
            return priceMap.get(pkgInfo.getBidNo()).multiply(new BigDecimal(pkgInfo.getCount()));
        }
        
        // 独有ID处理
        if (onlyBidSet.contains(pkgInfo.getBidNo())) {
            if (StringUtil.isEmpty(pkgInfo.getTotalLimitPrice()) || pkgInfo.getTotalLimitPrice().equals("0")) {
                BigDecimal onlyIdRate = new BigDecimal(config.getOnlyIdRate());
                return new BigDecimal(pkgInfo.getRealPrice()).multiply(onlyIdRate);
            }
        }
        
        // 普通ID处理
        if (StringUtil.isEmpty(pkgInfo.getTotalLimitPrice()) || pkgInfo.getTotalLimitPrice().equals("0")) {
            return new BigDecimal(pkgInfo.getRealPrice()).multiply(getPkgRor(pkgInfo, ror, ror2));
        } else {
            return new BigDecimal(pkgInfo.getTotalLimitPrice()).multiply(getPkgRate(pkgInfo, rate, rate2));
        }
    }
    
    /**
     * 调整价格到目标价格
     */
    private void adjustPricesToTarget(List<PkgInfo> pkgList, 
                                    BigDecimal targetPrice, 
                                    BigDecimal totalInitialPrice, 
                                    Set<String> onlyBidSet,
                                    Map<String, BigDecimal> priceMap,
                                    PriceCalculationResult result) {
        
        BigDecimal priceDifference = targetPrice.subtract(totalInitialPrice);
        
        if (priceDifference.compareTo(BigDecimal.ZERO) == 0) {
            return; // 无需调整
        }
        
        // 计算调整系数
        BigDecimal adjustmentRatio = calculateAdjustmentRatio(priceDifference, totalInitialPrice, onlyBidSet, pkgList, priceMap);
        
        // 应用调整
        for (PkgInfo pkgInfo : pkgList) {
            if (shouldAdjustPrice(pkgInfo, onlyBidSet, priceMap)) {
                BigDecimal adjustedPrice = new BigDecimal(pkgInfo.getTargetPrice())
                    .multiply(BigDecimal.ONE.add(adjustmentRatio));
                pkgInfo.setTargetPrice(adjustedPrice.toString());
                result.updatePackagePrice(pkgInfo.getBidNo(), adjustedPrice);
            }
        }
    }
    
    /**
     * 计算调整系数
     */
    private BigDecimal calculateAdjustmentRatio(BigDecimal priceDifference, 
                                              BigDecimal totalInitialPrice, 
                                              Set<String> onlyBidSet,
                                              List<PkgInfo> pkgList,
                                              Map<String, BigDecimal> priceMap) {
        
        BigDecimal adjustablePrice = BigDecimal.ZERO;
        
        for (PkgInfo pkgInfo : pkgList) {
            if (shouldAdjustPrice(pkgInfo, onlyBidSet, priceMap)) {
                adjustablePrice = adjustablePrice.add(new BigDecimal(pkgInfo.getTargetPrice()));
            }
        }
        
        if (adjustablePrice.compareTo(BigDecimal.ZERO) == 0) {
            // 如果没有可调整的价格，按比例分配给所有价格
            return priceDifference.divide(totalInitialPrice, 8, BigDecimal.ROUND_HALF_UP);
        }
        
        return priceDifference.divide(adjustablePrice, 8, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 判断是否应该调整价格
     */
    private boolean shouldAdjustPrice(PkgInfo pkgInfo, Set<String> onlyBidSet, Map<String, BigDecimal> priceMap) {
        // 未确定价格的独有ID
        return !priceMap.containsKey(pkgInfo.getBidNo()) && 
               onlyBidSet.contains(pkgInfo.getBidNo()) &&
               (StringUtil.isEmpty(pkgInfo.getTotalLimitPrice()) || pkgInfo.getTotalLimitPrice().equals("0"));
    }
    
    /**
     * 计算最终价格
     */
    private void calculateFinalPrices(List<PkgInfo> pkgList, 
                                    PkgAdjust adjust, 
                                    PriceConfiguration config,
                                    PriceCalculationResult result) {
        
        for (PkgInfo pkgInfo : pkgList) {
            BigDecimal finalPrice = new BigDecimal(pkgInfo.getTargetPrice());
            
            // 计算含税价格
            BigDecimal taxRate = CalcUtil.getTaxRate();
            BigDecimal withoutTaxPrice = finalPrice.divide(taxRate, 6, BigDecimal.ROUND_HALF_UP);
            BigDecimal withTaxPrice = withoutTaxPrice.multiply(taxRate);
            
            pkgInfo.setWithoutTaxPrice(withoutTaxPrice.toString());
            pkgInfo.setWithoutTotalPrice(withoutTaxPrice.multiply(new BigDecimal(pkgInfo.getCount())).toString());
            pkgInfo.setPrice(withTaxPrice.toString());
            pkgInfo.setTotalPrice(withTaxPrice.multiply(new BigDecimal(pkgInfo.getCount())).toString());
            
            result.addFinalPrice(pkgInfo.getBidNo(), finalPrice);
        }
    }
    
    /**
     * 获取包费率
     */
    private BigDecimal getPkgRate(PkgInfo pkgInfo, BigDecimal rate, BigDecimal rate2) {
        String desc = pkgInfo.getProductDesc();
        if (desc != null && (desc.contains("220") || desc.contains("330") || 
            desc.contains("500") || desc.contains("750"))) {
            return rate2;
        }
        return rate;
    }
    
    /**
     * 获取包微调系数
     */
    private BigDecimal getPkgRor(PkgInfo pkgInfo, BigDecimal ror, BigDecimal ror2) {
        String desc = pkgInfo.getProductDesc();
        if (desc != null && (desc.contains("220") || desc.contains("330") || 
            desc.contains("500") || desc.contains("750"))) {
            return ror2;
        }
        return ror;
    }
} 