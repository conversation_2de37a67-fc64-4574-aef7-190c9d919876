package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;
import com.sieyuan.shrcn.tool.pricemanager.model.PriceRate;

/**
 * 基础ID报价解析
 * 
 * <AUTHOR>
 * @version 1.0, 2020-12-2
 * 
 */
@SuppressWarnings("rawtypes")
public class PriceRateHandler extends SheetsHandler {

	private PriceRate priceRate;
	private List<PriceRate> priceRateList;

	public PriceRateHandler() {
		priceRateList = new ArrayList<>();
	}

	@Override
	public void cell(String cellReference, String formattedValue, XSSFComment comment) {
		super.cell(cellReference, formattedValue, comment);
		if (currentRow > 0 && !isEmpty(formattedValue)) {

			// 分包名称 基准物料名称 网省采购申请行号 分包编号 轮次 基准物料编码 技术规范书ID
			// 项目单位 附件名称 单位 扩展描述 包限价(万) 行限价(万) 数量 辅参ID 辅参数组合
			// 价格配置因子（%） 辅参数组合限价（万） 辅参未含税单价（万） 基准物料未含税单价（万）
			// 税率（%） 辅参含税单价（万） 基准物料含税单价（万） 基准物料未含税合价（万） 基准物料含税合价（万） 浮动比例

			if (currentCol == 0) {
				priceRate.setPkgName(formattedValue);
			} else if (currentCol == 1) {
				priceRate.setProductName(formattedValue);
			} else if (currentCol == 2) {
				priceRate.setOrderid(formattedValue);
			} else if (currentCol == 3) {
				priceRate.setPkgId(formattedValue);
			} else if (currentCol == 4) {
				priceRate.setRound(formattedValue);
			} else if (currentCol == 5) {
				priceRate.setCode(formattedValue);
			} else if (currentCol == 6) {
				priceRate.setPid(formattedValue);
			} else if (currentCol == 7) {
				priceRate.setCompany(formattedValue);
			} else if (currentCol == 8) {
				priceRate.setAttachment(formattedValue);
			} else if (currentCol == 9) {
				priceRate.setUnit(formattedValue);
			} else if (currentCol == 10) {
				priceRate.setExtendedDesc(formattedValue);
			} else if (currentCol == 11) {
				priceRate.setLimitPrice(formattedValue);
			} else if (currentCol == 12) {
				priceRate.setRowLimitPrice(formattedValue);
			} else if (currentCol == 13) {
				priceRate.setCount(formattedValue);
			} else if (currentCol == 14) {
				priceRate.setFid(formattedValue);
			} else if (currentCol == 15) {
				priceRate.setFunit(formattedValue);
			} else if (currentCol == 16) {
				priceRate.setRate(formattedValue);
			} else if (currentCol == 17) {
				priceRate.setfLimitPrice(formattedValue);
			} else if (currentCol == 18) {
				priceRate.setFprice(formattedValue);
			} else if (currentCol == 19) {
				priceRate.setBprice(formattedValue);
			} else if (currentCol == 20) {
				priceRate.setTaxrate(formattedValue);
			} else if (currentCol == 21) {
				priceRate.setfWithPrice(formattedValue);
			} else if (currentCol == 22) {
				priceRate.setbWithPrice(formattedValue);
			} else if (currentCol == 23) {
				priceRate.setbTotalPrice(formattedValue);
			} else if (currentCol == 24) {
				priceRate.setbTotalWithPrice(formattedValue);
			} else if (currentCol == 25) {
				priceRate.setFloatRate(formattedValue);
			}
		}
	}

	@Override
	public void endRow(int rowNum) {
		if (!StringUtils.isEmpty(priceRate.getPid())) {
			priceRateList.add(priceRate);
		}
	}

	@Override
	public void startRow(int rowNum) {
		super.startRow(rowNum);
		this.priceRate = new PriceRate();
	}

	public List<PriceRate> getPriceRateList() {
		return priceRateList;
	}

	public void setPriceRateList(List<PriceRate> priceRateList) {
		this.priceRateList = priceRateList;
	}

}
