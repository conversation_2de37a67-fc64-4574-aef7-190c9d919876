/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.TimeCounter;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.utils.ExcelExportUtil;

/**
 * @Description:计算表导出
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 
 */
public class CalOutputDialog extends WrappedDialog {

    private Combo combo;
    private Text diretoryRoot;
    private String name;
    private UIPreferences perference = UIPreferences.newInstance();
    private Map<String, List<PkgInfo>> pkgmap;
    private Map<Integer, List<PkgProduct>> prsmap;
    private Integer type;
    private List<String> warnings;
    private Button checkButton;

    private String DIR = ".outRoot";

    public CalOutputDialog(Shell parentShell, Integer type, String name) {
        super(parentShell);
        this.type = type;
        this.name = name;
        prsmap = new HashMap<>();
        pkgmap = new HashMap<>();
        warnings = new ArrayList<>();
        warnings = new ArrayList<>();
    }

    @Override
    protected void buttonPressed(int buttonId) {
        if (buttonId == OK) {
            String infopath = getClass().getName();
            final String root = diretoryRoot.getText();
            final Boolean checked = checkButton.getSelection();
            perference.setInfo(infopath + DIR, root);
            export(root, checked);
            if (warnings.size() > 0) {
                StringBuffer sb = new StringBuffer();
                for (String warning : warnings) {
                    sb.append(warning);
                }
                DialogHelper.showAsynWarning(sb.toString());
            }
        }
        super.buttonPressed(buttonId);
    }

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("导出计算表");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "确定", true);
        createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        Composite container = (Composite)super.createDialogArea(parent);
        container.setLayout(new GridLayout(3, false));

        combo = createSheetCombo(container, "包号：");
        diretoryRoot = SwtUtil.createDirectorySelector(container, "输出文件夹：", "*.docx");;
        GridData gdList = new GridData(GridData.FILL_HORIZONTAL);
        gdList.horizontalSpan = 3;
        
	    checkButton = SwtUtil.createButton(container, new GridData(
				SWT.LEFT, SWT.LEFT, false, false, 1, 1), SWT.CHECK, "是否按包区分");
	    checkButton.setSelection(false);

        init();
        return container;
    }

    private Combo createSheetCombo(Composite container, String title) {
        SwtUtil.createLabel(container, title, new GridData());
        Combo cb = SwtUtil.createCombo(container, new GridData());
        final GridData layoutData = new GridData();
        layoutData.horizontalSpan = 2;
        layoutData.widthHint = 80;
        cb.setLayoutData(layoutData);
        return cb;
    }

    /**
     * 导出计算表
     * @param path 路径 
     * @param checked 是否按照包区分
     */
	private void export(final String path, Boolean checked) {

		final List<PkgInfo> pkgs = PkgInfoDao.getPkgs();
		List<PkgInfo> pkglsit;
		for (PkgInfo pkgInfo : pkgs) {
			pkglsit = new ArrayList<>();
			if (pkgmap.containsKey(pkgInfo.getName())) {
				pkglsit = pkgmap.get(pkgInfo.getName());
				if (pkglsit == null) {
					pkglsit = new ArrayList<>();
				}
			}
			pkglsit.add(pkgInfo);
			pkgmap.put(pkgInfo.getName(), pkglsit);
		}

		List<PkgProduct> prs = PkgProductDao.getPkgProductByParentid(null);
		List<PkgProduct> prlsit;
		for (PkgProduct pkgProduct : prs) {
			prlsit = new ArrayList<>();
			if (prsmap.containsKey(pkgProduct.getParentid())) {
				prlsit = prsmap.get(pkgProduct.getParentid());
				if (prlsit == null) {
					prlsit = new ArrayList<>();
				}
			}
			prlsit.add(pkgProduct);
			prsmap.put(pkgProduct.getParentid(), prlsit);
		}

		if (!checked) {
			ProgressManager.execute(new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
					monitor.beginTask("正在导出数据中，请稍候...", pkgs.size());
					TimeCounter.begin();
					Set<String> pkgnames = pkgmap.keySet(); // 取出所有的key值
					List<String> bidNos = new ArrayList<>();
					int count = 0;
					for (String pkgname : pkgnames) {
						List<PkgInfo> list = pkgmap.get(pkgname);
						for (PkgInfo pkginfo : list) {
							if (!bidNos.contains(pkginfo.getBidNo())) {
								count++;
								exportCal(path, pkginfo, count, false);
								bidNos.add(pkginfo.getBidNo());
							}
							monitor.worked(1);
						}
					}
					TimeCounter.end("导出总耗时");
					ConsoleManager.getInstance().append("导出计算表完成！");
					monitor.done();
				}
			});
		} else {
			ProgressManager.execute(new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor)
						throws InvocationTargetException, InterruptedException {
					List<PkgInfo> allpkgs = PkgInfoDao.getPkgsWithLimitPrice(
							"", "", "");
					monitor.beginTask("正在导出数据中，请稍候...", allpkgs.size());
					TimeCounter.begin();
					int count = 0;
					for (PkgInfo pkginfo : allpkgs) {
						count++;
						exportCal(path + File.separator + pkginfo.getName(),
								pkginfo, count, true);
						monitor.worked(1);
					}
					TimeCounter.end("导出总耗时");
					ConsoleManager.getInstance().append("导出计算表完成！");
					monitor.done();
				}
			});
		}
	}

    private void exportCal(String path, PkgInfo pkgInfo, int count, Boolean isSingle) {
        List<PkgProduct> prs = prsmap.get(pkgInfo.getId());
		if (prs != null && prs.size() > 0) {
			String pkgPath = path + File.separator;
			if (!new File(pkgPath).exists()) {
				new File(pkgPath).mkdirs();
			}
			String product = pkgInfo.getProduct();
			Pattern pattern = Pattern.compile("[\\s\\\\/:\\?\\\"<>\\|]");
			Matcher matcher = pattern.matcher(product);
			if (matcher.find()) {
				warnings.add(pkgInfo.getName() + "_" + pkgInfo.getBidNo() + "_"
						+ product + "名称中包含特殊字符，生成计算表时已去掉，请检查！");
				product = matcher.replaceAll("");
			}
			if (isSingle) {
				ExcelExportUtil.exportCal(
						pkgPath + File.separator + pkgInfo.getBidNo() + "_"
								+ product + "_Row" + pkgInfo.getRowId()
								+ ".xlsx", prs, pkgInfo.getBidNo() + "_"
								+ product);
			} else {
				ExcelExportUtil.exportCal(
						pkgPath + File.separator + pkgInfo.getBidNo() + "_"
								+ product + ".xlsx", prs, pkgInfo.getBidNo()
								+ "_" + product);
			}
		}
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(600, 220);
    }

    private void init() {
        String infopath = getClass().getName();
        String diretoryPath = perference.getInfo(infopath + DIR);
        diretoryPath = DirManager.getOutputDir(GlobalData.getInstance().getProjectName()) + File.separator + "计算表";;
        diretoryRoot.setText(diretoryPath);
        loadPackages();
    }

    private void loadPackages() {
        if (type == 1) {
            List<String> names = new ArrayList<>();
            names.add("全部");
            combo.setItems(names.toArray(new String[names.size()]));
            combo.setText("全部");
        } else {
            List<String> names = new ArrayList<>();
            names.add(name);
            combo.setItems(names.toArray(new String[names.size()]));
            combo.setText(name);
        }
    }

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}
}
