/*
 * @(#) TemplatesComposite.java
 * 
 * Copyright (c) 2008 - 2012 上海思源弘瑞电力自动化有限公司. All rights reserved. 基于 Eclipse E4 a next generation platform (e.g., the
 * CSS styling, dependency injection, Modeled UI) Rich Client Application 开发的平台工具软件.
 */
package com.sieyuan.shrcn.tool.pricemanager.composite;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.MouseMoveListener;
import org.eclipse.swt.events.PaintEvent;
import org.eclipse.swt.events.PaintListener;
import org.eclipse.swt.graphics.Font;
import org.eclipse.swt.graphics.FontData;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.shrcn.found.ui.util.SwtUtil;
import com.sieyuan.shrcn.tool.pricemanager.data.IconManager;
import com.sieyuan.shrcn.tool.pricemanager.model.TreeData;
import com.sieyuan.shrcn.tool.pricemanager.views.DetailView;

/**
 * @Description:欢迎界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-30 下午5:08:01
 */

public class WelcomeComposite extends Composite {
    public static final Image priceWelcome = IconManager.priceWelcome;
    private TreeData treeData;

    public WelcomeComposite(CTabFolder parent, TreeData treeData, int none) {
        super(parent, SWT.NONE);
        this.treeData = treeData;
        createCompisiteArea(this);
    }

	private void createCompisiteArea(final Composite container) {
		container.setLayout(new GridLayout(6, false));

		final Composite canvas = new Composite(container, SWT.DOUBLE_BUFFERED);
		canvas.setLayout(new GridLayout(6, false));
		canvas.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 6, 1));

		canvas.setBackgroundImage(priceWelcome);
		int type = treeData.getType();
		if (type == 0) {
			GridData gd = new GridData(SWT.LEFT, SWT.LEFT, false, false, 1, 1);
			Label head = SwtUtil.createLabel(canvas, "报价管理工具", gd);
			FontData newFontData = head.getFont().getFontData()[0];
			newFontData.setStyle(SWT.BOLD);
			newFontData.setHeight(20);
			Font newFont = new Font(this.getShell().getDisplay(), newFontData);
			head.setFont(newFont);
			head.setForeground(DetailView.bgcolor2);// 设置字体颜色
		}

		final int imgWidth = priceWelcome.getBounds().width;
		final int imgHeight = priceWelcome.getBounds().height;

		canvas.addPaintListener(new PaintListener() {
			@Override
			public void paintControl(PaintEvent event) {
				event.gc.drawImage(priceWelcome, 0, 0, imgWidth, imgHeight, 0, 0, event.width, event.height);
			}
		});

		canvas.addMouseMoveListener(new MouseMoveListener() {

			@Override
			public void mouseMove(MouseEvent e) {
				canvas.redraw();
			}
		});

	}
}
