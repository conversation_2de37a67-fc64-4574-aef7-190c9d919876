package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.List;

import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;

/**
 * 单价分析汇总表
 * 
 * <AUTHOR>
 * @version 1.0, 2020-6-14
 * 
 */
@SuppressWarnings("rawtypes")
public class PkgPriceHandler extends SheetsHandler {

	private PkgInfo pkgInfo;

	// 所有的物料
	private List<PkgInfo> skgs;

	public PkgPriceHandler() {
		skgs = new ArrayList<>();
	}

	@Override
	public void cell(String cellReference, String formattedValue, XSSFComment comment) {
		super.cell(cellReference, formattedValue, comment);

		if (currentRow > 0 && !isEmpty(formattedValue)) {
			if (currentCol == 6) {
				pkgInfo.setWithoutTaxPrice(formattedValue);
			} else if (currentCol == 12) {
				pkgInfo.setApplyId(formattedValue);
			}
		}
	}

	@Override
	public void endRow(int rowNum) {
		if (pkgInfo.getApplyId() != null) {
			skgs.add(pkgInfo);
		}

	}

	public List<PkgInfo> getPkgs() {
		return skgs;
	}

	@Override
	public void startRow(int rowNum) {
		super.startRow(rowNum);
		this.pkgInfo = new PkgInfo();
		pkgInfo.setRowId(rowNum + 1);

	}
}
