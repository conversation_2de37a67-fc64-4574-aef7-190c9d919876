/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.TimeCounter;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.utils.ProductExportUtil;

/**
 * 导出开标结果
 * 
 * <AUTHOR>
 * 
 */
public class ProductOutputDialog extends WrappedDialog {

	private Combo combo;

	private String DIR = ".outRoot";
	private Text diretoryRoot;

	private String name; // 名称
	private UIPreferences perference = UIPreferences.newInstance();

	private Button selectButton;
	private Integer type; // 导出的类型，全部或者其他
	private Boolean withId; // 是否编号

	public ProductOutputDialog(Shell parentShell, Integer type, String name) {
		super(parentShell);
		this.type = type;
		this.name = name;
	}

	@Override
	protected void buttonPressed(int buttonId) {

		if (buttonId == OK) {
			String infopath = getClass().getName();
			final String root = diretoryRoot.getText();
			final String selected = combo.getText();
			perference.setInfo(infopath + DIR, root);
			withId = selectButton.getSelection();

			ProgressManager.execute(new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
					monitor.beginTask("正在导出数据中，请稍候...", 100000);
					TimeCounter.begin();
					ProductExportUtil.export(root, selected, withId, type, monitor);
					TimeCounter.end("导出总耗时");
					ConsoleManager.getInstance().append("导出开标文件完成！");
					monitor.done();
				}
			});
		}
		super.buttonPressed(buttonId);
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("导出开标文件");
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, "确定", true);
		createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		container.setLayout(new GridLayout(3, false));
		combo = createSheetCombo(container, "包号：");

		diretoryRoot = SwtUtil.createDirectorySelector(container, "输出文件夹：", "*.docx");
		;
		GridData gdList = new GridData(GridData.FILL_HORIZONTAL);
		gdList.horizontalSpan = 3;
		selectButton = SwtUtil.createButton(container, new GridData(SWT.LEFT, SWT.LEFT, false, false, 1, 1), SWT.CHECK, "是否对开标文件编号");
		selectButton.setSelection(true);
		init();
		return container;
	}

	private Combo createSheetCombo(Composite container, String title) {
		SwtUtil.createLabel(container, title, new GridData());
		Combo cb = SwtUtil.createCombo(container, new GridData());
		final GridData layoutData = new GridData();
		layoutData.horizontalSpan = 2;
		layoutData.widthHint = 80;
		cb.setLayoutData(layoutData);
		return cb;
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(600, 220);
	}

	private void init() {
		String infopath = getClass().getName();
		String diretoryPath = perference.getInfo(infopath + DIR);
		if (type == 3) {
			diretoryPath = DirManager.getOutputDir(GlobalData.getInstance().getProjectName()) + File.separator + "固化ID开标文件";
		} else {
			diretoryPath = DirManager.getOutputDir(GlobalData.getInstance().getProjectName()) + File.separator + "开标文件";
		}
		diretoryRoot.setText(diretoryPath);
		loadPackages();
	}

	private void loadPackages() {
		if (type == 1) {
			List<String> names = new ArrayList<>();
			names.add("全部");
			combo.setItems(names.toArray(new String[names.size()]));
			combo.setText("全部");
		} else if (type == 2) {
			List<String> names = new ArrayList<>();
			names.add(name);
			combo.setItems(names.toArray(new String[names.size()]));
			combo.setText(name);
		} else if (type == 3) {
			List<String> names = new ArrayList<>();
			names.add("全部");
			combo.setItems(names.toArray(new String[names.size()]));
			combo.setText("全部");
		}
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
