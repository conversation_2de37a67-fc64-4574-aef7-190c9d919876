package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;

/**
 * @Description:Dom4j解析库
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:01:04
 */
public class Dom4jUtil {

    public static Document getDocument(File f) throws DocumentException {
        Document doc = new SAXReader().read(f);
        return doc;

    }

    /**
     * ���޸ĺõ��ļ�д�뵽Դ�ļ���
     * @param f Ҫд����ļ�
     * @param doc д����ĵ�
     */
    public static void writeXML(File f, Document doc) {
        try {

            OutputStream ops = new FileOutputStream(f);
            OutputFormat format = OutputFormat.createPrettyPrint();
            format.setEncoding("utf-8");
            XMLWriter writer = new XMLWriter(ops, format);
            writer.write(doc);
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
