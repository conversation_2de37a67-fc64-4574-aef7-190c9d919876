package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.util.ArrayList;
import java.util.List;

import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;

/**
 * 包分类器
 * 负责将包按照不同的标准进行分类
 */
public class PackageClassifier {
    
    /**
     * 分类包
     * 
     * @param adjustList 调价列表
     * @return 包分类结果
     */
    public PackageCategories classifyPackages(List<PkgAdjust> adjustList) {
        List<PkgAdjust> priorityPackages = new ArrayList<>();
        List<PkgAdjust> nonPriorityPackages = new ArrayList<>();
        
        for (PkgAdjust adjust : adjustList) {
            if (Boolean.TRUE.equals(adjust.getIsPriority())) {
                priorityPackages.add(adjust);
            } else {
                nonPriorityPackages.add(adjust);
            }
        }
        
        return new PackageCategories(priorityPackages, nonPriorityPackages);
    }
} 