package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgErrInfo;

/**
 * @Description:错误信息dao
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:55:49
 
 */
public class PkgErrInfoDao {

    /**
     * 删除错误信息
     * @param errtype 错误类型
     * @param parentid 父节点id
     */
    public static void delPkgErrInfo(Integer errtype, Integer parentid) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        String sql = "DELETE FROM pkgerrinfo WHERE errtype = " + errtype + " and parentid =" + parentid;
        try {
            sqliteHelper.executeUpdate(sql);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询错误信息
     * @return
     */
    public static List<PkgErrInfo> getPkgErrInfo() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgErrInfo> sList = new ArrayList<>();
        StringBuffer sql =
            new StringBuffer(
                "select a.*,b.name as name,b.project_name as project_name,"
                    + "b.product as product,b.bidno as bidno,b.count as count from pkgerrinfo a left join pkginfo b on a.parentid=b.id ");

        try {
            sList = sqliteHelper.executeQuery(sql.toString(), new RowMapper<PkgErrInfo>() {
                @Override
                public PkgErrInfo mapRow(ResultSet rs, int index) throws SQLException {
                    PkgErrInfo pkgErrInfo = new PkgErrInfo();

                    pkgErrInfo.setId(rs.getInt("id"));
                    pkgErrInfo.setParentid(rs.getInt("parentid"));
                    pkgErrInfo.setErrtype(rs.getInt("errtype"));
                    pkgErrInfo.setErrdesc(rs.getString("errdesc"));
                    pkgErrInfo.setName(rs.getString("name"));
                    pkgErrInfo.setProjectName(rs.getString("project_name"));
                    pkgErrInfo.setProduct(rs.getString("product"));
                    pkgErrInfo.setBidno(rs.getString("bidno"));
                    pkgErrInfo.setCount(rs.getString("count"));
                    return pkgErrInfo;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    /**
     * 保存错误信息
     */
    public static void savePkgErrInfo() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
//        String sql =
//            "insert into 'pkgerrinfo' (parentid, errtype, errdesc) "
//                + "select id as parentid, 1 as errtype, '无相关技术应答' as errdesc from pkginfo where id not in"
//                + " (select distinct(parentid) from pkgproduct) union "
//                + "select distinct(parentid) as parentid, 2 as errtype, '未找到成本价格' as errdesc from pkgproduct "
//                + "where (costprice = '' or costprice is null) and ocunnt <> '' and quote='是' and ocunnt > 0 union "
//                + "select a.id as parentid, 3 as errtype, '未找到限价信息' as errdesc from pkginfo a left join limitprice b on a.product=b.product "
//                + "where b.limitprice is null;";
//        
        String sql =
                "insert into 'pkgerrinfo' (parentid, errtype, errdesc) "
                    + "select id as parentid, 1 as errtype, '无相关技术应答' as errdesc from pkginfo where id not in"
                    + " (select distinct(parentid) from pkgproduct) union "
                    + "select distinct(parentid) as parentid, 2 as errtype, '未找到成本价格' as errdesc from pkgproduct "
                    + "where (costprice = '' or costprice is null) and ocunnt <> '' and quote='是' and ocunnt > 0 ;";
        
        try {
            sqliteHelper.executeUpdate("delete from pkgerrinfo;");
            sqliteHelper.executeUpdate("update sqlite_sequence SET seq = 0 where name ='pkgerrinfo';");
            sqliteHelper.executeUpdate(sql);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

}
