/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.TimeCounter;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.utils.ProductExportUtil;

/**
 * 开标文件合并工具
 * 
 * <AUTHOR>
 * @version 1.0, 2020-4-21
 */
public class ProductMergeDialog extends WrappedDialog {
	
	private UIPreferences perference = UIPreferences.newInstance();

	private String INPUT = "input";
	private String OUTPUT = "output";

	private Text inputPath;
	private Text outputPath;

	public ProductMergeDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == OK) {
			String infopath = getClass().getName();
			final String input = inputPath.getText();
			final String output = outputPath.getText();
			String msg = checkInput();
			if (msg != null) {
				DialogHelper.showWarning(msg);
				return;
			}
			perference.setInfo(infopath + INPUT, input);
			perference.setInfo(infopath + OUTPUT, output);

			final File[] files = new File(input).listFiles();
			if (files == null) {
				DialogHelper.showAsynWarning("开标文件夹下没有文件，请检查!");
				return;
			}
			final List<String> paths = new ArrayList<>();
			for (File f : files) {
				if (f.isDirectory()) {
					paths.add(f.getPath());
				}
			}
			if (paths == null || paths.size() == 0) {
				DialogHelper.showAsynWarning("开标文件夹下没有包文件夹，请检查!");
				return;
			}

			ProgressManager.execute(new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
					monitor.beginTask("正在合并数据中，请稍候...", 100 * (paths.size() + 1));
					ProductExportUtil.mergeProductFiles(output, paths, monitor);
					monitor.worked(100);
					TimeCounter.end("合并总耗时");
					ConsoleManager.getInstance().append("开标文件合并成功！");
					monitor.done();
				}

			});
		}
		super.buttonPressed(buttonId);
	}
	

	private String checkInput() {
		return null;
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("开标文件合并工具");
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, "合并", true);
		createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		container.setLayout(new GridLayout(3, false));
		inputPath = SwtUtil.createDirectorySelector(container, "开标文件夹：", "请选择开标文件夹!");
		outputPath = SwtUtil.createDirectorySelector(container, "合并后存放文件夹：", "请选择合并后文件存放的文件夹!");

		init();
		return container;
	}
	

	private void init() {
		String infopath = getClass().getName();
		String input = perference.getInfo(infopath + INPUT);
		String output = perference.getInfo(infopath + OUTPUT);
		if (!StringUtils.isEmpty(input)) {
			inputPath.setText(input);
		}
		if (!StringUtils.isEmpty(output)) {
			outputPath.setText(output);
		}
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(600, 200);
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
