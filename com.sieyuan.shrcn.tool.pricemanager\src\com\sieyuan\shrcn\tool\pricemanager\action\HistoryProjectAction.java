package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.dialog.ProjectHistoryDialog;

/**
 * @Description:打开工程界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:09:45
 */
public class HistoryProjectAction extends MenuAction {

	public HistoryProjectAction(String text) {
		super(text);
		setAccelerator(SWT.CTRL + 'O');
	}

	@Override
	public void run() {
		ProjectHistoryDialog expDlg = new ProjectHistoryDialog(Display.getDefault().getActiveShell());
		expDlg.open();
	}
}
