/*
 * @(#) AboutAction.java
 *
 * Copyright (c) 2007 - 2013 上海思源弘瑞电力自动化有限公司.
 * All rights reserved. 基于 Eclipse E4 a next generation 
 * platform (e.g., the CSS styling, dependency injection, Modeled UI) 
 * Rich Client Application 开发的U21继电保护装置平台工具软件. 
 * 此系列工具软件包括装置调试下载工具、装置配置工具. 103通信调试工具、
 * 自动化装置测试工具。
 */
package com.sieyuan.shrcn.tool.pricemanager.action;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.shrcn.found.ui.action.MenuAction;

/***
 * 
 * 帮助信息
 * 
 * <AUTHOR>
 * @version 1.0， 2022-11-1
 * 
 */
public class HelpAction extends MenuAction {
	
	static final List<String> urls = new ArrayList<>();
	static boolean canRemove = false;

	public HelpAction(String text) {
		super(text);
	}

	public void run() {
		Runtime rt = Runtime.getRuntime();
		String url = "http://*************/help-pricemangertool/";
		try {
			rt.exec("rundll32 url.dll,FileProtocolHandler " + url);
		} catch (IOException e) {
			e.printStackTrace();
		}

	}
	
}
