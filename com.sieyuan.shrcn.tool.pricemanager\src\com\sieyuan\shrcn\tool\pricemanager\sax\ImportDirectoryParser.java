package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import com.shrcn.found.ui.util.DialogHelper;

/**
 * 导入文件夹文件
 * <AUTHOR>
 *
 */
public class ImportDirectoryParser {
    private List<String> allAbsoluteDocxFile;
    private List<String> allAbsoluteExcelFile;

    private List<String> allFile;
    private String directorypath;

    public ImportDirectoryParser(String directorypath) {
        super();
        this.directorypath = directorypath;
        this.allFile = new ArrayList<>();
        this.allAbsoluteDocxFile = new ArrayList<>();
        this.allAbsoluteExcelFile = new ArrayList<>();
    }

    public List<String> getAllAbsoluteDocxFile() {
        return allAbsoluteDocxFile;
    }

    public List<String> getAllAbsoluteExcelFile() {
        return allAbsoluteExcelFile;
    }

    public List<String> getAllFile() {
        return allFile;
    }

    public void parseDirectory() {
        File directoryFile = new File(directorypath);
        if (directoryFile.exists()) {
            File[] files = directoryFile.listFiles();
            // 遍历文件夹内的文件
            for (File file : files) {
                if (file.isHidden())
                    continue;// 隐藏文件不读
                if (!file.isDirectory()) {
                    String fileName = file.getName();
                    String absFileName = file.getAbsolutePath();
                    if (fileName.endsWith(".docx")) {
                        allAbsoluteDocxFile.add(absFileName);
                        allFile.add(fileName);
                    }
                    if (fileName.endsWith(".xlsx")) {
                        allAbsoluteExcelFile.add(absFileName);
                        allFile.add(fileName);
                    }

                }
                if (file.isDirectory()) {
                    File[] subfiles = file.listFiles();
                    for (File subfile : subfiles) {
                        String fileName = subfile.getName();
                        String absFileName = subfile.getAbsolutePath();
                        if (fileName.endsWith(".docx")) {
                            allAbsoluteDocxFile.add(absFileName);
                            allFile.add(fileName);
                        }
                        if (fileName.endsWith(".xlsx")) {
                            allAbsoluteExcelFile.add(absFileName);
                            allFile.add(fileName);
                        }
                    }
                }
            }
        } else {
            DialogHelper.showWarning("文件夹" + directorypath + "不存在！");
            return;
        }
    }

}
