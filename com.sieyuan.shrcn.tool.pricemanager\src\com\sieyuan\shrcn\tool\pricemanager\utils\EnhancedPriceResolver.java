/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.eclipse.core.runtime.IProgressMonitor;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgAdjustDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;

/**
 * 增强版价格管理算法
 * 采用模块化设计，遵循SOLID原则
 * 
 * <AUTHOR> (mailto:<EMAIL>)
 * @version 2.0, 2024-01-01
 */
public class EnhancedPriceResolver {

    private final UIPreferences preferences;
    private final List<PkgInfo> allPkgList;
    private final List<PkgProduct> allProducts;
    private final List<String> warnings;
    
    // 策略模式：价格计算策略
    private final PriceCalculationStrategy priceStrategy;
    private final PriceValidationStrategy validationStrategy;
    private final PriceDistributionStrategy distributionStrategy;

    public EnhancedPriceResolver() {
        this.preferences = UIPreferences.newInstance();
        this.allPkgList = new ArrayList<>();
        this.allProducts = new ArrayList<>();
        this.warnings = new ArrayList<>();
        
        // 初始化策略
        this.priceStrategy = new SmartPriceCalculationStrategy();
        this.validationStrategy = new ComprehensiveValidationStrategy();
        this.distributionStrategy = new WeightedDistributionStrategy();
    }

    /**
     * 主调价方法
     */
    public void adjustPrices(List<PkgAdjust> adjustList, IProgressMonitor monitor) {
        try {
            // 1. 数据预处理
            PriceDataContext context = preprocessData(adjustList, monitor);
            
            // 2. 分类处理
            processPackagesByCategory(context, monitor);
            
            // 3. 价格验证
            validatePrices(context);
            
            // 4. 结果更新
            updateResults(context);
            
        } catch (Exception e) {
            String errorMsg = "价格调整过程中发生错误: " + e.getMessage();
            ConsoleManager.getInstance().append(errorMsg);
            warnings.add(errorMsg);
        }
    }

    /**
     * 数据预处理
     */
    private PriceDataContext preprocessData(List<PkgAdjust> adjustList, IProgressMonitor monitor) {
        monitor.worked(10);
        
        // 获取基础数据
        Map<String, List<PkgInfo>> pkgMap = getPkgMap();
        Map<Integer, List<PkgProduct>> productMap = getProductMap();
        
        // 分类包
        PackageClassifier classifier = new PackageClassifier();
        PackageCategories categories = classifier.classifyPackages(adjustList);
        
        // 获取配置参数
        PriceConfiguration config = loadConfiguration();
        
        return new PriceDataContext(pkgMap, productMap, categories, config);
    }

    /**
     * 按类别处理包
     */
    private void processPackagesByCategory(PriceDataContext context, IProgressMonitor monitor) {
        // 处理优先包（无独有ID）
        processPriorityPackages(context, monitor);
        
        // 处理非优先包（有独有ID）
        processNonPriorityPackages(context, monitor);
    }

    /**
     * 处理优先包
     */
    private void processPriorityPackages(PriceDataContext context, IProgressMonitor monitor) {
        List<PkgAdjust> priorityPkgs = context.getCategories().getPriorityPackages();
        if (priorityPkgs.isEmpty()) {
            return;
        }

        // 按优先级排序
        Collections.sort(priorityPkgs, Comparator.comparing(PkgAdjust::getSeq));
        
        Map<String, BigDecimal> priceMap = new HashMap<>();
        
        for (PkgAdjust adjust : priorityPkgs) {
            processSinglePackage(adjust, context, priceMap, monitor);
        }
        
        // 更新价格映射
        updatePriceMap(priorityPkgs, context, priceMap);
    }

    /**
     * 处理非优先包
     */
    private void processNonPriorityPackages(PriceDataContext context, IProgressMonitor monitor) {
        List<PkgAdjust> nonPriorityPkgs = context.getCategories().getNonPriorityPackages();
        if (nonPriorityPkgs.isEmpty()) {
            return;
        }

        // 按优先级排序
        Collections.sort(nonPriorityPkgs, Comparator.comparing(PkgAdjust::getSeq));
        
        Map<String, BigDecimal> priceMap = new HashMap<>();
        
        for (PkgAdjust adjust : nonPriorityPkgs) {
            processSinglePackage(adjust, context, priceMap, monitor);
        }
    }

    /**
     * 处理单个包
     */
    private void processSinglePackage(PkgAdjust adjust, PriceDataContext context, 
                                    Map<String, BigDecimal> priceMap, IProgressMonitor monitor) {
        String pkgName = adjust.getPkgname();
        List<PkgInfo> pkgList = context.getPkgMap().get(pkgName);
        
        if (pkgList == null || pkgList.isEmpty()) {
            warnings.add("包 " + pkgName + " 没有找到相关信息");
            return;
        }

        // 计算目标价格
        BigDecimal targetPrice = new BigDecimal(adjust.getTargetprice());
        
        // 使用策略模式计算价格
        PriceCalculationResult result = priceStrategy.calculatePrices(
            pkgList, adjust, context.getConfig(), priceMap
        );
        
        // 价格分配
        distributionStrategy.distributePrices(result, context.getProductMap());
        
        // 更新包信息
        updatePackageInfo(pkgList, result);
        
        monitor.worked(5);
    }

    /**
     * 价格验证
     */
    private void validatePrices(PriceDataContext context) {
        for (PkgInfo pkg : allPkgList) {
            validationStrategy.validatePackage(pkg, warnings);
        }
    }

    /**
     * 更新结果
     */
    private void updateResults(PriceDataContext context) {
        PkgInfoDao.updatePkgInfo(allPkgList);
        PkgProductDao.updatePkgProductAnalysisPrice(allProducts);
    }

    /**
     * 加载配置
     */
    private PriceConfiguration loadConfiguration() {
        String inWeight = preferences.getInfo(EnvirmentSettingDialog.class.getName() + ".inTax");
        if (StringUtils.isEmpty(inWeight)) {
            inWeight = "2";
        }
        
        String outWeight = "1";
        String onlyIdRate = preferences.getInfo(EnvirmentSettingDialog.class.getName() + ".onlyIdRate");
        if (StringUtils.isEmpty(onlyIdRate)) {
            onlyIdRate = "1.0";
        }
        
        String originRate = preferences.getInfo(EnvirmentSettingDialog.class.getName() + ".originrate");
        if (StringUtils.isEmpty(originRate)) {
            originRate = "1.0";
        }
        
        return new PriceConfiguration(inWeight, outWeight, onlyIdRate, originRate);
    }

    /**
     * 获取包映射
     */
    private Map<String, List<PkgInfo>> getPkgMap() {
        Map<String, List<PkgInfo>> pkgMaps = new HashMap<>();
        List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
        
        for (PkgInfo pkgInfo : pkgs) {
            pkgMaps.computeIfAbsent(pkgInfo.getName(), k -> new ArrayList<>()).add(pkgInfo);
        }
        
        return pkgMaps;
    }

    /**
     * 获取产品映射
     */
    private Map<Integer, List<PkgProduct>> getProductMap() {
        Map<Integer, List<PkgProduct>> pkgProductMaps = new HashMap<>();
        List<PkgProduct> pkgLists = PkgProductDao.getPkgProductByParentid(null);
        
        for (PkgProduct pkgProduct : pkgLists) {
            pkgProductMaps.computeIfAbsent(pkgProduct.getParentid(), k -> new ArrayList<>()).add(pkgProduct);
        }
        
        return pkgProductMaps;
    }

    /**
     * 更新价格映射
     */
    private void updatePriceMap(List<PkgAdjust> packages, PriceDataContext context, Map<String, BigDecimal> priceMap) {
        for (PkgAdjust adjust : packages) {
            List<PkgInfo> pkgList = context.getPkgMap().get(adjust.getPkgname());
            if (pkgList != null) {
                for (PkgInfo pkgInfo : pkgList) {
                    BigDecimal unitPrice = CalcUtil.divide(pkgInfo.getTargetPrice(), pkgInfo.getCount());
                    priceMap.put(pkgInfo.getBidNo(), unitPrice);
                }
            }
        }
    }

    /**
     * 更新包信息
     */
    private void updatePackageInfo(List<PkgInfo> pkgList, PriceCalculationResult result) {
        for (PkgInfo pkgInfo : pkgList) {
            allPkgList.add(pkgInfo);
        }
    }

    /**
     * 更新计算结果
     */
    public void updateCalResult() {
        PkgInfoDao.updatePkgInfo(allPkgList);
        PkgProductDao.updatePkgProductAnalysisPrice(allProducts);
    }

    /**
     * 更新结果
     */
    public void updateResult(List<PkgAdjust> adjustList) {
        PkgAdjustDao.updateResultPricePkgInfo(adjustList);
    }

    /**
     * 获取警告信息
     */
    public List<String> getWarnings() {
        return warnings;
    }

    /**
     * 计算独有ID比率
     */
    public void calOnlyRate(List<PkgAdjust> adjustList) {
        OnlyIdRateCalculator calculator = new OnlyIdRateCalculator();
        calculator.calculateOnlyIdRates(adjustList);
    }
} 