package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;

import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.Product;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;

public class YZCProductHanderTest {

	@Test
	public void test() {
		Map<String, CostPrice> priceMap = new HashMap<>();
		List<Product> productlist = new ArrayList<>();
		int rowId = 1;

		String file = "e://9906-500138496-00013_预制舱式二次组合设备,AC110kV_1.docx";

        YZCProductHander yzc1 = new YZCProductHander(priceMap, 1);

        Product product = new Product(file);
        product.setRowId(rowId);
        product.setBidno(FieldUtils.getFileId(file));
        product.setName(FieldUtils.getPkgName(file));
        product.setOrderid(0);
        product.setQuote("否");
        product.setNumber("一");
        product.setDevname("预制舱舱体");
        productlist.add(product);
        yzc1.parse(file, productlist, rowId, 1);
        YZCProductHander yzc2 = new YZCProductHander(priceMap, 2);

        Product product2 = new Product(file);
        product2.setRowId(rowId);
        product2.setBidno(FieldUtils.getFileId(file));
        product2.setName(FieldUtils.getPkgName(file));
        product2.setOrderid(productlist.get(productlist.size() - 1).getOrderid());
        product2.setQuote("否");
        product2.setNumber("二");
        product2.setDevname("智能变电站故障录波装置");
        productlist.add(product2);
        yzc2.parse(file, productlist, rowId, productlist.get(productlist.size() - 1).getOrderid());

        YZCProductHander yzc3 = new YZCProductHander(priceMap, 3);
        Product product3 = new Product(file);
        product3.setRowId(rowId);
        product3.setBidno(FieldUtils.getFileId(file));
        product3.setName(FieldUtils.getPkgName(file));
        product3.setOrderid(productlist.get(productlist.size() - 1).getOrderid());
        product3.setQuote("否");
        product3.setNumber("三");
        product3.setDevname("智能变电站时间同步装置");
        productlist.add(product3);
        yzc3.parse(file, productlist, rowId, productlist.get(productlist.size() - 1).getOrderid());
        YZCProductHander yzc4 = new YZCProductHander(priceMap, 4);

        Product product4 = new Product(file);
        product4.setRowId(rowId);
        product4.setBidno(FieldUtils.getFileId(file));
        product4.setName(FieldUtils.getPkgName(file));
        product4.setOrderid(productlist.get(productlist.size() - 1).getOrderid());
        product4.setQuote("否");
        product4.setNumber("四");
        product4.setDevname("智能变电站电能量采集终端");
        productlist.add(product4);
        yzc4.parse(file, productlist, rowId, productlist.get(productlist.size() - 1).getOrderid());

        YZCProductHander yzc5 = new YZCProductHander(priceMap, 5);
        Product product5 = new Product(file);
        product5.setRowId(rowId);
        product5.setBidno(FieldUtils.getFileId(file));
        product5.setName(FieldUtils.getPkgName(file));
        product5.setOrderid(productlist.get(productlist.size() - 1).getOrderid());
        product5.setQuote("否");
        product5.setNumber("五");
        product5.setDevname("变电站智能一体化电源系统");
        productlist.add(product5);
        yzc5.parse(file, productlist, rowId, productlist.get(productlist.size() - 1).getOrderid());
        
		for (Product productTmp : productlist) {
			System.out.println(productTmp);
		}
	}

}
