output.. = bin/
javacDefaultEncoding..=UTF-8
bin.includes = META-INF/,\
               .,\
               css/default.css,\
               splash.bmp,\
               css/blue-gradient.css,\
               css/blue-gradient.png,\
               css/bright-gradient.css,\
               css/bright-gradient.png,\
               css/dark-gradient.css,\
               css/dark-gradient.png,\
               icons/,\
               bin/,\
               .classpath,\
               .project,\
               .settings/,\
               Application.e4xmi,\
               build.properties,\
               com.sieyuan.shrcn.tool.pricemanager.product,\
               file/,\
               hs_err_pid3088.log,\
               hs_err_pid3476.log,\
               hs_err_pid7792.log,\
               plugin.properties,\
               plugin.xml,\
               plugin_zh.properties,\
               fragment.e4xmi,\
               jacob-1.17-x86.dll,\
               lib/sqlite-jdbc-3.27.2.1.jar,\
               lib/jacob.jar,\
               AppPref.ini,\
               customization.ini,\
               pricemanager.ico,\
               lib/spire.xls.free-3.9.2.jar,\
               lib/easyexcel-2.2.6.jar
source.. = src/
