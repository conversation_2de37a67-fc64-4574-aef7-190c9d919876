/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.model;

import java.math.BigDecimal;

/**
 * @Description: 统计结果
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-27 上午8:28:41
 */
public class PkgTotal {

    private Integer id;
    private String pkgname;
    private String costTax;
    private String cost;
    private String rate;
    private String rate2;
    private String rateIn;
    private String rateOut;
    private String limitprice;
    private String targetprice;
    private String price;
    private String contribution; // 边际贡献

    public PkgTotal() {}

    public PkgTotal(String pkgname) {
        this.pkgname = pkgname;
    }

    public String getPkgname() {
        return pkgname;
    }

    public void setPkgname(String pkgname) {
        this.pkgname = pkgname;
    }

    public String getCostTax() {
        return costTax;
    }

    public void setCostTax(String costTax) {
        this.costTax = costTax;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public String getRateIn() {
        return rateIn;
    }

    public void setRateIn(String rateIn) {
        this.rateIn = rateIn;
    }

    public String getRateOut() {
        return rateOut;
    }

    public void setRateOut(String rateOut) {
        this.rateOut = rateOut;
    }

    public String getLimitprice() {
        return limitprice;
    }

    public void setLimitprice(String limitprice) {
        this.limitprice = limitprice;
    }

    public String getTargetprice() {
        return targetprice;
    }

    public void setTargetprice(String targetprice) {
        this.targetprice = targetprice;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getDiscount() {
        BigDecimal p = new BigDecimal(targetprice).divide(new BigDecimal(limitprice));
        p.setScale(6, BigDecimal.ROUND_HALF_UP);
        return p.toString();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getContribution() {
        return contribution;
    }

    public void setContribution(String contribution) {
        this.contribution = contribution;
    }

	public String getRate2() {
		return rate2;
	}

	public void setRate2(String rate2) {
		this.rate2 = rate2;
	}

}
