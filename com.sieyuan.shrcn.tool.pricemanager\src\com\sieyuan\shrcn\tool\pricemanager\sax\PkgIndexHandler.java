package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgIndex;

@SuppressWarnings("rawtypes")
public class PkgIndexHandler extends SheetsHandler {

    private PkgIndex pkgIndex;
    private List<PkgIndex> pkgIndexex;

    public PkgIndexHandler() {
        pkgIndexex = new ArrayList<>();
    }

    @Override
    public void cell(String cellReference, String formattedValue, XSSFComment comment) {
        super.cell(cellReference, formattedValue, comment);

        if (currentRow > 1 && !isEmpty(formattedValue)) {
            if (currentCol == 8) {
                pkgIndex.setRowId(Integer.valueOf(formattedValue));
            } else if (currentCol == 9) {
                pkgIndex.setPkgFile(formattedValue);
            }
        }
    }

    @Override
    public void endRow(int rowNum) {
        if (!StringUtils.isEmpty(pkgIndex.getPkgFile())) {
            pkgIndexex.add(pkgIndex);
        }
    }

    public List<PkgIndex> getPkgIndexex() {
        return pkgIndexex;
    }

    @Override
    public void startRow(int rowNum) {
        super.startRow(rowNum);
        this.pkgIndex = new PkgIndex();
    }

}
