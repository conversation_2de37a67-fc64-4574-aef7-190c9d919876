package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgBidInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgTotal;
import com.sieyuan.shrcn.tool.pricemanager.utils.CalcUtil;

/**
 * @Description: 物料信息Dao
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:57:41
 
 */
public class PkgInfoDao {

    public static List<PkgTotal> getPkgAnalysis(String pkgName) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgTotal> sList = new ArrayList<>();
        StringBuffer sb =
            new StringBuffer(
                "SELECT name, sum(totaltax) as totaltax, sum(totallimitprice) as totallimitprice , sum(totalprice) as totalprice from pkginfo ");
        if (!StringUtils.isEmpty(pkgName)) {
            sb.append("where name = '" + pkgName + "'");
        }
        sb.append(" group by name");
        try {
            sList = sqliteHelper.executeQuery(sb.toString(), new RowMapper<PkgTotal>() {
                @Override
                public PkgTotal mapRow(ResultSet rs, int index) throws SQLException {
                    PkgTotal pkgTotal = new PkgTotal();
                    pkgTotal.setPkgname(rs.getString("name"));
                    pkgTotal.setLimitprice(rs.getString("totallimitprice"));
                    pkgTotal.setCostTax(rs.getString("totaltax"));
                    pkgTotal.setPrice(rs.getString("totalprice"));
                    return pkgTotal;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static List<PkgBidInfo> getPkgBidInfos() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgBidInfo> sList = new ArrayList<>();
        try {
            sList =
                sqliteHelper
                    .executeQuery(
                        "select bidno,name, (a.count * b.limitprice) as limitprice ,a.count, a.orgPrice, a.product_desc, a.price,a.totalprice, a.avg, a.isValid, a.pavg, a.pisValid, a.valid, a.isSameId from pkginfo a left join limitprice b on a.bidno=b.product order by a.bidno, a.name",
                        new RowMapper<PkgBidInfo>() {
                            @Override
                            public PkgBidInfo mapRow(ResultSet rs, int index) throws SQLException {
                                PkgBidInfo pkgBidInfo = new PkgBidInfo();
                                pkgBidInfo.setPkgName(rs.getString("name"));
                                pkgBidInfo.setBidNo(rs.getString("bidno"));
                                pkgBidInfo.setLimitprice(rs.getString("limitprice"));
                                pkgBidInfo.setCount(rs.getString("count"));
                                pkgBidInfo.setPrice(rs.getString("price"));
                                pkgBidInfo.setTotalprice(rs.getString("totalprice"));
                                pkgBidInfo.setAvg(rs.getString("avg"));
                                pkgBidInfo.setIsValid(rs.getString("isValid"));
                                pkgBidInfo.setPavg(rs.getString("pavg"));
                                pkgBidInfo.setPisValid(rs.getString("pisValid"));
                                pkgBidInfo.setValid(rs.getString("valid"));
                                pkgBidInfo.setIsSameId(rs.getString("isSameId"));
                                pkgBidInfo.setProduct(rs.getString("product_desc"));
                                pkgBidInfo.setOrgPrice(StringUtils.trimToEmpty(rs.getString("orgPrice")));
                                return pkgBidInfo;
                            }
                        });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }
    
    public static List<PkgBidInfo> getProductReqortInfos() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgBidInfo> sList = new ArrayList<>();
        try {
            sList =
                sqliteHelper
                    .executeQuery(
                        "select bidno,name, a.product_desc, (a.count * b.limitprice) as limitprice ,a.count,a.price,a.totalprice, a.wavg, a.wisValid, a.wpavg, a.wpisValid, a.wvalid, a.isSameId from pkginfo a left join limitprice b on a.bidno=b.product order by a.bidno, a.name",
                        new RowMapper<PkgBidInfo>() {
                            @Override
                            public PkgBidInfo mapRow(ResultSet rs, int index) throws SQLException {
                                PkgBidInfo pkgBidInfo = new PkgBidInfo();
                                pkgBidInfo.setPkgName(rs.getString("name"));
                                pkgBidInfo.setBidNo(rs.getString("bidno"));
                                pkgBidInfo.setProduct(rs.getString("product_desc"));
                                pkgBidInfo.setLimitprice(rs.getString("limitprice"));
                                pkgBidInfo.setCount(rs.getString("count"));
                                pkgBidInfo.setPrice(rs.getString("price"));
                                pkgBidInfo.setTotalprice(rs.getString("totalprice"));
                                pkgBidInfo.setAvg(rs.getString("wavg"));
                                pkgBidInfo.setIsValid(rs.getString("wisValid"));
                                pkgBidInfo.setPavg(rs.getString("wpavg"));
                                pkgBidInfo.setPisValid(rs.getString("wpisValid"));
                                pkgBidInfo.setValid(rs.getString("wvalid"));
                                pkgBidInfo.setIsSameId(rs.getString("isSameId"));
                                return pkgBidInfo;
                            }
                        });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static List<PkgInfo> getPkgs() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgInfo> sList = new ArrayList<>();
        try {
            sList = sqliteHelper.executeQuery("select * from pkginfo", new RowMapper<PkgInfo>() {
                @Override
                public PkgInfo mapRow(ResultSet rs, int index) throws SQLException {
                    PkgInfo pkgInfo = new PkgInfo();
                    pkgInfo.setId(rs.getInt("id"));
                    pkgInfo.setName(rs.getString("name"));
                    pkgInfo.setProjectOwner(rs.getString("project_owner"));
                    pkgInfo.setProjectName(rs.getString("project_name"));
                    pkgInfo.setProduct(rs.getString("product"));
                    pkgInfo.setProductDesc(rs.getString("product_desc"));
                    pkgInfo.setUnit(rs.getString("unit"));
                    pkgInfo.setCount(rs.getString("count"));
                    pkgInfo.setIntelligence(rs.getString("intelligence"));
                    pkgInfo.setVoltageGrade(rs.getString("voltage_grade"));
                    pkgInfo.setBidNo(rs.getString("bidno"));
                    pkgInfo.setApplyId(rs.getString("applyId"));
                    pkgInfo.setRowId(rs.getInt("prowid"));
                    pkgInfo.setTotalPrice(rs.getString("totalprice"));
                    pkgInfo.setPrice(rs.getString("price"));
                    pkgInfo.setIsValid(rs.getString("isValid"));
                    pkgInfo.setWisValid(rs.getString("wisValid"));
                    pkgInfo.setPricerate(rs.getString("pricerate"));
                    return pkgInfo;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static List<PkgInfo> getPkgsWithLimitPrice(String name, String projectName, String bidno) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgInfo> sList = new ArrayList<>();
        StringBuffer sql =
            new StringBuffer("select * from pkginfo a left join limitprice b on a.bidno= b.product where 1=1 ");

        if (StringUtils.isNotEmpty(name)) {
            sql.append(" and a.name = '" + name + "' ");
        }
        if (StringUtils.isNotEmpty(projectName)) {
            sql.append(" and a.project_name = '" + projectName + "' ");
        }

        if (StringUtils.isNotEmpty(bidno)) {
            sql.append(" and a.bidno = '" + bidno + "' ");
        }

        try {
            sList = sqliteHelper.executeQuery(sql.toString(), new RowMapper<PkgInfo>() {
                @Override
                public PkgInfo mapRow(ResultSet rs, int index) throws SQLException {
                    PkgInfo pkgInfo = new PkgInfo();
                    pkgInfo.setId(rs.getInt("id"));
                    pkgInfo.setName(rs.getString("name"));
                    pkgInfo.setProjectOwner(rs.getString("project_owner"));
                    pkgInfo.setProjectName(rs.getString("project_name"));
                    pkgInfo.setProduct(rs.getString("product"));
                    pkgInfo.setProductDesc(rs.getString("product_desc"));
                    pkgInfo.setUnit(rs.getString("unit"));
                    pkgInfo.setCount(rs.getString("count"));
                    pkgInfo.setIntelligence(rs.getString("intelligence"));
                    pkgInfo.setVoltageGrade(rs.getString("voltage_grade"));
                    pkgInfo.setBidNo(rs.getString("bidno"));
                    pkgInfo.setApplyId(rs.getString("applyId"));
                    pkgInfo.setLimitPrice(rs.getString("limitprice"));
                    pkgInfo.setRowId(rs.getInt("prowid"));
                    pkgInfo.setTotalPrice(rs.getString("totalprice"));
                    pkgInfo.setPrice(rs.getString("price"));
                    pkgInfo.setTotalTax(rs.getString("totalTax")); // 含税成本.setTotalTax(totalTaxPkg.toString()); // 含税成本
                    pkgInfo.setWithoutTaxPrice(rs.getString("wthoutTaxPrice"));
                    pkgInfo.setWithoutTotalPrice(rs.getString("withOutTaxTotal"));

                    pkgInfo.setAvg(rs.getString("avg"));
                    pkgInfo.setIsValid(rs.getString("isValid"));
                    pkgInfo.setPavg(rs.getString("pavg"));
                    pkgInfo.setPisValid(rs.getString("pisValid"));
                    pkgInfo.setValid(rs.getString("valid"));
                    
                    pkgInfo.setWavg(rs.getString("wavg"));
                    pkgInfo.setWisValid(rs.getString("wisValid"));
                    pkgInfo.setWpavg(rs.getString("wpavg"));
                    pkgInfo.setWpisValid(rs.getString("wpisValid"));
                    pkgInfo.setWvalid(rs.getString("wvalid"));
                    
                    pkgInfo.setIsSameId(rs.getString("isSameId"));
                    pkgInfo.setPricerate(rs.getString("pricerate"));
                    pkgInfo.setRealPrice(rs.getString("realprice"));
                    pkgInfo.setOrgPrice(rs.getString("orgPrice"));
                         
                    if (!StringUtils.isEmpty(rs.getString("limitprice"))) {
                        pkgInfo.setTotalLimitPrice(String.format("%.6f", Double.valueOf(rs.getString("limitprice"))
                            * Integer.valueOf(rs.getString("count"))));
                    } else {
                        pkgInfo.setTotalLimitPrice("");
                    }
                    return pkgInfo;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static List<PkgTotal> getPkgTotals() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgTotal> sList = new ArrayList<>();
        try {
            sList =
                sqliteHelper
                    .executeQuery(
                        "SELECT name, sum(totaltax) as totaltax, sum(b.limitprice * a.count * 100)/100 as totallimitprice from pkginfo a  left join limitprice b on a.bidno=b.product   group by name",
                        new RowMapper<PkgTotal>() {
                            @Override
                            public PkgTotal mapRow(ResultSet rs, int index) throws SQLException {
                                PkgTotal pkgTotal = new PkgTotal();
                                pkgTotal.setPkgname(rs.getString("name"));
                                pkgTotal.setCostTax(rs.getString("totaltax"));
                                pkgTotal.setLimitprice(rs.getString("totallimitprice"));
                                pkgTotal.setTargetprice("");
                                pkgTotal.setPrice("");
                                return pkgTotal;
                            }
                        });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }
    
	public static Map<String, PkgAdjust> getPkgAdjust() {
		Map<String, PkgAdjust> adjustMap = new HashMap<>();
		List<PkgInfo> allPkg = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
		for (PkgInfo pkgInfo : allPkg) {
			PkgAdjust adjust = new PkgAdjust();
			adjust.setLimitprice("0");
			adjust.setPkgname(pkgInfo.getName());
			if (adjustMap.containsKey(pkgInfo.getName())) {
				adjust = adjustMap.get(pkgInfo.getName());
			}
			if (!StringUtils.isEmpty(pkgInfo.getTotalLimitPrice())) {
				adjust.setLimitprice(CalcUtil.getBigDecimalString(CalcUtil.getBigDecimal(adjust.getLimitprice()).add(CalcUtil.getBigDecimal(pkgInfo.getTotalLimitPrice()))));
			} else {
				adjust.setLimitprice(CalcUtil.getBigDecimalString(CalcUtil.getBigDecimal(adjust.getLimitprice())));
			}
			adjustMap.put(pkgInfo.getName(), adjust);
		}
		return adjustMap;
	}

    public static void savePkgs(List<PkgInfo> pkgs) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgInfo pkginfo : pkgs) {
            String name = pkginfo.getName(); // 包号
            String projectOwner = pkginfo.getProjectOwner(); // 项目单位
            if(StringUtils.isEmpty(pkginfo.getProjectName())){
            	pkginfo.setProjectName(ToolConstants.DEFAULT_PRJ);
            } 
            String projectName = pkginfo.getProjectName(); // 项目名称
            String product = pkginfo.getProduct(); // 物资名称
            String productDesc = pkginfo.getProductDesc(); // 物资描述
            String unit = pkginfo.getUnit(); // 单位
            String count = pkginfo.getCount(); // 数量
            String intelligence = pkginfo.getIntelligence(); // 是否智能
            String voltageGrade = pkginfo.getVoltageGrade(); // 电压等级
            String bidNo = pkginfo.getBidNo(); // 技术规范ID
            String applyId = pkginfo.getApplyId(); // 申请号
            String pricerate = pkginfo.getPricerate(); // 行报价基准价
            Integer rowId = pkginfo.getRowId(); // 行号

            String sql =
                "INSERT INTO pkginfo (name, project_owner, project_name, product, product_desc, unit,count, "
                    + "intelligence,voltage_grade, bidno, applyId, pricerate, prowid) " + "VALUES (" + "'"
                    + name
                    + "'"
                    + ", "
                    + "'"
                    + projectOwner
                    + "'"
                    + ", "
                    + "'"
                    + projectName
                    + "'"
                    + ", "
                    + "'"
                    + product
                    + "'"
                    + ", "
                    + "'"
                    + productDesc
                    + "'"
                    + ", "
                    + "'"
                    + unit
                    + "'"
                    + ", "
                    + "'"
                    + count
                    + "'"
                    + ", "
                    + "'"
                    + intelligence
                    + "'"
                    + ", "
                    + "'"
                    + voltageGrade
                    + "'"
                    + ", "
                    + "'"
                    + bidNo
                    + "','" + applyId
                    + "','" + pricerate
                    + "',"+ rowId + ")";
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

    public static void updatePkgInfo(List<PkgInfo> pkgInfoList) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgInfo pkgInfo : pkgInfoList) {
            Integer id = pkgInfo.getId();
            String totalTax = pkgInfo.getTotalTax();
            String totaltarget = pkgInfo.getTargetPrice();
            String totalLimitPrice = pkgInfo.getTotalLimitPrice();
            String totalprice = pkgInfo.getTotalPrice();
            String price = pkgInfo.getPrice();
            
            String wthoutTaxPrice = pkgInfo.getWithoutTaxPrice();
            String withOutTaxTotal = pkgInfo.getWithoutTotalPrice();

            String sql =
                "update pkginfo set totallimitprice='" + totalLimitPrice + "', price='" + price + "', totaltax='"
                    + totalTax + "', wthoutTaxPrice='" + wthoutTaxPrice + "', withOutTaxTotal='" + withOutTaxTotal
                    + "', totalprice='" + totalprice + "',totaltarget='" + totaltarget + "' where id=" + id;
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
    
    public static void updateRealPricePkgInfo(List<PkgInfo> pkgInfoList) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgInfo pkgInfo : pkgInfoList) {
            Integer id = pkgInfo.getId();
            String realPrice = pkgInfo.getRealPrice();
            String orgPrice = pkgInfo.getOrgPrice();

            String sql =
                "update pkginfo set realprice='" + realPrice + "', orgPrice='" + orgPrice + "' where id=" + id;
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 更新相同ID
     * @param pkgInfoList 
     */
	public static void updatePkgInfoSameId(List<PkgInfo> pkgInfoList) {
		SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
		List<String> sqls = new ArrayList<String>();
		for (PkgInfo pkgInfo : pkgInfoList) {
			Integer id = pkgInfo.getId();
			String isSameId = pkgInfo.getIsSameId();

			String sql = "update pkginfo set isSameId='" + isSameId + "' where id=" + id;
			sqls.add(sql);
		}
		try {
			sqliteHelper.executeUpdate(sqls);
		} catch (ClassNotFoundException | SQLException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 更新行报价基准价
	 * 
	 * @param pkgInfoList
	 */
	public static void updatePkgInfoPriceRateById(List<PkgInfo> pkgInfoList) {
		SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
		List<String> sqls = new ArrayList<String>();
		for (PkgInfo pkgInfo : pkgInfoList) {
			Integer id = pkgInfo.getId();
			String priceRate = pkgInfo.getPricerate();

			String sql = "update pkginfo set pricerate='" + priceRate + "' where id=" + id;
			sqls.add(sql);
		}
		try {
			sqliteHelper.executeUpdate(sqls);
		} catch (ClassNotFoundException | SQLException e) {
			e.printStackTrace();
		}
	}

    /**
     * 更新告警字段
     */
    public static void updatePkgValid(List<PkgInfo> pkgInfoList) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
		for (PkgInfo pkgInfo : pkgInfoList) {
			Integer id = pkgInfo.getId();
			
			String isValid = pkgInfo.getIsValid();
			String avgRate = pkgInfo.getAvgRate();
			String avg = pkgInfo.getAvg();
			String pisValid = pkgInfo.getPisValid();
			String pavg = pkgInfo.getPavg();
			String valid = pkgInfo.getValid();
			
			String wisValid = pkgInfo.getWisValid();
			String wavgRate = pkgInfo.getWavgRate();
			String wavg = pkgInfo.getWavg();
			String wpisValid = pkgInfo.getWpisValid();
			String wpavg = pkgInfo.getWpavg();
			String wvalid = pkgInfo.getWvalid();

			String sql = "update pkginfo set isValid='" + isValid + "', avg='" + avg + "', avgRate='" + avgRate + "', pisValid='"
					+ pisValid + "', pavg='" + pavg + "', valid='" + valid + "', wisValid='" + wisValid + "', wavg='" + wavg + "', wavgRate='" + wavgRate + "', wpisValid='"
					+ wpisValid + "', wpavg='" + wpavg + "', wvalid='" + wvalid + "'  where id=" + id;
			sqls.add(sql);
		}
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除调价的价格信息
     */
	public static void deletePkgInfo() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
		String sql = "update pkginfo set totaltax='', totalprice='', price='',totaltax='',wthoutTaxPrice='',withOutTaxTotal='';";
        try {
            sqliteHelper.executeUpdate(sql);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
		
	}
}
