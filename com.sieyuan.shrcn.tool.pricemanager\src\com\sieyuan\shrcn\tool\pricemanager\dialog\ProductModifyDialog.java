package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.file.util.FileManipulate;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.dialog.MessageDialog;
import com.shrcn.found.ui.u21.table.Table;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.model.FileItem;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.TypeRule;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.utils.ProductExportUtil;
import com.sieyuan.shrcn.tool.pricemanager.views.table.FileTableMode;

/**
 * 开标文件一纸导出功能
 * 
 * <AUTHOR>
 * @version 1.0, 2020-6-13
 */
public class ProductModifyDialog extends WrappedDialog {

	private UIPreferences perference = UIPreferences.newInstance();

	private String OUT_DIR = "开标文件一纸";
	private String PKG_PATH = ".pkgRoot";
	private String OUT_PATH = ".outRoot";
	private String OUTPUT_RULE = ".outputRule";

	// 物料清单
	private FileTableMode fileTableMode;
	private Map<String, String> priceMaps;
	private List<PkgInfo> pkgs;
	private Table table;
	private Text pkgPathTxt;
	private Text outputPathTxt;
	private Text outputRule;
	private Button selectBtn;

	public ProductModifyDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		container.setLayout(new GridLayout(3, false));
		pkgPathTxt = SwtUtil.createDirectorySelector(container, "单价汇总表文件夹：", "*.");

		outputRule = SwtUtil.createFileSelector(container, "型号品牌填写规则：", "*.xlsx");
		outputPathTxt = SwtUtil.createDirectorySelector(container, "输出文件夹：", "*.");

		table = new Table(container, SWT.BORDER | SWT.V_SCROLL | SWT.H_SCROLL);
		table.setLayout(new GridLayout(1, false));

		GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 6, 10);
		gd_table.widthHint = 650;
		gd_table.heightHint = 280;
		table.setLayoutData(gd_table);
		fileTableMode = new FileTableMode();
		table.setModel(fileTableMode);

		initData();
		addListeners();
		return container;
	}

	@SuppressWarnings("unchecked")
	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == OK) {
			String pkgPath = pkgPathTxt.getText();
			String outRulePath = outputRule.getText();
			String outFilePath = outputPathTxt.getText();
			if (StringUtils.isEmpty(pkgPath)) {
				DialogHelper.showAsynError("单价汇总表文件夹不能为空！");
				return;
			}
			if (!selectBtn.getSelection()) { // 非协议库存
				if (StringUtils.isEmpty(outRulePath)) {
					DialogHelper.showAsynError("型号品牌填写规则不能为空！");
					return;
				}
			}
			if (StringUtils.isEmpty(outFilePath)) {
				DialogHelper.showAsynError("输出文件夹不能为空！");
				return;
			}
			generateTecFile();
			MessageDialog.openInformation(this.getShell(), "开标文件一纸导出工具", "开标文件一纸文件生成成功!");
			return;
		} else if (buttonId == IDialogConstants.ABORT_ID) {
			FileDialog dialog = new FileDialog(this.getShell(), SWT.OPEN | SWT.MULTI);
			dialog.setFilterExtensions(new String[] { "*.xlsx" });
			@SuppressWarnings("unused")
			String fileName = dialog.open();// 返回最后一个选择文件的全路径
			String[] fileNames = dialog.getFileNames();// 返回所有选择的文件名，不包括路径
			String path = dialog.getFilterPath();// 返回选择的路径，这个和fileNames配合可以得到所有的文件的全路径
			List<FileItem> items = fileTableMode.getItems();
			FileItem item = new FileItem();
			for (String fileItem : fileNames) {
				item = new FileItem();
				item.setChecked(true);
				item.setStatus("");
				item.setFileName(fileItem);
				item.setFileType(fileItem.substring(fileItem.lastIndexOf(".") + 1));
				File file = new File(path + File.separator + fileItem);
				item.setAbsFileName(file.getAbsolutePath());
				item.setFileSize(FieldUtils.getFileSize(file));
				items.add(item);
			}
			fileTableMode.setItems(items);
			table.redraw();
			return;
		} else if (buttonId == IDialogConstants.BACK_ID) {
			List<FileItem> itemsList = new ArrayList<>();
			List<FileItem> items = fileTableMode.getItems();
			for (FileItem fileItem : items) {
				if (!fileItem.isChecked()) {
					itemsList.add(fileItem);
				}
			}
			fileTableMode.setItems(itemsList);
			table.redraw();
			return;
		}
		super.buttonPressed(buttonId);
	}

	@SuppressWarnings("unchecked")
	private void generateTecFile() {
		final List<FileItem> fileItems = fileTableMode.getItems();
		final String outPath = outputPathTxt.getText();
		// 2、遍历解压后的文件，存入到map对象中
		final String filePath = pkgPathTxt.getText();
		final String outRulePath = outputRule.getText();
		final boolean selection = selectBtn.getSelection();
		ProgressManager.execute(new IRunnableWithProgress() {
			@Override
			public void run(final IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
				Map<String, TypeRule> typeMap = new HashMap<>();
				Map<String, PkgInfo> pkgMap = new HashMap<>();
				if (!selection) { // 非协议库存
					monitor.beginTask("正在解析型号品牌填写规则...", 5);
					typeMap = ProductExportUtil.parseTypeRule(outRulePath);
					
					List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
					for (PkgInfo pkgInfo : pkgs) {
						pkgMap.put(pkgInfo.getApplyId(), pkgInfo);
					}
				}
				monitor.beginTask("正在解析单价汇总表...", 5);
				pkgs = new ArrayList<>();
				// 2、按照包号获取物料清单
				priceMaps = new HashMap<>();

				List<String> allFiles = new ArrayList<String>();
				FieldUtils.getAllFilePaths(new File(filePath), allFiles);

				// 1、解析货物清单
				for (String fileName : allFiles) {
					String priceFile = new File(fileName).getName();
					if (priceFile.contains("包") && priceFile.endsWith(ToolConstants.XLSX)) {
						ProductExportUtil.parsePkg(fileName, pkgs);
					}
				}

				for (PkgInfo pkgInfo : pkgs) {
					priceMaps.put(pkgInfo.getApplyId(), pkgInfo.getWithoutTaxPrice());
				}

				monitor.worked(10);
				monitor.done();

				monitor.beginTask("正在生成开标文件一纸文件...", fileItems.size() * 30 + pkgs.size());

		
				for (final FileItem fileItem : fileItems) {
					String templatePath = fileItem.getAbsFileName();
					String fileName = fileItem.getFileName();
					updateTable(fileItem, "正在处理中...");
					String templateOutput = outPath + File.separator + OUT_DIR + File.separator + fileName;
					if (!new File(outPath + File.separator + OUT_DIR).exists()) {
						new File(outPath + File.separator + OUT_DIR).mkdirs();
					}
					FileManipulate.copyByChannel(templatePath, templateOutput);
					generateExcute(templateOutput, typeMap, pkgMap, selection, monitor);
					updateTable(fileItem, "文件处理完毕");
					monitor.worked(30);
				}
				monitor.done();
			}

		});
	}

	/**
	 * 初始化数据
	 */
	private void initData() {
		pkgPathTxt.setText(perference.getInfo(ProductModifyDialog.class.getName() + PKG_PATH));
		outputPathTxt.setText(perference.getInfo(ProductModifyDialog.class.getName() + OUT_PATH));
		outputRule.setText(perference.getInfo(ProductModifyDialog.class.getName() + OUTPUT_RULE));
	}

	/**
	 * 执行生成过程
	 * 
	 * @param typeMap
	 * @param pkgProductMap
	 * @param pkgMap
	 * @param selection
	 * 
	 * @param bidMaps2
	 * 
	 * @param needModify
	 * 
	 */
	public void generateExcute(String templateOutput, Map<String, TypeRule> typeMap, Map<String, PkgInfo> pkgMap, boolean selection, IProgressMonitor monitor) {
		// 处理excel文件
		if (selection) {
			ProductExportUtil.writeExcelPOI(templateOutput, priceMaps);
		} else {
			ProductExportUtil.writeExcelPOI(templateOutput, typeMap, pkgMap, priceMaps);
		}
	}

	/**
	 * 添加Listener
	 */
	private void addListeners() {
		pkgPathTxt.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				perference.setInfo(ProductModifyDialog.class.getName() + PKG_PATH, pkgPathTxt.getText());
			}
		});
		outputPathTxt.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				perference.setInfo(ProductModifyDialog.class.getName() + OUT_PATH, outputPathTxt.getText());
			}
		});
		outputRule.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				perference.setInfo(ProductModifyDialog.class.getName() + OUTPUT_RULE, outputRule.getText());
			}
		});
	}

	/**
	 * 更新表格内容
	 * 
	 * @param fileItem
	 *            fileItem
	 * @param status
	 *            状态
	 */
	private void updateTable(final FileItem fileItem, final String status) {
		Display.getDefault().asyncExec(new Runnable() {
			@Override
			public void run() {
				fileItem.setStatus(status);
				table.redraw();
			}
		});
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("开标文件一纸导出工具");
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		((GridLayout) parent.getLayout()).numColumns++;
		selectBtn = SwtUtil.createCheckBox(parent, "旧版本格式", new GridData());

		createButton(parent, IDialogConstants.OK_ID, "生成", true);
		createButton(parent, IDialogConstants.ABORT_ID, "添加文件", false);
		createButton(parent, IDialogConstants.BACK_ID, "移除文件", false);
		createButton(parent, IDialogConstants.CANCEL_ID, "关闭", false);
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(760, 480);
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
