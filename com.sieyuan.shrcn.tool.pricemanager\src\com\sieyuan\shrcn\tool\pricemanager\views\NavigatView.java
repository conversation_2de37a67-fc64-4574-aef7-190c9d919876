package com.sieyuan.shrcn.tool.pricemanager.views;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;

import com.sieyuan.shrcn.tool.pricemanager.composite.NavigatComposite;

/**
 * @Description:导航树view
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午11:06:05
 */
public class NavigatView {

    private static NavigatComposite item;

    public static void refreshTree() {
        Display.getDefault().syncExec(new Runnable() {
            public void run() {
                item.intNagat();
            }
        });
    }

    private Composite composite;

    public NavigatView(Composite parent) {
        composite = new Composite(parent, SWT.NONE);
        composite.setLayout(new GridLayout(1, false));
        item = new NavigatComposite(composite, SWT.NONE);
        item.setLayout(new GridLayout(1, false));
        item.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
    }

    public NavigatComposite getComposite() {
        return item;
    }

}
