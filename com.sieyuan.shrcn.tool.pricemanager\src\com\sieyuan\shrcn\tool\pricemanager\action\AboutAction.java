package com.sieyuan.shrcn.tool.pricemanager.action;

import com.shrcn.found.ui.action.MenuAction;
import com.shrcn.found.ui.dialog.AboutDialog;
import com.shrcn.found.ui.util.UIProperties;

/**
 * @Description:关于界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:39:04
 */
public class AboutAction extends MenuAction {

	public AboutAction(String text) {
		super(text);
	}

	@Override
	public void run() {
		AboutDialog dialog = new AboutDialog(getShell(), "报价管理工具   " + UIProperties.getInstance().getVersion() + "\n\n");
		dialog.open();
	}
}
