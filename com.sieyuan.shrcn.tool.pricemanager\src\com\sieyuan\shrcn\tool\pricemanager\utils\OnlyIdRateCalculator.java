package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.shrcn.found.common.util.StringUtil;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgAdjustDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.utils.CalcUtil;

/**
 * 独有ID比率计算器
 * 负责计算独有ID的比率和优先级
 */
public class OnlyIdRateCalculator {
    
    /**
     * 计算独有ID比率
     * 
     * @param adjustList 调价列表
     */
    public void calculateOnlyIdRates(List<PkgAdjust> adjustList) {
        if (adjustList == null || adjustList.isEmpty()) {
            return;
        }
        
        // 获取包映射
        Map<String, List<PkgInfo>> pkgMap = getPkgMap();
        
        // 获取所有包名
        List<String> pkgNames = new ArrayList<>();
        for (PkgAdjust adjust : adjustList) {
            pkgNames.add(adjust.getPkgname());
        }
        
        // 获取独有ID集合
        Set<String> onlyBidSet = FieldUtils.getOnlyBidSet(pkgNames);
        
        // 计算每个包的独有ID比率
        for (PkgAdjust adjust : adjustList) {
            calculatePackageOnlyIdRate(adjust, pkgMap, onlyBidSet);
        }
        
        // 按比率排序并设置优先级
        sortAndSetPriority(adjustList);
    }
    
    /**
     * 计算单个包的独有ID比率
     */
    private void calculatePackageOnlyIdRate(PkgAdjust adjust, Map<String, List<PkgInfo>> pkgMap, Set<String> onlyBidSet) {
        String pkgName = adjust.getPkgname();
        List<PkgInfo> pkgList = pkgMap.get(pkgName);
        
        if (pkgList == null || pkgList.isEmpty()) {
            adjust.setResult("0");
            return;
        }
        
        BigDecimal ror = new BigDecimal(adjust.getRor());
        BigDecimal ror2 = new BigDecimal(adjust.getRor2());
        BigDecimal targetPrice = new BigDecimal(adjust.getTargetprice());
        BigDecimal totalOnlyIdPrice = BigDecimal.ZERO;
        
        // 计算独有ID的总价格
        for (PkgInfo pkgInfo : pkgList) {
            if (onlyBidSet.contains(pkgInfo.getBidNo())) {
                if (StringUtil.isEmpty(pkgInfo.getTotalLimitPrice()) || pkgInfo.getTotalLimitPrice().equals("0")) {
                    BigDecimal onlyIdPrice = new BigDecimal(pkgInfo.getRealPrice())
                        .multiply(getPkgRor(pkgInfo, ror, ror2));
                    totalOnlyIdPrice = totalOnlyIdPrice.add(onlyIdPrice);
                }
            }
        }
        
        // 计算比率
        String result = "0";
        if (totalOnlyIdPrice.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal ratio = totalOnlyIdPrice.divide(targetPrice, 6, BigDecimal.ROUND_HALF_UP);
            result = ratio.toString();
        }
        
        adjust.setResult(result);
    }
    
    /**
     * 按比率排序并设置优先级
     */
    private void sortAndSetPriority(List<PkgAdjust> adjustList) {
        // 按比率降序排序
        Collections.sort(adjustList, new Comparator<PkgAdjust>() {
            @Override
            public int compare(PkgAdjust o1, PkgAdjust o2) {
                try {
                    double ratio1 = Double.parseDouble(o1.getResult());
                    double ratio2 = Double.parseDouble(o2.getResult());
                    return Double.compare(ratio2, ratio1); // 降序
                } catch (NumberFormatException e) {
                    return 0;
                }
            }
        });
        
        // 设置优先级
        int seq = 0;
        for (PkgAdjust adjust : adjustList) {
            if (!"0".equals(adjust.getResult())) {
                adjust.setSeq(seq++);
            }
        }
    }
    
    /**
     * 获取包映射
     */
    private Map<String, List<PkgInfo>> getPkgMap() {
        Map<String, List<PkgInfo>> pkgMaps = new HashMap<>();
        List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
        
        for (PkgInfo pkgInfo : pkgs) {
            pkgMaps.computeIfAbsent(pkgInfo.getName(), k -> new ArrayList<>()).add(pkgInfo);
        }
        
        return pkgMaps;
    }
    
    /**
     * 获取包微调系数
     */
    private BigDecimal getPkgRor(PkgInfo pkgInfo, BigDecimal ror, BigDecimal ror2) {
        String desc = pkgInfo.getProductDesc();
        if (desc != null && (desc.contains("220") || desc.contains("330") || 
            desc.contains("500") || desc.contains("750"))) {
            return ror2;
        }
        return ror;
    }
} 