/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * @Description:组件配置表
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-27 上午8:28:11
 */
public class PkgProduct {

    private Integer id;
    private String bidNo; // 编号
    private String costprice;// 成本价格
    private String count;
    private String devname;
    private String devtype;
    private String intelligence;
    private Integer lnType;
    private String lnTypeString;
    private String name;
    private String number;
    private String ocunnt;
    private String odevtype;
    private Integer orderid;
    private Integer parentid;
    private String searchdevtype;
    private String supply;
    private String quote;// 是否报价 1=是，2=否
    private String unit;
    private String area;
    private Integer rowId;

    private String price;
    private String totalprice;
    private String weight = "";

    public String getArea() {
        return area;
    }

    public String getBidNo() {
        return bidNo;
    }

    public String getCostprice() {
        return costprice;
    }

    public String getCount() {
        return count;
    }

    public String getDevname() {
        return devname;
    }

    public String getDevtype() {
        return devtype;
    }

    public Integer getId() {
        return id;
    }

    public String getIntelligence() {
        return intelligence;
    }

    public Integer getLnType() {
        return lnType;
    }

    public String getLnTypeString() {
        return lnTypeString;
    }

    public String getName() {
        return name;
    }

    public String getNumber() {
        return number;
    }

    public String getOcunnt() {
        return ocunnt;
    }

    public String getOdevtype() {
        return odevtype;
    }

    public Integer getOrderid() {
        return orderid;
    }

    public Integer getParentid() {
        return parentid;
    }

    public String getPrice() {
        return price;
    }

    public String getSearchdevtype() {
        return searchdevtype;
    }

    public String getSupply() {
        return supply;
    }

    public String getUnit() {
        return unit;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public void setBidNo(String bidNo) {
        this.bidNo = bidNo;
    }

    public void setCostprice(String costprice) {
        this.costprice = costprice;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public void setDevname(String devname) {
        this.devname = devname;
    }

    public void setDevtype(String devtype) {
        this.devtype = devtype;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setIntelligence(String intelligence) {
        this.intelligence = intelligence;
    }

    public void setLnType(Integer lnType) {
        this.lnType = lnType;
    }

    public void setLnTypeString(String lnTypeString) {
        this.lnTypeString = lnTypeString;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public void setOcunnt(String ocunnt) {
        this.ocunnt = ocunnt;
    }

    public void setOdevtype(String odevtype) {
        this.odevtype = odevtype;
    }

    public void setOrderid(Integer orderid) {
        this.orderid = orderid;
    }

    public void setParentid(Integer parentid) {
        this.parentid = parentid;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public void setSearchdevtype(String searchdevtype) {
        this.searchdevtype = searchdevtype;
    }

    public void setSupply(String supply) {
        this.supply = supply;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getRowId() {
        return rowId;
    }

    public void setRowId(Integer rowId) {
        this.rowId = rowId;
    }

    public String getQuote() {
        return quote;
    }

    public void setQuote(String quote) {
        this.quote = quote;
    }

    public String getTotalprice() {
        return totalprice;
    }

    public void setTotalprice(String totalprice) {
        this.totalprice = totalprice;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

}
