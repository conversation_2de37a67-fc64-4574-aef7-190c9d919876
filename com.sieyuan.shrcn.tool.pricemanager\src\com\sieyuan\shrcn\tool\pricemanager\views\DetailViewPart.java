package com.sieyuan.shrcn.tool.pricemanager.views;

import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.part.ViewPart;

import com.shrcn.found.common.event.Context;
import com.shrcn.found.common.event.EventManager;
import com.shrcn.found.common.event.IEventHandler;
import com.sieyuan.shrcn.tool.pricemanager.EventConstants;

/**
 * @Description:WordToolViewPart word工具viewpart
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON><PERSON>uan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午11:02:57
 */
public class DetailViewPart extends ViewPart implements IEventHandler {
    public static final String ID = "com.sieyuan.shrcn.tool.pricemanager.app.detailview";
    @SuppressWarnings("unused")
    private DetailView detailView;

    /**
     * This is a callback that will allow us to create the viewer and initialize it.
     */
    public void createPartControl(Composite parent) {
        EventManager.getDefault().registEventHandler(this);
        detailView = new DetailView(parent);
    }

    @Override
    public void execute(Context context) {
        String event = context.getEventName();
        if (EventConstants.SETPARTNAME.equals(event)) {
            if (context.getData() != null) {
                String partName = (String)context.getData();
                setPartName(partName);
            }
        }
    }

    /**
     * Passing the focus request to the viewer's control.
     */
    public void setFocus() {}
}
