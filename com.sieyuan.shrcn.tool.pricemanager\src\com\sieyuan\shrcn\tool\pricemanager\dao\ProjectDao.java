package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;

import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.Project;
import com.sieyuan.shrcn.tool.pricemanager.utils.Dom4jUtil;

/**
 * @Description:Project增加删除修改查询等
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-5 下午2:16:16
 */
public class ProjectDao {

    /**
     * 添加联系人 要求id要唯一
     * @param con
     * @throws IOException 
     * @throws DocumentException 
     */
    public static void addProject(Project con) {
        try {
            // 读取XML文件
            File file = new File(DirManager.getProjectFile());
            if (file.exists()) {
                file.createNewFile();
            }
            Document doc = Dom4jUtil.getDocument(file);

            // 添加信息
            Element rootEle = doc.getRootElement();
            // 根据根节点添加Project节点
            Element conEle = rootEle.addElement("project");
            // 编号使用UUID算法生成一个随机且唯一的字符串
            conEle.addAttribute("name", con.getProjectName());
            conEle.addElement("zb_id").addText(con.getZbId());
            conEle.addElement("tax_rate").addText(con.getTaxRate());

            // 将xml文件写入到磁盘中
            Dom4jUtil.writeXML(file, doc);
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据id删除联系人
     * @param id 
     * @throws DocumentException 
     */
    public static void deleteProject(String name) {
        try {
            // 读取XML文件
            File file = new File(DirManager.getProjectFile());
            Document doc = Dom4jUtil.getDocument(file);

            // Xpath技术,快速找出联系人
            Element conEle = (Element)doc.selectSingleNode("//project[@name='" + name + "']");
            doc.getRootElement().remove(conEle);
            // 将xml文件写入到磁盘中
            Dom4jUtil.writeXML(file, doc);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询联系人
     * @return XML文件所有联系人的集合
     * @throws DocumentException 
     */
    @SuppressWarnings("unchecked")
    public static List<Project> findAll() {
        // 定义一个集合用于装所有的联系人信息
        List<Project> conList = new ArrayList<Project>();
        // 读取XML文件
        File file = new File(DirManager.getProjectFile());
        Document doc = null;
        try {
            doc = Dom4jUtil.getDocument(file);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        // 获得XML文件中联系人列表
        List<Element> list = doc.getRootElement().elements("project");
        // 遍历联系人列表
        for (Element conEle : list) {
            Project con = new Project();
            con.setProjectName(conEle.attributeValue("name"));
            con.setZbId(conEle.elementText("zb_id"));
            con.setTaxRate(conEle.elementText("tax_rate"));
            // 每封装一个联系人信息，就添加到集合中
            conList.add(con);
        }
        return conList;
    }

    public static void main(String[] args) {
        Project project = new Project();

        // 向XML文件中添加联系人
        project.setProjectName("123");
        project.setTaxRate("123");
        project.setZbId("123");
        addProject(project);
        deleteProject("123");
    }

    /**
     * 修改联系人
     * @param con
     * @throws DocumentException 
     */
    public static void updateProject(Project con) throws DocumentException {
        // 读取XML文件
        File file = new File(DirManager.getProjectFile());
        Document doc = Dom4jUtil.getDocument(file);
        String name = con.getProjectName();
        // XPath技术，快速找出联系人
        Element conEle = (Element)doc.selectSingleNode("//project[@name='" + name + "']");
        conEle.element("zb_id").setText(con.getZbId());
        conEle.element("tax_rate").setText(con.getTaxRate());
        // 将xml文件写入到磁盘中
        Dom4jUtil.writeXML(file, doc);
    }
}