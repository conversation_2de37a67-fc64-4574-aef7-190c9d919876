package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.List;

import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;

public class LogUtils {

    public static void recordErrInfo(List<String> logs) {
        try {
            // 创建输出文件 result.txt
            File file = new File(DirManager.getPriceLogRecordFile());
            OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(file, true));
            for (String log : logs) {
                writer.write(log + "\n");
                ConsoleManager.getInstance().append(log);
            }
            writer.flush();
            writer.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
