package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * 按照权重拆分报价
 * 
 * <AUTHOR>
 * @version 1.0, 2020-12-2
 * 
 */
public class PriceRate {

	// 主键
	private Integer id;

	// 分包编号
	private String pkgId = "";

	// 分包名称
	private String pkgName = "";

	// 轮次
	private String round = "";

	// 附件名称
	private String attachment = "";

	// 基准物料编码
	private String code = "";

	// 基准物料名称
	private String productName = "";

	// 技术规范书ID
	private String pid = "";

	// 网省采购申请行号
	private String orderid = "";

	// 项目单位
	private String company = "";

	// 单位
	private String unit = "";

	// 扩展描述
	private String extendedDesc = "";

	// 包限价(万) 行限价(万)
	private String limitPrice = "";

	// 行限价(万)
	private String rowLimitPrice = "";

	// 数量
	private String count = "";

	// 辅参ID
	private String fid = "";

	// 辅参数组合
	private String funit = "";

	// 价格配置因子（%）
	private String rate = "";

	// 辅参数组合限价（万）
	private String fLimitPrice = "";

	// 辅参未含税单价（万）
	private String fprice = ""; // 更新

	// 基准物料未含税单价（万）
	private String bprice = "";

	// 税率（%）
	private String taxrate = ""; // 更新

	// 辅参含税单价（万）
	private String fWithPrice = "";

	// 基准物料含税单价（万）
	private String bWithPrice = "";

	// 基准物料未含税合价（万）
	private String bTotalPrice = "";

	// 基准物料含税合价（万）
	private String bTotalWithPrice = "";

	// 浮动比例
	private String floatRate = "";

	// 算术平均值偏差10%
	private String valid = "";
	private String avg = "";
	// 加权平均值10%
	private String pavg = "";
	private String pvalid = "";
	
	private String allvalid = "";

	// 总和的最大值、最小值
	private String max;
	private String min;

	// 单价的最大值、最小值
	private String pmax;
	private String pmin;

	public String getPkgId() {
		return pkgId;
	}

	public void setPkgId(String pkgId) {
		this.pkgId = pkgId;
	}

	public String getPkgName() {
		return pkgName;
	}

	public void setPkgName(String pkgName) {
		this.pkgName = pkgName;
	}

	public String getRound() {
		return round;
	}

	public void setRound(String round) {
		this.round = round;
	}

	public String getAttachment() {
		return attachment;
	}

	public void setAttachment(String attachment) {
		this.attachment = attachment;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getPid() {
		return pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
	}

	public String getOrderid() {
		return orderid;
	}

	public void setOrderid(String orderid) {
		this.orderid = orderid;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getExtendedDesc() {
		return extendedDesc;
	}

	public void setExtendedDesc(String extendedDesc) {
		this.extendedDesc = extendedDesc;
	}

	public String getLimitPrice() {
		return limitPrice;
	}

	public void setLimitPrice(String limitPrice) {
		this.limitPrice = limitPrice;
	}

	public String getRowLimitPrice() {
		return rowLimitPrice;
	}

	public void setRowLimitPrice(String rowLimitPrice) {
		this.rowLimitPrice = rowLimitPrice;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	public String getFid() {
		return fid;
	}

	public void setFid(String fid) {
		this.fid = fid;
	}

	public String getFunit() {
		return funit;
	}

	public void setFunit(String funit) {
		this.funit = funit;
	}

	public String getRate() {
		return rate;
	}

	public void setRate(String rate) {
		this.rate = rate;
	}

	public String getfLimitPrice() {
		return fLimitPrice;
	}

	public void setfLimitPrice(String fLimitPrice) {
		this.fLimitPrice = fLimitPrice;
	}

	public String getFprice() {
		return fprice;
	}

	public void setFprice(String fprice) {
		this.fprice = fprice;
	}

	public String getBprice() {
		return bprice;
	}

	public void setBprice(String bprice) {
		this.bprice = bprice;
	}

	public String getTaxrate() {
		return taxrate;
	}

	public void setTaxrate(String taxrate) {
		this.taxrate = taxrate;
	}

	public String getfWithPrice() {
		return fWithPrice;
	}

	public void setfWithPrice(String fWithPrice) {
		this.fWithPrice = fWithPrice;
	}

	public String getbWithPrice() {
		return bWithPrice;
	}

	public void setbWithPrice(String bWithPrice) {
		this.bWithPrice = bWithPrice;
	}

	public String getbTotalPrice() {
		return bTotalPrice;
	}

	public void setbTotalPrice(String bTotalPrice) {
		this.bTotalPrice = bTotalPrice;
	}

	public String getbTotalWithPrice() {
		return bTotalWithPrice;
	}

	public void setbTotalWithPrice(String bTotalWithPrice) {
		this.bTotalWithPrice = bTotalWithPrice;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getFloatRate() {
		return floatRate;
	}

	public void setFloatRate(String floatRate) {
		this.floatRate = floatRate;
	}

	public String getAvg() {
		return avg;
	}

	public void setAvg(String avg) {
		this.avg = avg;
	}

	public String getPavg() {
		return pavg;
	}

	public void setPavg(String pavg) {
		this.pavg = pavg;
	}

	public String getValid() {
		return valid;
	}

	public void setValid(String valid) {
		this.valid = valid;
	}

	public String getPvalid() {
		return pvalid;
	}

	public void setPvalid(String pvalid) {
		this.pvalid = pvalid;
	}

	public String getMax() {
		return max;
	}

	public void setMax(String max) {
		this.max = max;
	}

	public String getMin() {
		return min;
	}

	public void setMin(String min) {
		this.min = min;
	}

	public String getPmax() {
		return pmax;
	}

	public void setPmax(String pmax) {
		this.pmax = pmax;
	}

	public String getPmin() {
		return pmin;
	}

	public void setPmin(String pmin) {
		this.pmin = pmin;
	}

	public String getAllvalid() {
		return allvalid;
	}

	public void setAllvalid(String allvalid) {
		this.allvalid = allvalid;
	}

}
