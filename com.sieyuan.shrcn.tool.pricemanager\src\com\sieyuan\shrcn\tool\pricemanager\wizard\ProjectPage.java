package com.sieyuan.shrcn.tool.pricemanager.wizard;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.jface.wizard.WizardPage;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;

import com.sieyuan.shrcn.tool.pricemanager.dao.ProjectDao;
import com.sieyuan.shrcn.tool.pricemanager.model.Project;

public class ProjectPage extends WizardPage {
    // 此监听器，每次文本框里的文本变动都会触发，触发频度较大
    private class MyModifyListener implements ModifyListener {
        public void modifyText(ModifyEvent e) {
            setPageComplete(false); // 先使“完成”和“下一步”两按钮无效
            // 用Apache Commons Lang的StringUtils可以检查出全角空格
            name = nameText.getText().trim();// 设回实例变量name
            if (name.equals("")) {
                setErrorMessage("工程名称不能为空！");
            } else if (allNames.contains(name)) {
                setErrorMessage("工程名称已经存在！");
            } else {
                // 前面的检查都通过后......
                setErrorMessage(null); // 消除对话框上的出错提示
                setPageComplete(true); // 使“完成”和“下一步”两按钮可用
            }

        }
    }

    private List<String> allNames = new ArrayList<>();
    private String name;

    private Text nameText;

    // 必须继承父类的构造函数
    protected ProjectPage(String pageName) {
        super(pageName);
    }

    // 改写自父类的方法，在此方法中构建页面上的界面组件。注意不要在传入参数parent基础直接创建界面元素，而应在一个新面板topComp上创建
    public void createControl(Composite parent) {
        // 每页的提示信息
        setTitle("工程信息");
        setMessage("请输入工程名称", INFORMATION);
        // 创建一个页面组件的底层面板，并使用GridLayout布局
        Composite topComp = new Composite(parent, SWT.NULL);
        topComp.setLayout(new GridLayout());
        nameText = new Text(topComp, SWT.BORDER);
        nameText.setLayoutData(new GridData(GridData.FILL_HORIZONTAL));
        // 给两文本框加入监听器
        MyModifyListener listener = new MyModifyListener();
        nameText.addModifyListener(listener);
        // 必须要的一行
        setControl(topComp);
        List<Project> proList = ProjectDao.findAll();
        allNames = new ArrayList<>();
        for (Project project : proList) {
            allNames.add(project.getProjectName());
        }
        setPageComplete(false); // 使“完成”和“下一步”两按钮可用
    }

    // --------相应的Setter/Getter方法 ---------------
    public String getName() {
        return name;
    }

    public void setName(String string) {
        name = string;
    }

}