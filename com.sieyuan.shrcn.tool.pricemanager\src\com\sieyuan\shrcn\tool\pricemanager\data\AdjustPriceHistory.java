package com.sieyuan.shrcn.tool.pricemanager.data;

import java.io.File;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;

import com.shrcn.found.file.xml.DOM4JNodeHelper;
import com.shrcn.found.file.xml.XMLFileManager;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;

/**
 * @Description 调价历史
 * <AUTHOR>
 */
public class AdjustPriceHistory {
	
	/*
	 * 历史节点名称
	 */
	private static final String HIS = "history";
	/*
	 * id属性名
	 */
	private static final String ID = "id";
	/*
	 * name属性名
	 */
	private static final String NAME = "name";
	/*
	 * path属性名
	 */
	private static final String PATH = "path";
	
	/*
	 * id属性		历史id
	 */
	private volatile int id;
	/*
	 * name属性	历史名称
	 */
	private String name;
	/**
	 * path属性	历史数据db地址
	 */
	private String path;
    
	
	
    public int getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}
	

	/**
	 * 保存报价历史
	 * @param project 项目名称
	 */
	public void savePriceHistory(String project) {
		hisId(project);
		// 1. 数据库文件备份
		if (StringUtils.isEmpty(project)) {
			return;
		}
		String hisPath = DirManager.getHistoryDBFile(project);
		try {
			FieldUtils.copyFile(DirManager.getProjectFile(project), hisPath);
		} catch (Exception e) {
			e.printStackTrace();
		}
		// 2. 记录备份历史至xml文件
		this.name = hisPath.substring(hisPath.lastIndexOf("\\") + 1, hisPath.lastIndexOf("."));
		this.path = hisPath;
		String hisXml = DirManager.getHistoryRecordFile(project);
		Document doc = XMLFileManager.loadXMLFile(hisXml);
		Element root = doc.getRootElement();
		Element his = root.addElement(HIS);
		DOM4JNodeHelper.addAttributes(his, this, ID, NAME, PATH);
		XMLFileManager.writeDoc(doc, hisXml);
	}
	
	/**
	 * 获取项目下调价历史名称集合
	 * @param project 项目名称
	 * @return	调价历史名称集合
	 */
	public static List<String> getAllHisNames(String project) {
		try {
			String hisXml = DirManager.getHistoryRecordFile(project);
			Element root = DOM4JNodeHelper.getRootElement(hisXml);
			List<String> hisNames = DOM4JNodeHelper.getAttributes(root, HIS, NAME);
			Collections.reverse(hisNames);
			return hisNames;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return Collections.emptyList();
	}
	
	/**
	 * 通过名称选择历史
	 * @param project 项目名称
	 * @param hisName 历史名称
	 * @return	是否存在历史记录
	 */
	public static boolean selectByName(String project, String hisName) {
		if (StringUtils.isBlank(project) || StringUtils.isBlank(hisName)) {
			return false;
		}
		try {
			FieldUtils.copyFile(getHisPathByName(project, hisName), DirManager.getProjectFile(project));
			return true;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	/**
	 * 根据历史名称删除历史记录
	 * @param project 项目名称
	 * @param hisName 历史名称
	 */
	public static void deleteHis(String project, String hisName) {
		try {
			FieldUtils.delDir(new File(getHisPathByName(project, hisName)));
		} catch (Exception e) {
			e.printStackTrace();
		}
		// 删除xml历史记录
		String hisXml = DirManager.getHistoryRecordFile(project);
		Document doc = XMLFileManager.loadXMLFile(hisXml);
		Element root = doc.getRootElement();
		Element selectElement = selectElement(root, project, hisName);
		root.remove(selectElement);
		XMLFileManager.writeDoc(doc, hisXml);
	}
	
	/**
	 * 根据历史名称查询历史地址
	 * @param project 项目名称
	 * @param hisName 历史名称
	 * @return	历史数据db地址
	 */
	private static String getHisPathByName(String project, String hisName) {
		try {
			return selectElement(project, hisName).attributeValue(PATH);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/**
	 * 根据历史记录名称查询xml节点
	 * @param project 项目名称
	 * @param hisName 历史名称
	 * @return	xml节点
	 */
	private static Element selectElement(String project, String hisName) {
		return selectElement(null, project, hisName);
	}
	
	private static Element selectElement(Element root, String project, String hisName) {
		if (null == root) {
			String hisXml = DirManager.getHistoryRecordFile(project);
			root = DOM4JNodeHelper.getRootElement(hisXml);
		}
		List<Element> hisElements = DOM4JNodeHelper.selectNodes(root, HIS);
		for (Element element : hisElements) {
			String value = element.attributeValue(NAME);
			if (!StringUtils.isEmpty(value) && StringUtils.equals(value, hisName)) {
				return element;
			}
		}
		return null;
	}
	
	/**
	 * 刷新历史id
	 * @param project 项目名称
	 */
	private void hisId(String project) {
		if (this.id == 0) {
			this.id = getHisLastId(project);
		}
		this.id++;
	}
	
	/**
	 * 获取历史最后一个记录id
	 * @param project 项目名称
	 * @return	历史记录最后一个id
	 */
	private int getHisLastId(String project) {
		String hisXml = DirManager.getHistoryRecordFile(project);
		Element root = DOM4JNodeHelper.getRootElement(hisXml);
		List<String> idList = DOM4JNodeHelper.getAttributes(root, HIS, ID);
		if (CollectionUtils.isEmpty(idList)) {
			return 0;
		}
		String maxId = Collections.max(idList);
		return Integer.parseInt(maxId);
	}
	
}
