/**
 * Copyright (c) 2007-2010 上海思弘瑞电力控制技术有限公司. All rights reserved. This program is an eclipse Rich Client Application
 * based on IEC61850 SCT.
 */
package com.sieyuan.shrcn.tool.pricemanager.views.table;

import com.shrcn.found.ui.model.AUIConfig;
import com.shrcn.found.ui.model.TableManager;

/**
 * 
 * <AUTHOR>
 * @version 1.0, 2013-4-8
 */
/**
 * $Log: UIConfig.java,v $
 * Revision 1.2  2013/06/14 01:37:43  cchun
 * Refactor:提取 TableManager
 *
 * Revision 1.1  2013/05/17 06:53:07  cxc
 * Refactor：修改UIConfig
 *
 * Revision 1.2  2013/04/12 09:12:41  scy
 * Update：删除无用属性
 *
 * Revision 1.1  2013/04/10 06:21:09  zsy
 * Add:创建表格和表单
 *
 */
public class UIConfig extends AUIConfig {

    private static UIConfig inst = null;

    public static UIConfig getInstance() {
        if (inst == null) {
            inst = new UIConfig();
        }
        return inst;
    }

    private UIConfig() {
        this.tableManager = new TableManager(UIConfig.class, UIConfig.class.getPackage().getName() + ".uiconfig");
    }
}
