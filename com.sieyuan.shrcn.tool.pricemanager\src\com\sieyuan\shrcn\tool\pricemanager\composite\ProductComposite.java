/*
 * @(#) TemplatesComposite.java
 * 
 * Copyright (c) 2008 - 2012 上海思源弘瑞电力自动化有限公司. All rights reserved. 基于 Eclipse E4 a next generation platform (e.g., the
 * CSS styling, dependency injection, Modeled UI) Rich Client Application 开发的平台工具软件.
 */
package com.sieyuan.shrcn.tool.pricemanager.composite;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.events.KeyEvent;
import org.eclipse.swt.events.KeyListener;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.graphics.Image;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.shrcn.found.common.event.Context;
import com.shrcn.found.common.event.EventManager;
import com.shrcn.found.common.event.IEventHandler;
import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.UIConstants;
import com.shrcn.found.ui.table.TableExcelExportor;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shr.u21.ui.table.Table;
import com.sieyuan.shrcn.tool.pricemanager.EventConstants;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgErrInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.data.IconManager;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.dialog.PriceSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.model.TreeData;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;
import com.sieyuan.shrcn.tool.pricemanager.views.table.ProductInfoTableMode;

/**
 * @Description:组件配置表
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-30 下午5:08:01
 */

public class ProductComposite extends Composite  implements IEventHandler {
	
    private UIPreferences perference = UIPreferences.newInstance();
    private String IN_TAX = ".inTax";
    
    public static final Image price404 = IconManager.price404;
    private List<Integer> allzero = new ArrayList<>();
    private Integer bidNo;
    private PriceSettingDialog priceSettingDialog;
    private ProductInfoTableMode productInfoTableMode;
    private List<PkgProduct> productList = new ArrayList<>();
    private Table table;
    private TreeData treeData;

    public ProductComposite(CTabFolder parent, TreeData treeData, int none) {
        super(parent, SWT.NONE);
        this.treeData = treeData;
        priceSettingDialog = new PriceSettingDialog(this.getShell());
        createCompisiteArea(this);
        
        EventManager.getDefault().registEventHandler(this);
    }
    
    @Override
    public void dispose() {
    	super.dispose();
    	
        EventManager.getDefault().removeEventHandler(this);
    }

	private void createCompisiteArea(Composite container) {
		container.setLayout(new GridLayout(6, false));
		Label gridlabel = new Label(container, SWT.NONE);
		gridlabel.setLayoutData(new GridData(SWT.LEFT, SWT.FILL, false, false, 6, 1));
		gridlabel.setText(ToolConstants.TECH_DOCX);

		table = new Table(container, UIConstants.KTABLE_CELL_STYLE);
		table.setLayout(new GridLayout(1, false));
		GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 6, 10);
		gd_table.widthHint = 479;
		table.setLayoutData(gd_table);
		productInfoTableMode = new ProductInfoTableMode() {
			@Override
			public void doSetContentAt(int col, int row, Object value) {
				PkgProduct pkgProduct = (PkgProduct) productInfoTableMode.getItems().get(row - 1);
				if (col == 5) {
					if (pkgProduct.getOdevtype().equals(value))
						return;
					pkgProduct.setOdevtype((String) value);
				} else if (col == 6) {
					if (pkgProduct.getOcunnt().equals(value))
						return;
					pkgProduct.setOcunnt((String) value);
				} else if (col == 7) {
					if (pkgProduct.getSupply().equals(value))
						return;
					pkgProduct.setSupply((String) value);
				} else if (col == 9) {
					if (pkgProduct.getQuote().equals(value))
						return;
					pkgProduct.setQuote((String) value);
				} else if (col == 10) {
					if (pkgProduct.getSearchdevtype().equals(value))
						return;
					pkgProduct.setSearchdevtype((String) value);
				} else if (col == 12) {
					if (pkgProduct.getCostprice().equals(value))
						return;
					pkgProduct.setCostprice((String) value);
				} else if (col == 13) {
					if (pkgProduct.getPrice().equals(value))
						return;
					pkgProduct.setPrice((String) value);
				}

				if (pkgProduct.getQuote().equals("是")) {
					// 如果为外购
					if (pkgProduct.getLnType() == 1) {
						String inWight = perference.getInfo(EnvirmentSettingDialog.class.getName() + IN_TAX);
						if (StringUtils.isEmpty(inWight)) {
							inWight = "2";
						}
						pkgProduct.setWeight(inWight);
					} else {
						pkgProduct.setWeight("1");
					}
				} else {
					pkgProduct.setWeight("");
				}

				List<PkgProduct> pkgProductList = new ArrayList<>();
				pkgProductList.add(pkgProduct);

				PkgProductDao.updatePkgProductPrice(pkgProductList);
				refreshTable();
				if (allzero.size() == 0) {
					// 如果全部可以找到价格，删除错误数据
					PkgErrInfoDao.delPkgErrInfo(2, productList.get(0).getParentid());
					NavigatView.refreshTree();
				}
			}
		};
		table.setModel(productInfoTableMode);

		int type = treeData.getType();
		if (type == 7) {
			String[] array = treeData.getKey().split(ToolConstants.CONNECT);
			gridlabel.setText("【" + array[1] + "】" + ToolConstants.TECH_DOCX);
			bidNo = Integer.valueOf(array[0]);
			refreshTable();
		}

		table.addKeyListener(new KeyListener() {
			@Override
			public void keyPressed(KeyEvent e) {
				if (e.keyCode == 9) {
					GlobalData.getInstance().setShow(!GlobalData.getInstance().getShow());
					refreshTable();
				}
			}

			@Override
			public void keyReleased(KeyEvent e) {
			}
		});

		table.addMouseListener(new MouseListener() {

			@Override
			public void mouseUp(MouseEvent e) {
				// TODO 自动生成的方法存根

			}

			@Override
			public void mouseDown(MouseEvent e) {
				// TODO 自动生成的方法存根

			}

			@Override
			public void mouseDoubleClick(MouseEvent e) {

				Point[] points = table.getCellSelection();
				int col = points[0].x;
				if (col == 10 || col == 12) {
					updateDevType();
				}

			}
		});
	}

    public void updateDevType(){

		if (priceSettingDialog != null && priceSettingDialog.getOpen() == false) {
			priceSettingDialog.open();
		} else {
			return;
		}
		CostPrice costprice = priceSettingDialog.getCostPrice();
		if (costprice != null) {
			Point[] points = table.getCellSelection();
			int row = points[0].y;
			PkgProduct pkgProduct = (PkgProduct) productInfoTableMode.getItems().get(row - 1);
			String cost = pkgProduct.getCostprice();
			pkgProduct.setSearchdevtype(costprice.getDevtype());
			pkgProduct.setCostprice(costprice.getPrice());
			pkgProduct.setPrice(costprice.getPrice());
			pkgProduct.setArea(costprice.getArea());
			pkgProduct.setCostprice(costprice.getCostPrice());
			pkgProduct.setLnType(costprice.getType());
			pkgProduct.setSupply(costprice.getSupply());
			List<PkgProduct> pkgProductList = new ArrayList<>();
			pkgProductList.add(pkgProduct);

			// 相同ID的一起更新
			// if (!StringUtil.isEmpty(pkgProduct.getOdevtype())) {
			// List<PkgProduct> tmpList =
			// PkgProductDao.getPkgProductByDevTypeAndBidNo(pkgProduct.getBidNo(),
			// pkgProduct.getOdevtype());
			// for (PkgProduct pr : tmpList) {
			// pr.setSearchdevtype(costprice.getDevtype());
			// pr.setCostprice(costprice.getPrice());
			// pr.setPrice(costprice.getPrice());
			// pr.setCostprice(costprice.getCostPrice());
			// pr.setLnType(costprice.getType());
			// pr.setSupply(costprice.getSupply());
			// pkgProductList.add(pr);
			// }
			// }

			PkgProductDao.updatePkgProductPrice(pkgProductList);
			refreshTable();
			if (StringUtil.isEmpty(cost) && allzero.size() == 0) {
				// 如果全部可以找到价格，删除错误数据
				for (PkgProduct pr : pkgProductList) {
					PkgErrInfoDao.delPkgErrInfo(2, pr.getParentid());
				}
				NavigatView.refreshTree();
			}
		}
	
    }
    
    @Override
    public void execute(Context context) {
        String event = context.getEventName();
        if (EventConstants.EXPORTPRODUCT.equals(event)) {
        	
    		int type = treeData.getType();
    		if (type == 7) {
    			PkgInfo pkg = (PkgInfo)treeData.getData();
            	TableExcelExportor.export("【" +  pkg.getName() + "_" + pkg.getBidNo() + "】" + ToolConstants.TECH_DOCX, productInfoTableMode);
    		}
        }
    }
	
    /**
     * 刷新表格删除错误数据
     */
    private void refreshTable() {
        productList = PkgProductDao.getPkgProductByParentid(bidNo);
        if (productList != null && productList.size() != 0) {
            if (GlobalData.getInstance().getShow() == false) {
                allzero = new ArrayList<>();
                productInfoTableMode.setItems(productList);
                for (int i = 0; i < productList.size(); i++) {
                    if (productList.get(i) != null && !StringUtil.isEmpty(productList.get(i).getOcunnt())
                        && !productList.get(i).getOcunnt().equals("0") && productList.get(i).getQuote().equals("是")
                        && StringUtil.isEmpty(productList.get(i).getCostprice())) {
                        allzero.add(i + 1);
                    }
                }
                productInfoTableMode.setAllZero(allzero);
            } else {
                List<PkgProduct> pkgProducts = new ArrayList<>();
                for (PkgProduct pkgProduct : productList) {
                    if (StringUtil.isEmpty(pkgProduct.getOcunnt()) || pkgProduct.getOcunnt().equals("0")) {
                    } else {
                        pkgProducts.add(pkgProduct);
                    }
                }
                allzero = new ArrayList<>();
                for (int i = 0; i < pkgProducts.size(); i++) {
                    if (pkgProducts.get(i) != null && !StringUtil.isEmpty(pkgProducts.get(i).getOcunnt())
                        && !pkgProducts.get(i).getOcunnt().equals("0") && pkgProducts.get(i).getQuote().equals("是")
                        && StringUtil.isEmpty(pkgProducts.get(i).getCostprice())) {
                        allzero.add(i + 1);
                    }
                }
                productInfoTableMode.setItems(pkgProducts);
                productInfoTableMode.setAllZero(allzero);
            }
        } else {
            table.setVisible(false);
            this.setBackgroundImage(price404);
        }
        // productInfoTableMode.setColumnWidth(8, 0);
        table.redraw();
    }

}
