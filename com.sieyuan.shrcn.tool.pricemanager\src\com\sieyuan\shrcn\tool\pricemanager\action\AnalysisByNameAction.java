package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.dialog.AnalysisByNameDialog;

/**
 * @Description:统计界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:39:02
 
 */
public class AnalysisByNameAction extends MenuAction {

    public AnalysisByNameAction(String text) {
        super(text);
		setAccelerator(SWT.CTRL + 'R');
    }

    @Override
    public void run() {
        AnalysisByNameDialog expDlg = new AnalysisByNameDialog(Display.getDefault().getActiveShell());
        expDlg.open();
    }
}
