/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.TimeCounter;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgTotalDao;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgTotal;
import com.sieyuan.shrcn.tool.pricemanager.utils.ExcelExportUtil;
import com.sieyuan.shrcn.tool.pricemanager.utils.SXSSFWorkbookUtil;

/**
 * @Description:招标文件导出
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 */
public class PkgProductOutputDialog extends WrappedDialog {

    private Combo combo;

    private String DIR = ".outRoot";
    private Text diretoryRoot;
    @SuppressWarnings("unused")
	private String ID = ".id";
    @SuppressWarnings("unused")
	private List<Map<String, Object>> mapList = new ArrayList<>();
    private String name; // 名称
    private UIPreferences perference = UIPreferences.newInstance();
    private Map<String, List<PkgInfo>> pkgmap;
    private Map<Integer, List<PkgProduct>> prsmap;
    private Integer type; // 导出的类型，全部或者其他

    public PkgProductOutputDialog(Shell parentShell, Integer type, String name) {
        super(parentShell);
        this.type = type;
        this.name = name;
        prsmap = new HashMap<>();
        pkgmap = new HashMap<>();
    }

    @Override
    protected void buttonPressed(int buttonId) {

        if (buttonId == OK) {
            String infopath = getClass().getName();
            final String root = diretoryRoot.getText();
            final String selected = combo.getText();
            perference.setInfo(infopath + DIR, root);

            ProgressManager.execute(new IRunnableWithProgress() {
                @Override
                public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
                    monitor.beginTask("正在导出数据中，请稍候...", 100000);
                    TimeCounter.begin();
                    export(root, selected, monitor);
                    TimeCounter.end("导出总耗时");
                    ConsoleManager.getInstance().append("导出开标文件完成！");
                    monitor.done();
                }
            });
        }
        super.buttonPressed(buttonId);
    }

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("导出组件材料配置表");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "确定", true);
        createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        Composite container = (Composite)super.createDialogArea(parent);
        container.setLayout(new GridLayout(3, false));
        combo = createSheetCombo(container, "包号：");

        diretoryRoot = SwtUtil.createDirectorySelector(container, "输出文件夹：", "*.docx");;
        GridData gdList = new GridData(GridData.FILL_HORIZONTAL);
        gdList.horizontalSpan = 3;
        init();
        return container;
    }

    private Combo createSheetCombo(Composite container, String title) {
        SwtUtil.createLabel(container, title, new GridData());
        Combo cb = SwtUtil.createCombo(container, new GridData());
        final GridData layoutData = new GridData();
        layoutData.horizontalSpan = 2;
        layoutData.widthHint = 80;
        cb.setLayoutData(layoutData);
        return cb;
    }

    /**
     * 导出开标文件数据
     * @param path 路径
     * @param selected 
     * @param monitor 进度信息
     */
	private void export(String path, String selected, IProgressMonitor monitor) {
		List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
		List<PkgInfo> pkglsit;
		for (PkgInfo pkgInfo : pkgs) {
			pkglsit = new ArrayList<>();
			if (pkgmap.containsKey(pkgInfo.getName())) {
				pkglsit = pkgmap.get(pkgInfo.getName());
				if (pkglsit == null) {
					pkglsit = new ArrayList<>();
				}
			}
			pkglsit.add(pkgInfo);
			pkgmap.put(pkgInfo.getName(), pkglsit);
		}

		List<PkgTotal> pkgTotals = PkgTotalDao.getPkgTotal();
		Map<String, String> pkgTotalMaps = new HashMap<>();
		for (PkgTotal pkgTotal : pkgTotals) {
			pkgTotalMaps.put(pkgTotal.getPkgname(), pkgTotal.getPrice());
		}

		List<PkgProduct> prs = PkgProductDao.getPkgProductByParentid(null);
		List<PkgProduct> prlsit;
		for (PkgProduct pkgProduct : prs) {
			prlsit = new ArrayList<>();
			if (prsmap.containsKey(pkgProduct.getParentid())) {
				prlsit = prsmap.get(pkgProduct.getParentid());
				if (prlsit == null) {
					prlsit = new ArrayList<>();
				}
			}
			prlsit.add(pkgProduct);
			prsmap.put(pkgProduct.getParentid(), prlsit);
		}

		if (selected.equals("全部")) {
			Set<String> pkgnames = pkgmap.keySet(); // 取出所有的key值
			for (String pkgname : pkgnames) {
				List<PkgInfo> list = pkgmap.get(pkgname);
				int count = 0;
				for (PkgInfo pkginfo : list) {
					count++;
					SXSSFWorkbookUtil.reset();
					exportSingle(path, pkginfo, count);
					monitor.worked(100000 / pkgs.size());
				}
				SXSSFWorkbookUtil.reset();

			}
		}
	}

    private void exportSingle(String path, PkgInfo pkgInfo, int count) {
        List<PkgProduct> prs = prsmap.get(pkgInfo.getId());
        if (prs != null && prs.size() > 0) {
            String dir = path + File.separator + pkgInfo.getName();
            File dirFolder = new File(dir);
            if (!dirFolder.exists()) {
                dirFolder.mkdirs();
            }
            String filepath = "";
            filepath = dir + File.separator + (count + "_" + pkgInfo.getBidNo()) + ".xlsx";
            ExcelExportUtil.exportPkgProduct(filepath, prs, count + "_" + pkgInfo.getBidNo() + "_" + pkgInfo.getProductDesc() + "_組件材料配置表");
        }
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(600, 220);
    }

    private void init() {
        String infopath = getClass().getName();
        String diretoryPath = perference.getInfo(infopath + DIR);
        if(StringUtils.isEmpty(diretoryPath)){
            diretoryPath = DirManager.getOutputDir(GlobalData.getInstance().getProjectName()) + File.separator + "组件材料表";;
        }
        diretoryRoot.setText(diretoryPath);
        loadPackages();
    }

    private void loadPackages() {
        if (type == 1) {
            List<String> names = new ArrayList<>();
            names.add("全部");
            combo.setItems(names.toArray(new String[names.size()]));
            combo.setText("全部");
        } else if (type == 2) {
            List<String> names = new ArrayList<>();
            names.add(name);
            combo.setItems(names.toArray(new String[names.size()]));
            combo.setText(name);
        } else if (type == 3) {
            List<String> names = new ArrayList<>();
            names.add("全部");
            combo.setItems(names.toArray(new String[names.size()]));
            combo.setText("全部");
        }
    }

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
