package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.ui.app.WrappedTitleAreaDialog;
import com.shrcn.found.ui.table.Messages;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgTotalDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgBidInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgTotal;
import com.sieyuan.shrcn.tool.pricemanager.utils.ExcelExportUtil;
import com.sieyuan.shrcn.tool.pricemanager.views.table.TableFactory;

import de.kupzog.ktable.KTableModel;

/**
 * 一键导出统计结果
 * 
 * <AUTHOR>
 * 
 */
public class OneKeyAnalysisDialog extends WrappedTitleAreaDialog {

	private RKTable pkgTb;

	public OneKeyAnalysisDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == OK) {
			Shell shell = Display.getDefault().getActiveShell();
			final String directory = DialogHelper.selectDirectory(shell, SWT.SAVE, "设置文件保存文件", "请设置文件保存文件");
			if (StringUtils.isEmpty(directory)) {
				return;
			}
			ProgressManager.execute(new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {

					String nameFile = directory + File.separator + "报价结果统计.xlsx";
					String idFile = directory + File.separator + "ID报价统计.xlsx";

					exportTableData("报价结果", nameFile, pkgTb.getTablemodel(), monitor);
					ConsoleManager.getInstance().append(nameFile + "导出成功。");

					List<PkgBidInfo> bidInfoList = PkgInfoDao.getPkgBidInfos();
					ExcelExportUtil.exportIDReport(idFile, bidInfoList);
					ConsoleManager.getInstance().append(idFile + "导出成功。");

					DialogHelper.showAsynInformation("一键导出统计结果成功");

					monitor.done();
				}
			}, false);
			return;
		}
		super.buttonPressed(buttonId);
	}

	/**
	 * 创建单元格样式
	 * 
	 * @param sxssfWorkbook
	 * @return
	 */
	private static CellStyle getCellStyle(SXSSFWorkbook sxssfWorkbook) {
		CellStyle style = sxssfWorkbook.createCellStyle();
		style.setBorderBottom(BorderStyle.THIN); // 下边框
		style.setBorderLeft(BorderStyle.THIN);// 左边框
		style.setBorderTop(BorderStyle.THIN);// 上边框
		style.setBorderRight(BorderStyle.THIN);// 右边框
		return style;
	}

	/**
	 * 导出KTable数据至Excel文件
	 * 
	 * @param model
	 */
	private static void exportTableData(String title, String fileName, KTableModel model, IProgressMonitor monitor) {
		int size = (model == null) ? 0 : model.getRowCount();
		monitor.beginTask(Messages.XOTable_2 + title, size + 2);
		int fLen = model.getColumnCount();
		SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(10000);// 通过限制内存中可访问的记录行数来实现其低内存利用，当达到限定值时，新一行数据的加入会引起老一行的数据刷新到硬盘
		Sheet sheet = sxssfWorkbook.createSheet(title);
		CellStyle style = getCellStyle(sxssfWorkbook);
		monitor.worked(1);
		if (model != null) {
			monitor.setTaskName(Messages.XOTable_7);
			for (int rowNum = 0; rowNum < size; rowNum++) {
				Row rowData = sheet.createRow(rowNum);
				for (int colNum = 0; colNum < fLen; colNum++) {
					String cellValue = "" + model.getContentAt(colNum, rowNum);
					Cell dataCell = CellUtil.createCell(rowData, colNum, cellValue);
					dataCell.setCellStyle(style);
					if (rowNum < 1) {
						int width = 30 * model.getColumnWidth(colNum) + 1500;
						sheet.setColumnWidth(colNum, width);
					}
				}
				monitor.worked(1);
			}
		}
		monitor.setTaskName(Messages.XOTable_12);
		FileOutputStream out = null;
		try {
			out = new FileOutputStream(fileName);
			sxssfWorkbook.write(out);
		} catch (IOException e) {
			SCTLogger.error(e.getMessage());
		} finally {
			try {
				if (out != null)
					out.close();
			} catch (IOException e) {
				SCTLogger.error(e.getMessage());
			}
		}
		monitor.done();

	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("一键导出统计结果");
	}

	private void initData() {
		List<PkgTotal> sList = PkgTotalDao.getPkgTotal();
		pkgTb.setInput(sList);
	}

	@Override
	protected Control createDialogArea(Composite parent) {

		setTitle("一键导出统计结果");
		setMessage("报价结果统计、ID报价统计一键导出");
		Composite container = new Composite(parent, SWT.FILL);
		container.setLayout(new GridLayout(1, false));
		container.setLayoutData(new GridData(GridData.FILL_BOTH));

		this.pkgTb = TableFactory.getPkgTotalTable(container);
		pkgTb.getTable().setLayoutData(new GridData(GridData.FILL_BOTH));

		initData();

		return container;
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(950, 600);
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, IDialogConstants.OK_LABEL, true);
		createButton(parent, IDialogConstants.CANCEL_ID, IDialogConstants.CANCEL_LABEL, false);
	}

}
