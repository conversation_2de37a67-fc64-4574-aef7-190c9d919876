package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.List;

import com.jacob.activeX.ActiveXComponent;
import com.jacob.com.ComThread;
import com.jacob.com.Dispatch;
import com.jacob.com.Variant;

/**
 * @Description:Word工具
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:59:24
 */
public class WordUtils {

    // word文档
    private Dispatch doc;
    // 所有word文档集合
    private Dispatch documents;
    // 保存退出
    private boolean saveOnExit;
    // 选定的范围或插入点
    private Dispatch selection;

    // word运行程序对象
    private ActiveXComponent word;

    public WordUtils(boolean visible) {
        ComThread.InitMTA();
        System.out.println("启动 Word...");
        word = new ActiveXComponent("Word.Application");
        word.setProperty("Visible", new Variant(visible));
        documents = word.getProperty("Documents").toDispatch();
    }

    /**
     * 关闭word文档
     */
    public void closeDocument() {
        System.out.println("关闭文档");
        if (doc != null) {
            Dispatch.call(doc, "Close", new Variant(saveOnExit));
            doc = null;
        }
    }

    /**
     * 创建一个新的word文档
     */
    public void createNewDocument() {
        doc = Dispatch.call(documents, "Add").toDispatch();
        selection = Dispatch.get(word, "Selection").toDispatch();
    }

    public void exeCmd(String commandStr) {
        BufferedReader br = null;
        try {
            Process p = Runtime.getRuntime().exec(commandStr);
            br = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = null;
            StringBuilder sb = new StringBuilder();
            while ((line = br.readLine()) != null) {
                sb.append(line + "\n");
            }
            System.out.println(sb.toString());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void exit() {
        word.invoke("Quit", new Variant[0]);
        ComThread.Release();
    }

    /**
     * 从选定内容或插入点开始查找文本
     * 
     * @param findText 要查找的文本
     * @return boolean true-查找到并选中该文本，false-未查找到文本
     */
    public boolean find(String findText) {
        if (findText == null || findText.equals("")) {
            return false;
        }
        // 从selection所在位置开始查询
        Dispatch find = Dispatch.call(getSelection(), "Find").toDispatch();
        // 设置要查找的内容
        Dispatch.put(find, "Text", findText);
        // 向前查找
        Dispatch.put(find, "Forward", "True");
        // 设置格式
        Dispatch.put(find, "Format", "True");
        // 大小写匹配
        Dispatch.put(find, "MatchCase", "True");
        // 全字匹配
        Dispatch.put(find, "MatchWholeWord", "True");
        // 查找并选中
        return Dispatch.call(find, "Execute").getBoolean();
    }

    /**
     * 查找指定文本出现次数
     * @param findText
     * @return
     */
    public int findOccurence(String findText) {
        if (findText == null || findText.equals("")) {
            return 0;
        }
        int num = 0;
        while (find(findText)) {
            num++;
        }
        return num;
    }

    /**
     * 进入页脚视图
     */
    public void footerView() {
        // 取得活动窗体对象
        Dispatch ActiveWindow = word.getProperty("ActiveWindow").toDispatch();
        // 取得活动窗格对象
        Dispatch ActivePane = Dispatch.get(ActiveWindow, "ActivePane").toDispatch();
        // 取得视窗对象
        Dispatch view = Dispatch.get(ActivePane, "View").toDispatch();
        Dispatch.put(view, "SeekView", "10");
    }

    /**
     * 获取书签的位置
     * 
     * @param bookmarkName
     * @return 书签的位置
     */
    public Dispatch getBookmark(String bookmarkName) {
        try {
            Dispatch bookmark = Dispatch.call(this.doc, "Bookmarks", bookmarkName).toDispatch();
            return Dispatch.get(bookmark, "Range").toDispatch();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public int getPageCount() {
        Dispatch selection = Dispatch.get(word, "Selection").toDispatch();
        return Dispatch.call(selection, "information", new Variant(4)).getInt();
    }

    /**
     * 获取当前的选定的内容或者插入点
     * 
     * @return 当前的选定的内容或者插入点
     */
    public Dispatch getSelection() {
        if (selection == null)
            selection = Dispatch.get(word, "Selection").toDispatch();
        return selection;
    }

    /**
     * 读取word/wps文档的大纲
     * 
     * @param filename 要打开的文档的目录(带文件名)
     * @param strChoose 打开方式 Microsoft Word： 2 WPS： 3
     */
    public String getWordBookmark() {
        StringBuffer sb = new StringBuffer("");
        try {
            // 所有表格
            Dispatch tables = Dispatch.get(doc, "Tables").toDispatch();
            // 获取表格总数
            int tableCount = Dispatch.get(tables, "Count").getInt();
            Dispatch table = null;
            // 删除所有表格（删除第一个表格后，第二个表格会变成第一表格）
            for (int i = 0; i < tableCount; i++) {
                table = Dispatch.call(tables, "Item", new Variant(1)).toDispatch();
                Dispatch.call(table, "Delete");
            }

            // 获取大纲列表
            Dispatch paragraphs = Dispatch.get(doc, "Paragraphs").toDispatch();
            int count = Dispatch.get(paragraphs, "Count").getInt();// 大纲数量
            // 当前大纲等级层次
            String number = "";
            int level1 = 0;
            int level2 = 0;
            int level3 = 0;
            int level4 = 0;
            // 从后往前获取大纲
            for (int i = 0; i < count; i++) {
                // 当前大纲项
                Dispatch paragraph = Dispatch.call(paragraphs, "Item", new Variant(i + 1)).toDispatch();
                // 大纲等级
                int level = Dispatch.get(paragraph, "OutlineLevel").getInt();
                Dispatch paragraphRange = Dispatch.get(paragraph, "Range").toDispatch();
                // 一般目录不会超过4级，4级往后是内容，可以跳过

                if (level <= 4) {
                    if (level == 1) {
                        level1++;
                        level2 = 0;
                        level3 = 0;
                        level4 = 0;
                        number = level1 + "";
                    } else if (level == 2) {
                        level2++;
                        level3 = 0;
                        level4 = 0;
                        number = level1 + "." + level2;
                    } else if (level == 3) {
                        level3++;
                        level4 = 0;
                        number = level1 + "." + level2 + "." + level3;
                    }
                    if (level == 4) {
                        level4++;
                        number = level1 + "." + level2 + "." + level3 + "." + level4;
                    }
                    // 标题编号
                    Dispatch listFormat = Dispatch.get(paragraphRange, "ListFormat").toDispatch();
                    String listString = "";
                    try {
                        listString = Dispatch.get(listFormat, "ListString").toString();
                    } catch (Exception e) {
                        System.out.println("没有ListForat属性的标题："
                            + Dispatch.get(paragraphRange, "Text").toString().replaceAll("\\\r|\\\f", ""));
                    }
                    // 标题
                    String text = Dispatch.get(paragraphRange, "Text").toString().replaceAll("\\\r|\\\f", "");
                    // 可能会存在一些为空的隐藏的大纲，text是为空的
                    if (text == null || ("").equals(text)) {
                        continue;
                    }
                    text = listString + text;
                    // 索引的页码
                    int page = Dispatch.call(paragraphRange, "information", 1).getInt();
                    // System.out.println("text:" + text + "   page:" + page + "   level:" + level);

                    sb.append(number + "   " + text + "   " + page + "\n");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("获取书签失败");
        }
        return sb.toString();
    }

    /**
     * 进入页眉视图
     */
    public void headerView() {
        // 取得活动窗体对象
        Dispatch ActiveWindow = word.getProperty("ActiveWindow").toDispatch();
        // 取得活动窗格对象
        Dispatch ActivePane = Dispatch.get(ActiveWindow, "ActivePane").toDispatch();
        // 取得视窗对象
        Dispatch view = Dispatch.get(ActivePane, "View").toDispatch();
        Dispatch.put(view, "SeekView", "9");
    }

    /**
     * 在指定的书签位置插入文本
     * 
     * @param bookmarkName
     * @param text
     */
    public void insertAtBookmark(String bookmarkName, String text) {
        Dispatch dispatch = getBookmark(bookmarkName);
        if (dispatch != null)
            Dispatch.put(dispatch, "Text", text);
    }

    /**
     * 在当前插入点中插入图片
     * 
     * @param imagePath
     */
    public void insertImage(String imagePath) {
        Dispatch.call(Dispatch.get(getSelection(), "InLineShapes").toDispatch(), "AddPicture", imagePath);
    }

    /**
     * 在当前插入点中插入图片
     * 
     * @param imagePath
     */
    public void insertImage(String imagePath, int width, int height) {
        Dispatch picture =
            Dispatch.call(Dispatch.get(getSelection(), "InLineShapes").toDispatch(), "AddPicture", imagePath)
                .toDispatch();
        Dispatch.call(picture, "Select");
        Dispatch.put(picture, "Width", new Variant(width));
        Dispatch.put(picture, "Height", new Variant(height));
        moveRight();
    }

    /**
     * 在指定的书签位置插入图片
     * 
     * @param bookmarkName
     * @param imagePath
     */
    public void insertImageAtBookmark(String bookmarkName, String imagePath) {
        Dispatch dispatch = getBookmark(bookmarkName);
        if (dispatch != null)
            Dispatch.call(Dispatch.get(dispatch, "InLineShapes").toDispatch(), "AddPicture", imagePath);
    }

    /**
     * 在指定的书签位置插入图片
     * 
     * @param bookmarkName
     * @param imagePath
     * @param width
     * @param height
     */
    public void insertImageAtBookmark(String bookmarkName, String imagePath, int width, int height) {
        Dispatch dispatch = getBookmark(bookmarkName);
        if (dispatch != null) {
            Dispatch picture =
                Dispatch.call(Dispatch.get(dispatch, "InLineShapes").toDispatch(), "AddPicture", imagePath)
                    .toDispatch();
            Dispatch.call(picture, "Select");
            Dispatch.put(picture, "Width", new Variant(width));
            Dispatch.put(picture, "Height", new Variant(height));
        }
    }

    /**
     * 在当前插入点插入字符串
     */
    public void insertText(String text) {
        putFontSize(getSelection(), "宋体", 9);
        Dispatch.put(getSelection(), "Text", text);
    }

    /**
     * 把选定的内容或者插入点向指定的方向移动
     * 
     * @param actionName
     * @param pos
     */
    private void move(String actionName, int pos) {
        for (int i = 0; i < pos; i++)
            Dispatch.call(getSelection(), actionName);
    }

    /**
     * 把选定的内容或者插入点向下移动
     * 
     * @param pos
     */
    public void moveDown(int pos) {
        move("MoveDown", pos);
    }

    /**
     * 把插入点移动到文件末尾位置
     */
    public void moveEnd() {
        Dispatch.call(getSelection(), "EndKey", new Variant(6));
    }

    /**
     * 把选定的内容或者插入点向左移动
     * 
     * @param pos
     */
    public void moveLeft(int pos) {
        move("MoveLeft", pos);
    }

    /**
     * 把选定的内容或者插入点向右移动
     */
    public void moveRight() {
        Dispatch.call(getSelection(), "MoveRight");
    }

    /**
     * 把选定的内容或者插入点向右移动
     * 
     * @param pos
     */
    public void moveRight(int pos) {
        move("MoveRight", pos);
    }

    /**
     * 把插入点移动到文件首位置
     */
    public void moveStart() {
        Dispatch.call(getSelection(), "HomeKey", new Variant(6));
    }

    /**
     * 把选定的内容或插入点向上移动
     * 
     * @param pos
     */
    public void moveUp(int pos) {
        move("MoveUp", pos);
    }

    /**
     * 插入换页符
     */
    public void newPage() {
        Dispatch.call(getSelection(), "InsertBreak");
    }

    public void nextPage() {
        moveEnd();
        moveDown(1);
    }

    /**
     * 打开一个已经存在的word文档
     * 
     * @param docPath
     */
    public void openDocument(String docPath, boolean readOnly) {
        System.out.println("打开文档..." + docPath);
        doc =
            Dispatch.invoke(documents, "Open", Dispatch.Method,
                new Object[] {docPath, new Variant(false), new Variant(true),// 是否只读  
                    new Variant(false), new Variant("pwd")}, new int[1]).toDispatch();

        selection = Dispatch.get(word, "Selection").toDispatch();
    }

    /**
     * 打开一个有密码保护的word文档
     * 
     * @param docPath
     * @param password
     */
    public void openDocument(String docPath, String password) {
        doc = Dispatch.call(documents, "Open", docPath).toDispatch();
        unProtect(password);
        selection = Dispatch.get(word, "Selection").toDispatch();
    }

    /**
     * 进入普通视图
     */
    public void pageView() {
        // 取得活动窗体对象
        Dispatch ActiveWindow = word.getProperty("ActiveWindow").toDispatch();
        // 取得活动窗格对象
        Dispatch ActivePane = Dispatch.get(ActiveWindow, "ActivePane").toDispatch();
        // 取得视窗对象
        Dispatch view = Dispatch.get(ActivePane, "View").toDispatch();
        Dispatch.put(view, "SeekView", new Variant(0));// 普通视图
    }
    
    /** *//** 
     * 在当前文档末尾拷贝来自另一个文档中的段落 
     *     
     * @param anotherDocPath 另一个文档的磁盘路径 
     * @param tableIndex 被拷贝的段落在另一格文档中的序号(从1开始) 
     */ 
    public void copyParagraphFromAnotherDoc(String anotherDocPath, int paragraphIndex) { 
    	Dispatch wordContent = Dispatch.get(doc, "Content").toDispatch(); // 取得当前文档的内容 
    	Dispatch.call(wordContent, "InsertAfter", "$selection$");// 插入特殊符定位插入点 
    	copyParagraphFromAnotherDoc(anotherDocPath, paragraphIndex, "$selection$"); 
    } 
    
    /** *//** 
     * 在当前文档指定的位置拷贝来自另一个文档中的表格 
     *     
     * @param anotherDocPath 另一个文档的磁盘路径 
     */ 
    public void copyTableFromAnotherDoc(String anotherDocPath, String findText) {
		Dispatch doc2 = null;
		try {
			doc2 = Dispatch.call(documents, "Open", anotherDocPath).toDispatch();
			Dispatch tables = Dispatch.get(doc2, "Tables").toDispatch();
			// 获取表格总数
			int tableCount = Dispatch.get(tables, "Count").getInt();
			if (tableCount == 0) {
				return;
			}
			Dispatch table = Dispatch.call(tables, "Item", new Variant(tableCount)).toDispatch();
			Dispatch range = Dispatch.get(table, "Range").toDispatch();
			Dispatch.call(range, "Copy");
			Dispatch textRange = Dispatch.get(selection, "Range").toDispatch();
			Dispatch.call(textRange, "Paste");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (doc2 != null) {
				Dispatch.call(doc2, "Close", new Variant(saveOnExit));
				doc2 = null;
			}
		}
	} 

    /** *//** 
     * 在当前文档指定的位置拷贝来自另一个文档中的段落 
     *     
     * @param anotherDocPath 另一个文档的磁盘路径 
     * @param tableIndex 被拷贝的段落在另一格文档中的序号(从1开始) 
     * @param pos 当前文档指定的位置 
     */ 
    public void copyParagraphFromAnotherDoc(String anotherDocPath, int paragraphIndex, String pos) {
		Dispatch doc2 = null;
		try {
			doc2 = Dispatch.call(documents, "Open", anotherDocPath).toDispatch();
			Dispatch paragraphs = Dispatch.get(doc2, "Paragraphs").toDispatch();

			Dispatch paragraph = Dispatch.call(paragraphs, "Item", new Variant(paragraphIndex)).toDispatch();
			Dispatch range = Dispatch.get(paragraph, "Range").toDispatch();
			Dispatch.call(range, "Copy");
			if (this.find(pos)) {
				Dispatch textRange = Dispatch.get(selection, "Range").toDispatch();
				Dispatch.call(textRange, "Paste");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (doc2 != null) {
				Dispatch.call(doc2, "Close", new Variant(saveOnExit));
				doc2 = null;
			}
		}
	} 

    /**
     * 打印
     */
    public void print() {
        Dispatch.call(doc, "PrintOut");
    }

    public void print(String printerName) {
        word.setProperty("ActivePrinter", new Variant(printerName));
        print();
    }

    /**
     * 指定打印机名称和打印输出工作名称
     * 
     * @param printerName
     * @param outputName
     */
    public void print(String printerName, String outputName) {
        word.setProperty("ActivePrinter", new Variant(printerName));
        Dispatch.call(doc, "PrintOut", new Object[] {new Variant(true), new Variant(true), new Variant(2),
            new Variant(outputName)});
    }

    /**
     * 打印预览：
     */
    public void printpreview() {
        Dispatch.call(doc, "PrintPreView");
    }

    /**
     * 添加密码保护
     * 
     * @param password
     */
    public void protect(String password) {
        String protectionType = Dispatch.get(doc, "ProtectionType").toString();
        if ("-1".equals(protectionType)) {
            Dispatch.call(doc, "Protect", new Object[] {new Variant(3), new Variant(true), password});
        }
    }

    /**
     * 设置选中或当前插入点的字体
     * 
     * @param selection
     * @param fontName
     * @param size
     */
    public void putFontSize(Dispatch selection, String fontName, int size) {
        Dispatch font = Dispatch.get(selection, "Font").toDispatch();
        Dispatch.put(font, "Name", new Variant(fontName));
        Dispatch.put(font, "Size", new Variant(size));
    }

    /**
     * 全局将指定的文本替换成图片
     * 
     * @param findText
     * @param imagePath
     */
    public void replaceAllImage(String findText, String imagePath, int width, int height) {
        moveStart();
        while (find(findText)) {
            Dispatch picture =
                Dispatch.call(Dispatch.get(getSelection(), "InLineShapes").toDispatch(), "AddPicture", imagePath)
                    .toDispatch();
            Dispatch.call(picture, "Select");
            Dispatch.put(picture, "Width", new Variant(width));
            Dispatch.put(picture, "Height", new Variant(height));
            moveStart();
        }
    }

    /**
     * 全局替换文本
     * 
     * @param findText
     * @param newText
     */
    public void replaceAllText(String findText, String newText) {
        int count = getPageCount();
        for (int i = 0; i < count; i++) {
            headerView();
            while (find(findText)) {
                Dispatch.put(getSelection(), "Text", newText);
                moveEnd();
            }
            footerView();
            while (find(findText)) {
                Dispatch.put(getSelection(), "Text", newText);
                moveStart();
            }
            pageView();
            moveStart();
            while (find(findText)) {
                Dispatch.put(getSelection(), "Text", newText);
                moveStart();
            }
            nextPage();
        }
    }

    /**
     * 全局替换文本
     * 
     * @param findText
     * @param newText
     */
    public void replaceAllText(String findText, String newText, String fontName, int size) {
        /**** 插入页眉页脚 *****/
        // 取得活动窗体对象
        Dispatch ActiveWindow = word.getProperty("ActiveWindow").toDispatch();
        // 取得活动窗格对象
        Dispatch ActivePane = Dispatch.get(ActiveWindow, "ActivePane").toDispatch();
        // 取得视窗对象
        Dispatch view = Dispatch.get(ActivePane, "View").toDispatch();
        /**** 设置页眉  *****/
        Dispatch.put(view, "SeekView", "9");
        while (find(findText)) {
            Dispatch.put(getSelection(), "Text", newText);
            moveStart();
        }
        /**** 设置页脚 *****/
        Dispatch.put(view, "SeekView", "10");
        while (find(findText)) {
            Dispatch.put(getSelection(), "Text", newText);
            moveStart();
        }
        Dispatch.put(view, "SeekView", new Variant(0));// 恢复视图
        moveStart();
        while (find(findText)) {
            Dispatch.put(getSelection(), "Text", newText);
            putFontSize(getSelection(), fontName, size);
            moveStart();
        }
    }

    /**
     * 将指定的文本替换成图片
     * 
     * @param findText
     * @param imagePath
     * @return boolean true-查找到并替换该文本，false-未查找到文本
     */
    public boolean replaceImage(String findText, String imagePath, int width, int height) {
        moveStart();
        if (!find(findText))
            return false;
        Dispatch picture =
            Dispatch.call(Dispatch.get(getSelection(), "InLineShapes").toDispatch(), "AddPicture", imagePath)
                .toDispatch();
        Dispatch.call(picture, "Select");
        Dispatch.put(picture, "Width", new Variant(width));
        Dispatch.put(picture, "Height", new Variant(height));
        moveRight();
        return true;
    }

    /**
     * 查找并替换文字
     * 
     * @param findText
     * @param newText
     * @return boolean true-查找到并替换该文本，false-未查找到文本
     */
    public boolean replaceText(String findText, String newText) {
        moveStart();
        if (!find(findText))
            return false;
        Dispatch.put(getSelection(), "Text", newText);
        return true;
    }

    /**
     * 保存文档
     * 
     * @param savePath
     */
    public void save(String savePath) {
        Dispatch.call(Dispatch.call(word, "WordBasic").getDispatch(), "FileSaveAs", savePath);
    }

    /**
     * 文档另存为
     * 
     * @param savePath
     */
    public void saveAs(String savePath) {
        Dispatch.call(doc, "SaveAs", savePath);
    }
    
    /**
     * 文档另存为
     * 
     * @param savePath
     */
    public void saveAsDocx(String savePath) {
		/*
		 * *其中第44行中的 invoke（）函数中的Variant(n)参数指定另存为的文件类型（n的取值范围是0-25），他们分别是：
		 * Variant(0):docVariant(1):dotVariant(2-5)，Variant(7):txtVariant(6):rft
		 * Variant(8)，Variant(10):htmVariant(9):mht
		 * Variant(11)，Variant(19-22):xmlVariant(12):docxVariant(13):docm
		 * Variant(14):dotxVariant(15):dotmVariant(16)、Variant(24):docx
		 * Variant(17):pdfVariant(18):xpsVariant(23):odt
		 * Variant(25):与Office2003与2007的转换程序相关，执行本程序后弹出一个警告框说是需要更高版本的 Microsoft
		 * Works Converter由于我计算机上没有安装这个转换器，所以不清楚此参数代表什么格式
		 */
        Dispatch.invoke((Dispatch) doc,"SaveAs", Dispatch.Method, new Object[]{savePath, new Variant(12)}, new int[1]);
    }

    /**
     * 文档另存为PDF <b>
     * <p>
     * 注意：此操作要求word是2007版本或以上版本且装有加载项：Microsoft Save as PDF 或 XPS
     * </p>
     * </b>
     * 
     * @param savePath
     */
    public void saveAsPdf(String savePath) throws InterruptedException {
        long start = System.currentTimeMillis();
        System.out.println("转换文档到 PDF..." + savePath);
        File tofile = new File(savePath);
        if (tofile.exists()) {
            tofile.delete();
        }
        Dispatch.call(doc, "SaveAs", savePath, new Variant(17));
        long end = System.currentTimeMillis();
        System.out.println("转换完成..用时：" + (end - start) + "ms.");
    }

    /**
     * 设置退出时参数
     * 
     * @param saveOnExit boolean true-退出时保存文件，false-退出时不保存文件 　　　　
     */
    public void setSaveOnExit(boolean saveOnExit) {
        this.saveOnExit = saveOnExit;
    }

    /**
     * 显示审阅的最终状态
     */
    public void showFinalState() {
        Dispatch.call(doc, "AcceptAllRevisionsShown");
    }

    /**
     * 合并DOCX文件
     * @param fileList
     * @param savepaths
     */
    public void uniteDoc(List<String> fileList, String savepaths) {
        if (fileList.size() == 0 || fileList == null) {
            return;
        }
        // 打开word
        try {
            // 追加文件
            for (int i = 0; i < fileList.size() -1; i++) {
                Dispatch.invoke(word.getProperty("Selection").toDispatch(), "insertFile", Dispatch.Method,
                    new Object[] {fileList.get(i), "", new Variant(false), new Variant(false), new Variant(false)},
                    new int[3]);

                Dispatch selection = Dispatch.get(word, "Selection").toDispatch();
				/*
				 * 插入分页符 若不插入，则直接将文件按顺序拼接成一个word 若插入分页符则添加了一个分页符号 变成了多页
				 */
				Dispatch.call(selection, "InsertBreak", new Variant(7));

            }
            // 保存新的word文件
            Dispatch.call(doc, "SaveAs", savepaths);
            // Dispatch.invoke((Dispatch) doc, "SaveAs", Dispatch.Method,
            // new Object[] { savepaths, new Variant(1) }, new int[3]);
        } catch (Exception e) {
            throw new RuntimeException("合并word文件出错.原因:" + e);
        }
    }

    /**
     * 去掉密码保护
     * 
     * @param password
     */
    public void unProtect(String password) {
        try {
            String protectionType = Dispatch.get(doc, "ProtectionType").toString();
            if (!"-1".equals(protectionType)) {
                Dispatch.call(doc, "Unprotect", password);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
