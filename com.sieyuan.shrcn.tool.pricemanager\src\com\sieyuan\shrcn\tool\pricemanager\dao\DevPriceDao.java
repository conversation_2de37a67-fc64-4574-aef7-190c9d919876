package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.shrcn.found.common.util.StringUtil;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.DevPrice;

/**
 * @Description: 计算表数据Dao
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Si<PERSON>uan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:54:08
 
 */
public class DevPriceDao {

    public static List<DevPrice> getAllProductPrices() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<DevPrice> sList = new ArrayList<>();
        StringBuffer sql =
            new StringBuffer("select * from (select * from devprice order by bidno) a  order by a.orderid");
        try {
            sList = sqliteHelper.executeQuery(sql.toString(), new RowMapper<DevPrice>() {
                @Override
                public DevPrice mapRow(ResultSet rs, int index) throws SQLException {
                    DevPrice priceModel = new DevPrice();
                    priceModel.setOrderid(rs.getInt("orderid"));
                    priceModel.setNumber(rs.getString("number"));
                    priceModel.setName(rs.getString("name"));
                    priceModel.setBidno(rs.getString("bidno"));
                    priceModel.setDevname(rs.getString("devname"));
                    priceModel.setDevtype(rs.getString("devtype"));
                    priceModel.setUnit(rs.getString("unit"));
                    priceModel.setCount(rs.getString("count"));
                    priceModel.setOdevtype(rs.getString("odevtype"));
                    priceModel.setOcunnt(rs.getString("ocunnt"));
                    priceModel.setSupply(rs.getString("supply"));
                    priceModel.setArea(rs.getString("area"));
                    priceModel.setPrice(rs.getString("price"));
                    priceModel.setLnType(rs.getString("lntype"));
                    priceModel.setQuote(rs.getString("quote"));
                    priceModel.setSearchType(rs.getString("searchdevtype"));
                    priceModel.setCostprice(rs.getString("costprice"));
                    priceModel.setId(rs.getInt("id"));
                    return priceModel;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static void saveDevPriceInfo(List<DevPrice> devPrices) {

        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();

        for (DevPrice devPrice : devPrices) {
            Integer orderid = devPrice.getOrderid();
            String number = devPrice.getNumber();
            String name = devPrice.getName();
            String bidno = devPrice.getBidno();
            String devname = devPrice.getDevname();
            String devtype = devPrice.getDevtype();
            String unit = devPrice.getUnit();
            String count = devPrice.getCount();
            String odevtype = devPrice.getOdevtype();
            String ocunnt = devPrice.getOcunnt();
            String supply = devPrice.getSupply();
            String area = devPrice.getArea();
            if (StringUtil.isEmpty(bidno)) {
                continue;
            }
            String price = devPrice.getPrice();
            String lntype = devPrice.getLnType();
            String searchtype = devPrice.getSearchType();
            String costPrice = devPrice.getCostprice();
            String quote = devPrice.getQuote();

            String sql =
                "INSERT INTO devprice (orderid, number, name, bidno, devname, devtype, unit, count,odevtype, ocunnt,supply, "
                    + "area, quote, searchdevtype, lntype, costprice, price) " + "VALUES ("
                    + orderid
                    + ", "
                    + "'"
                    + number
                    + "'"
                    + ", "
                    + "'"
                    + name
                    + "'"
                    + ", "
                    + "'"
                    + bidno
                    + "'"
                    + ", "
                    + "'"
                    + devname
                    + "'"
                    + ", "
                    + "'"
                    + devtype
                    + "'"
                    + ", "
                    + "'"
                    + unit
                    + "'"
                    + ", "
                    + "'"
                    + count
                    + "'"
                    + ", "
                    + "'"
                    + odevtype
                    + "'"
                    + ", "
                    + "'"
                    + ocunnt
                    + "'"
                    + ", "
                    + "'"
                    + supply
                    + "'"
                    + ", "
                    + "'"
                    + area
                    + "'"
                    + ", "
                    + "'"
                    + quote
                    + "',"
                    + "'"
                    + searchtype
                    + "'"
                    + ", "
                    + "'"
                    + lntype
                    + "'"
                    + ", " + "'" + costPrice + "'" + "," + "'" + price + "'" + ")";
            sqls.add(sql);
        }
        try {
            // sqliteHelper.executeUpdate("delete from devprice;");
            // sqliteHelper.executeUpdate("update sqlite_sequence SET seq = 0 where name ='devprice';");
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

    public static void saveDevPriceInfo(Set<DevPrice> devPrices) {

        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();

        for (DevPrice devPrice : devPrices) {
            Integer orderid = devPrice.getOrderid();
            String number = devPrice.getNumber();
            String name = devPrice.getName();
            String bidno = devPrice.getBidno();
            String devname = devPrice.getDevname();
            String devtype = devPrice.getDevtype();
            String unit = devPrice.getUnit();
            String count = devPrice.getCount();
            String odevtype = devPrice.getOdevtype();
            String ocunnt = devPrice.getOcunnt();
            String supply = devPrice.getSupply();
            String area = devPrice.getArea();
            if (StringUtil.isEmpty(bidno)) {
                continue;
            }
            String price = devPrice.getPrice();
            String lntype = devPrice.getLnType();
            String searchtype = devPrice.getSearchType();
            String costPrice = devPrice.getCostprice();
            String quote = devPrice.getQuote();
            String file = devPrice.getFile();

            String sql =
                "INSERT INTO devprice (orderid, number, name, bidno, devname, devtype, unit, count,odevtype, ocunnt,supply, "
                    + "area, quote, searchdevtype, lntype, costprice, price, file) " + "VALUES ("
                    + orderid
                    + ",'"
                    + number
                    + "','"
                    + name
                    + "','"
                    + bidno
                    + "','"
                    + devname
                    + "','"
                    + devtype
                    + "','"
                    + unit
                    + "','"
                    + count
                    + "','"
                    + odevtype
                    + "','"
                    + ocunnt
                    + "','"
                    + supply
                    + "','"
                    + area
                    + "','"
                    + quote
                    + "','"
                    + searchtype
                    + "','"
                    + lntype
                    + "','" + costPrice + "','" + price  + "','" + file + "')";
            sqls.add(sql);
        }
        try {
            // sqliteHelper.executeUpdate("delete from devprice;");
            // sqliteHelper.executeUpdate("update sqlite_sequence SET seq = 0 where name ='devprice';");
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

    // 清空数据
    public static void truncateDevPrice(List<String> bidNos) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        try {
            for (String bidNO : bidNos) {
                String sql = "delete from devprice where bidno='" + bidNO + "';";
                sqls.add(sql);
            }
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

	public static void updateDevPriceInfo(List<DevPrice> devPriceLsit) {
		SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
		List<String> sqls = new ArrayList<String>();
		for (DevPrice devPrice : devPriceLsit) {
			Integer id = devPrice.getId();
			String lnType = StringUtils.trimToEmpty(devPrice.getLnType());
			String costprice = StringUtils.trimToEmpty(devPrice.getCostprice());
			String price = StringUtils.trimToEmpty(devPrice.getPrice());
			String searchType = StringUtils.trimToEmpty(devPrice.getSearchType());
			String supply = StringUtils.trimToEmpty(devPrice.getSupply());

			String sql = "update devprice set lntype='" + lnType + "',searchdevtype='" + searchType + "',supply='" + supply + "', costprice='" + costprice + "',price='" + price + "' where id=" + id;
			sqls.add(sql);
		}
		try {
			sqliteHelper.executeUpdate(sqls);
		} catch (ClassNotFoundException | SQLException e) {
			e.printStackTrace();
		}
	}
}
