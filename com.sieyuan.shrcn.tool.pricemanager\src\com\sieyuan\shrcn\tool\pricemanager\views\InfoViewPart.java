package com.sieyuan.shrcn.tool.pricemanager.views;

import org.eclipse.jface.action.Action;
import org.eclipse.jface.action.IToolBarManager;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.ui.part.ViewPart;

import com.shrcn.found.ui.action.ItClearAction;
import com.shrcn.found.ui.action.ItExportAction;
import com.shrcn.found.ui.util.SwtUtil;

/**
 * @Description:InfoViewPart 信息控制台 ViewPart
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午11:07:29
 
 */
public class InfoViewPart extends ViewPart {
    public static final String ID = "com.sieyuan.shrcn.tool.pricemanager.app.infoview";
    private StyledText text;

    /**
     * This is a callback that will allow us to create the viewer and initialize
     * it.
     */
    public void createPartControl(Composite parent) {
        new InfoView(parent);
        text = InfoView.getText();
        Action actions[] = {new ItClearAction(text), new ItExportAction(text)};
        SwtUtil.createContextMenu(text, actions);
        initializeToolBar();
    }

    /**
     * 初始化工具条
     */
    private void initializeToolBar() {
        IToolBarManager toolBarManager = getViewSite().getActionBars().getToolBarManager();
        toolBarManager.add(new ItClearAction(text));
        toolBarManager.add(new ItExportAction(text));
    }

    /**
     * Passing the focus request to the viewer's control.
     */
    public void setFocus() {}
}
