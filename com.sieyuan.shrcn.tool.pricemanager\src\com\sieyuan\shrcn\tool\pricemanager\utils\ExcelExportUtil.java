package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.alibaba.fastjson.JSONArray;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.model.DevPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgBidInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.model.PriceRate;

/**
 * @Description:ExcelExportUtil
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-10 下午3:33:27
 
 */
public class ExcelExportUtil {
	
    /**
     * 导出Excel文件（支持样式优化和列宽调整）
     *
     * @param filePath    文件路径（如：C:/BidEvaluationResult.xlsx）
     * @param sheetName   工作表名称
     * @param headers     表头数组
     * @param data        数据列表
     * @param footerInfo  底部信息（键值对：字段名 -> 值）
     */
    public static void exportToExcel(
            String filePath,
            String sheetName,
            String[] headers,
            List<Object[]> data,
            Map<String, Object> footerInfo
    ) {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(sheetName);

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook); // 表头样式
        CellStyle dataStyle = createDataStyle(workbook);     // 数据行样式
        CellStyle footerStyle = createFooterStyle(workbook); // 底部信息样式

        // 1. 写入表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle); // 应用表头样式
        }

        // 2. 写入数据行
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            Object[] rowData = data.get(i);
            for (int j = 0; j < rowData.length; j++) {
                Cell cell = row.createCell(j);
                setCellValue(cell, rowData[j]);
                cell.setCellStyle(dataStyle); // 应用数据行样式
            }
        }

        // 3. 写入底部信息
        if (footerInfo != null && !footerInfo.isEmpty()) {
            int startRowIndex = data.size() + 2; // 数据行下方空一行
            int rowIndex = startRowIndex;
            for (Map.Entry<String, Object> entry : footerInfo.entrySet()) {
                Row footerRow = sheet.createRow(rowIndex++);
                Cell keyCell = footerRow.createCell(0);
                keyCell.setCellValue(entry.getKey());
                keyCell.setCellStyle(footerStyle); // 应用底部信息样式

                Cell valueCell = footerRow.createCell(1);
                setCellValue(valueCell, entry.getValue());
                valueCell.setCellStyle(footerStyle); // 应用底部信息样式
            }
        }

        // 4. 调整列宽
        adjustColumnWidth(sheet, headers.length);

        // 5. 写入文件
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            workbook.write(fos);
            System.out.println("Excel文件生成成功：" + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 调整列宽
     */
    private static void adjustColumnWidth(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i); // 自动调整列宽
            int currentWidth = sheet.getColumnWidth(i);
            int minWidth = 4000; // 设置最小列宽（单位：1/256字符宽度）
            if (currentWidth < minWidth) {
                sheet.setColumnWidth(i, minWidth); // 如果列宽小于最小值，则设置为最小值
            }
        }
    }

    /**
     * 创建表头样式（背景色 + 边框）
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex()); // 背景色
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN); // 上边框
        style.setBorderBottom(BorderStyle.THIN); // 下边框
        style.setBorderLeft(BorderStyle.THIN); // 左边框
        style.setBorderRight(BorderStyle.THIN); // 右边框
        style.setAlignment(HorizontalAlignment.CENTER); // 居中
        Font font = workbook.createFont();
        font.setBold(true); // 加粗
        style.setFont(font);
        return style;
    }

    /**
     * 创建数据行样式（边框）
     */
    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN); // 上边框
        style.setBorderBottom(BorderStyle.THIN); // 下边框
        style.setBorderLeft(BorderStyle.THIN); // 左边框
        style.setBorderRight(BorderStyle.THIN); // 右边框
        return style;
    }

    /**
     * 创建底部信息样式（背景色 + 边框）
     */
    private static CellStyle createFooterStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex()); // 背景色
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN); // 上边框
        style.setBorderBottom(BorderStyle.THIN); // 下边框
        style.setBorderLeft(BorderStyle.THIN); // 左边框
        style.setBorderRight(BorderStyle.THIN); // 右边框
        return style;
    }

    /**
     * 根据数据类型设置单元格值
     */
    private static void setCellValue(Cell cell, Object value) {
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        }
    }
	
	// 行报价清单
	public static void exportPkgProduct(String path, List<PkgProduct> products, String sheetName) {
		JSONArray jsonArray = new JSONArray();
		for (int i = 0; i < products.size(); i++) {
			PkgProduct priceRate = products.get(i);
			jsonArray.add(priceRate);
		}
		/*
		 * titleList存放了2个元素,分别为titleMap和headMap
		 */
		@SuppressWarnings("rawtypes")
		ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
		LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
		titleMap.put("title1", sheetName);
		titleMap.put("title2", "https://www.sieyuan.com");
		// 2.headMap存放了该excel的列项
		LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
		
		// 分包名称	基准物料名称	网省采购申请行号	分包编号	轮次	基准物料编码	技术规范书ID	项目单位	附件名称	单位	扩展描述	包限价(万)	行限价(万)	数量	辅参ID	辅参数组合	价格配置因子（%）	辅参数组合限价（万）	辅参未含税单价（万）	基准物料未含税单价（万）	税率（%）	辅参含税单价（万）	基准物料含税单价（万）	基准物料未含税合价（万）	基准物料含税合价（万）

		headMap.put("number", "序号");
		headMap.put("devname", "元件名称");
		headMap.put("devtype", "规格型号");
		headMap.put("unit", "单位");
		headMap.put("count", "数量");
		headMap.put("odevtype", "规格型式");
		headMap.put("ocunnt", "数量");
		headMap.put("supply", "制造商");
		headMap.put("area", "产地");
		
		headMap.put("quote", "是否报价");
		headMap.put("searchdevtype", "价格选型型号");
		headMap.put("lnType", "类型");
		headMap.put("costprice", "成本价格（万）");
		headMap.put("price", "对外报价（万）");
		headMap.put("weight", "报价权重");
		
		titleList.add(titleMap);
		titleList.add(headMap);

		File file = new File(path);
		if (!file.exists())
			try {
				file.createNewFile();
			} catch (IOException e1) {
				e1.printStackTrace();
			}// 创建该文件夹目录
		OutputStream os = null;
		try {
			System.out.println("正在导出xlsx...");
			long start = System.currentTimeMillis();
			// .xlsx格式
			os = new FileOutputStream(path);
			SXSSFWorkbookUtil.exportExcel(titleList, jsonArray, os, true, true);
			System.out.println("导出完成...共" + products.size() + "条数据,用时" + (System.currentTimeMillis() - start) + "毫秒");
			System.out.println("文件路径：" + path);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (os != null) {
					os.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	// 辅参报价检查
	@SuppressWarnings("rawtypes")
	public static void exportPriceRateReport(String path, List<PriceRate> allPriceRate, String sheetName) {
		JSONArray jsonArray = new JSONArray();
		for (int i = 0; i < allPriceRate.size(); i++) {
			PriceRate priceRate = allPriceRate.get(i);

			if (!StringUtils.isEmpty(priceRate.getValid())) {
				if (priceRate.getValid().equals("1")) {
					priceRate.setValid("是");
				} else if (priceRate.getValid().equals("2")) {
					priceRate.setValid("否");
				} else {
					priceRate.setValid("");
				}
			} else {
				priceRate.setValid("");
			}

			if (!StringUtils.isEmpty(priceRate.getPvalid())) {
				if (priceRate.getPvalid().equals("1")) {
					priceRate.setPvalid("是");
				} else if (priceRate.getPvalid().equals("2")) {
					priceRate.setPvalid("否");
				} else {
					priceRate.setPvalid("");
				}
			} else {
				priceRate.setPvalid("");
			}

			if (priceRate.getValid().equals("是") && priceRate.getPvalid().equals("是")) {
				priceRate.setAllvalid("是");
			} else {
				priceRate.setAllvalid("否");
			}

			String avg = priceRate.getAvg();
			String pavg = priceRate.getPavg();

			Float limit = Float.valueOf(UIPreferencesUtil.getIdLimit()) / 100;

			if (!StringUtils.isEmpty(avg) && CalcUtil.isDouble(avg)) {
				BigDecimal max = CalcUtil.getPriceMax(new BigDecimal(avg), limit);
				BigDecimal min = CalcUtil.getPriceMin(new BigDecimal(avg), limit);
				BigDecimal pmax = CalcUtil.getPriceMax(new BigDecimal(pavg), limit);
				BigDecimal pimin = CalcUtil.getPriceMin(new BigDecimal(pavg), limit);
				priceRate.setMax(String.valueOf(max));
				priceRate.setMin(String.valueOf(min));
				priceRate.setPmax(String.valueOf(pmax));
				priceRate.setPmin(String.valueOf(pimin));
			}

			jsonArray.add(priceRate);
		}
		/*
		 * titleList存放了2个元素,分别为titleMap和headMap
		 */
		ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
		LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
		titleMap.put("title1", sheetName);
		titleMap.put("title2", "https://www.sieyuan.com");
		// 2.headMap存放了该excel的列项
		LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();

		headMap.put("pid", "技术规范书ID");
		headMap.put("funit", "辅参数组合");

		headMap.put("pkgId", "分包编号");
		headMap.put("pkgName", "分包名称");
		headMap.put("round", "轮次");
		headMap.put("attachment", "附件上传状态");
		headMap.put("code", "基准物料编码");
		headMap.put("productName", "基准物料名称");

		headMap.put("orderid", "网省采购申请行号");
		headMap.put("company", "项目单位");
		headMap.put("unit", "单位");
		headMap.put("extendedDesc", "扩展描述	");
		headMap.put("limitPrice", "包限价（万）");
		headMap.put("rowLimitPrice", "行限价（万）");
		headMap.put("count", "数量");
		headMap.put("fid", "辅参ID");

		headMap.put("rate", "价格配置因子（%）");
		headMap.put("fLimitPrice", "辅参数组合限价（万）");
		headMap.put("fprice", "辅参未含税单价（万）");
		headMap.put("bprice", "基准物料未含税单价（万）	");
		headMap.put("taxrate", "税率（%）	");
		headMap.put("fWithPrice", "辅参含税单价（万）");
		headMap.put("bWithPrice", "基准物料含税单价（万）");
		headMap.put("bTotalPrice", "基准物料未含税合价（万）");
		headMap.put("bTotalWithPrice", "基准物料含税合价（万）");
		headMap.put("floatRate", "上浮比例（%）");

		headMap.put("max", "sum最大值");
		headMap.put("min", "sum总最小值");
		headMap.put("avg", "sum总平均值");

		headMap.put("pmax", "per最大值");
		headMap.put("pmin", "per最小值");
		headMap.put("pavg", "per平均值");
		headMap.put("valid", "sum报价是否在±" + UIPreferencesUtil.getIdLimit() + "%范围");
		headMap.put("pvalid", "per报价是否在±" + UIPreferencesUtil.getIdLimit() + "%范围");
		headMap.put("allvalid", "报价平均值是否在±" + UIPreferencesUtil.getIdLimit() + "%范围");

		titleList.add(titleMap);
		titleList.add(headMap);

		File file = new File(path);
		if (!file.exists())
			try {
				file.createNewFile();
			} catch (IOException e1) {
				e1.printStackTrace();
			}// 创建该文件夹目录
		OutputStream os = null;
		try {
			System.out.println("正在导出xlsx...");
			long start = System.currentTimeMillis();
			// .xlsx格式
			os = new FileOutputStream(path);
			SXSSFWorkbookUtil.exportExcel(titleList, jsonArray, os, true, false);
			System.out.println("导出完成...共" + allPriceRate.size() + "条数据,用时" + (System.currentTimeMillis() - start) + "毫秒");
			System.out.println("文件路径：" + path);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (os != null) {
					os.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	
	// 行报价清单
	public static void exportPriceRate(String path, List<PriceRate> allPriceRate, String sheetName) {
		JSONArray jsonArray = new JSONArray();
		for (int i = 0; i < allPriceRate.size(); i++) {
			PriceRate priceRate = allPriceRate.get(i);
			jsonArray.add(priceRate);
		}
		/*
		 * titleList存放了2个元素,分别为titleMap和headMap
		 */
		@SuppressWarnings("rawtypes")
		ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
		LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
		titleMap.put("title1", sheetName);
		titleMap.put("title2", "https://www.sieyuan.com");
		// 2.headMap存放了该excel的列项
		LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
		
		// 分包名称	基准物料名称	网省采购申请行号	分包编号	轮次	基准物料编码	技术规范书ID	项目单位	附件名称	单位	扩展描述	包限价(万)	行限价(万)	数量	辅参ID	辅参数组合	价格配置因子（%）	辅参数组合限价（万）	辅参未含税单价（万）	基准物料未含税单价（万）	税率（%）	辅参含税单价（万）	基准物料含税单价（万）	基准物料未含税合价（万）	基准物料含税合价（万）

		headMap.put("pkgName", "分包名称");
		headMap.put("productName", "基准物料名称");
		headMap.put("orderid", "网省采购申请行号");
		headMap.put("pkgId", "分包编号");
		headMap.put("round", "轮次");
		headMap.put("code", "基准物料编码");
		headMap.put("pid", "技术规范书ID");
		headMap.put("company", "项目单位");
		headMap.put("attachment", "附件名称");
		
		headMap.put("unit", "单位");
		headMap.put("extendedDesc", "扩展描述	");
		headMap.put("limitPrice", "包限价（万）");
		headMap.put("rowLimitPrice", "行限价（万）");
		headMap.put("count", "数量");
		headMap.put("fid", "辅参ID");
		headMap.put("funit", "辅参数组合");
		headMap.put("rate", "价格配置因子（%）");
		headMap.put("fLimitPrice", "辅参数组合限价（万）");
		headMap.put("fprice", "辅参未含税单价（万）");
		headMap.put("bprice", "基准物料未含税单价（万）	");
		headMap.put("taxrate", "税率（%）	");
		headMap.put("fWithPrice", "辅参含税单价（万）");
		headMap.put("bWithPrice", "基准物料含税单价（万）");
		headMap.put("bTotalPrice", "基准物料未含税合价（万）");
		headMap.put("bTotalWithPrice", "基准物料含税合价（万）");
		// headMap.put("floatRate", "上浮比例（%）");
		
		titleList.add(titleMap);
		titleList.add(headMap);

		File file = new File(path);
		if (!file.exists())
			try {
				file.createNewFile();
			} catch (IOException e1) {
				e1.printStackTrace();
			}// 创建该文件夹目录
		OutputStream os = null;
		try {
			System.out.println("正在导出xlsx...");
			long start = System.currentTimeMillis();
			// .xlsx格式
			os = new FileOutputStream(path);
			SXSSFWorkbookUtil.exportExcel(titleList, jsonArray, os, true, false);
			System.out.println("导出完成...共" + allPriceRate.size() + "条数据,用时" + (System.currentTimeMillis() - start) + "毫秒");
			System.out.println("文件路径：" + path);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (os != null) {
					os.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

    @SuppressWarnings("rawtypes")
    public static void exportCal(String path, List<PkgProduct> allPrices, String bidName) {
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < allPrices.size(); i++) {
            PkgProduct devPrice = allPrices.get(i);
            if (devPrice.getLnType() == 1) {
                devPrice.setLnTypeString(ToolConstants.PRICE_IN);
            } else if (devPrice.getLnType() == 2) {
                devPrice.setLnTypeString(ToolConstants.PRICE_OUT);
            } else {
                devPrice.setLnTypeString(ToolConstants.PRICE_UNKOWN);
            }
            jsonArray.add(devPrice);
        }

        /*
         * titleList存放了2个元素,分别为titleMap和headMap
         */
        ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
        // 1.titleMap存放了该excel的头信息
        LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
        titleMap.put("title1", bidName + "计算表");
        titleMap.put("title2", "https://www.sieyuan.com");
        // 2.headMap存放了该excel的列项
        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();

        headMap.put("number", "序号");
        headMap.put("devname", "元件名称");
        headMap.put("devtype", "规格型号");
        headMap.put("unit", "单位");
        headMap.put("count", "数量");
        headMap.put("odevtype", "规格型式");
        headMap.put("ocunnt", "数量");
        headMap.put("supply", "制造商");
        headMap.put("area", "产地");
        headMap.put("quote", "是否报价");
        headMap.put("searchdevtype", "价格选型型号");
        headMap.put("lnTypeString", "类型");
        headMap.put("costprice", "成本价格");
        headMap.put("price", "对外报价");
        headMap.put("weight", "报价权重");

        titleList.add(titleMap);
        titleList.add(headMap);

        File file = new File(path);
        if (!file.exists())
            try {
                file.createNewFile();
            } catch (IOException e1) {
                e1.printStackTrace();
            }// 创建该文件夹目录
        OutputStream os = null;
        try {
            System.out.println("正在导出xlsx...");
            long start = System.currentTimeMillis();
            // .xlsx格式
            os = new FileOutputStream(path);
            SXSSFWorkbookUtil.exportExcel(titleList, jsonArray, os, true, true);
            System.out.println("导出完成...共" + allPrices.size() + "条数据,用时" + (System.currentTimeMillis() - start) + "毫秒");
            System.out.println("文件路径：" + path);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 独有ID统计结果
     * @param path
     * @param allPkgBidInfos
     */
	@SuppressWarnings("rawtypes")
	public static void exportDiffIDReport(String path, List<PkgBidInfo> allPkgBidInfos) {
		JSONArray jsonArray = new JSONArray();
		for (int i = 0; i < allPkgBidInfos.size(); i++) {
			PkgBidInfo devPrice = allPkgBidInfos.get(i);
			jsonArray.add(devPrice);
		}

		/*
		 * titleList存放了2个元素,分别为titleMap和headMap
		 */
		ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
		// 1.titleMap存放了该excel的头信息
		LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
		titleMap.put("title1", "独有ID统计结果");
		titleMap.put("title2", "https://www.sieyuan.com");
		// 2.headMap存放了该excel的列项
		LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();

		headMap.put("bidNo", "技术规范ID");
		headMap.put("pkgName", "包号");
		headMap.put("limitprice", "总限价");
		headMap.put("product", "物资描述");
		headMap.put("count", "数量");
		headMap.put("price", "含税单价");
		headMap.put("totalprice", "含税总价");

		titleList.add(titleMap);
		titleList.add(headMap);

		File file = new File(path);
		if (!file.exists())
			try {
				file.createNewFile();
			} catch (IOException e1) {
				e1.printStackTrace();
			}// 创建该文件夹目录
		OutputStream os = null;
		try {
			System.out.println("正在导出xlsx...");
			long start = System.currentTimeMillis();
			// .xlsx格式
			os = new FileOutputStream(path);
			SXSSFWorkbookUtil.exportExcel(titleList, jsonArray, os, false, true);
			System.out.println("导出完成...共" + allPkgBidInfos.size() + "条数据,用时" + (System.currentTimeMillis() - start) + "毫秒");
			System.out.println("文件路径：" + path);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (os != null) {
					os.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

    @SuppressWarnings("rawtypes")
    public static void exportIDReport(String path, List<PkgBidInfo> allPkgBidInfos) {
        UIPreferences perference = UIPreferences.newInstance();
        String ID_LIMIT = "idLimit";
        String idLimit = perference.getInfo(EnvirmentSettingDialog.class.getName() + ID_LIMIT);
        if (StringUtils.isEmpty(idLimit)) {
            idLimit = ToolConstants.LIMIT;
        }
        JSONArray jsonArray = new JSONArray();
		for (int i = 0; i < allPkgBidInfos.size(); i++) {
			PkgBidInfo devPrice = allPkgBidInfos.get(i);
			if (!StringUtils.isEmpty(devPrice.getIsValid())) {
				if (devPrice.getIsValid().equals("1")) {
					devPrice.setIsValid("是");
				} else if (devPrice.getIsValid().equals("2")) {
					devPrice.setIsValid("否");
				} else {
					devPrice.setIsValid("");
				}
			} else {
				devPrice.setIsValid("");
			}

			if (!StringUtils.isEmpty(devPrice.getPisValid())) {
				if (devPrice.getPisValid().equals("1")) {
					devPrice.setPisValid("是");
				} else if (devPrice.getPisValid().equals("2")) {
					devPrice.setPisValid("否");
				} else {
					devPrice.setPisValid("");
				}
			} else {
				devPrice.setPisValid("");
			}

			if (devPrice.getIsValid().equals("是") && devPrice.getPisValid().equals("是")) {
				devPrice.setValid("是");
			} else {
				devPrice.setValid("否");
			}

			String avg = devPrice.getAvg();
			String pavg = devPrice.getPavg();

			Float limit = Float.valueOf(idLimit) / 100;

			if (!StringUtils.isEmpty(avg) && CalcUtil.isDouble(avg)) {
				BigDecimal max = CalcUtil.getPriceMax(new BigDecimal(avg), limit);
				BigDecimal min = CalcUtil.getPriceMin(new BigDecimal(avg), limit);
				BigDecimal pmax = CalcUtil.getPriceMax(new BigDecimal(pavg), limit);
				BigDecimal pimin = CalcUtil.getPriceMin(new BigDecimal(pavg), limit);
				devPrice.setMax(String.valueOf(max));
				devPrice.setMin(String.valueOf(min));
				devPrice.setPmax(String.valueOf(pmax));
				devPrice.setPmin(String.valueOf(pimin));
			}

			devPrice.setRealRate(String.valueOf(CalcUtil.divide(devPrice.getTotalprice(), devPrice.getOrgPrice())));
			jsonArray.add(devPrice);
		}

        /*
         * titleList存放了2个元素,分别为titleMap和headMap
         */
        ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
        // 1.titleMap存放了该excel的头信息
        LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
        titleMap.put("title1", "按ID统计结果");
        titleMap.put("title2", "https://www.sieyuan.com");
        // 2.headMap存放了该excel的列项
        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();

        headMap.put("bidNo", "技术规范ID");
        headMap.put("pkgName", "包号");
        headMap.put("limitprice", "限价");
        headMap.put("count", "数量");
        headMap.put("price", "含税单价");
        headMap.put("totalprice", "含税总价");
        
        headMap.put("orgPrice", "参考价");
        headMap.put("realRate", "含税总价/参考价");

        headMap.put("max", "sum最大值");
        headMap.put("min", "sum总最小值");
        headMap.put("avg", "sum总平均值");
        
        headMap.put("pmax", "per最大值");
        headMap.put("pmin", "per最小值");
        headMap.put("pavg", "per平均值");
        headMap.put("isValid", "sum报价是否在±" + idLimit + "%范围");
        headMap.put("pisValid", "per报价是否在±" + idLimit + "%范围");
        headMap.put("valid", "报价平均值是否在±" + idLimit + "%范围");
        headMap.put("product", "物资描述");
        
        titleList.add(titleMap);
        titleList.add(headMap);

        File file = new File(path);
        if (!file.exists())
            try {
                file.createNewFile();
            } catch (IOException e1) {
                e1.printStackTrace();
            }// 创建该文件夹目录
        OutputStream os = null;
        try {
            System.out.println("正在导出xlsx...");
            long start = System.currentTimeMillis();
            // .xlsx格式
            os = new FileOutputStream(path);
            SXSSFWorkbookUtil.exportExcel(titleList, jsonArray, os, false, true);
            System.out.println("导出完成...共" + allPkgBidInfos.size() + "条数据,用时" + (System.currentTimeMillis() - start)
                + "毫秒");
            System.out.println("文件路径：" + path);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    // 物资名称偏差
    @SuppressWarnings("rawtypes")
	public static void exportProductReport(String path, List<PkgBidInfo> allPkgBidInfos) {
		String idLimit = ToolConstants.LIMIT;
		JSONArray jsonArray = new JSONArray();
		for (int i = 0; i < allPkgBidInfos.size(); i++) {
			PkgBidInfo devPrice = allPkgBidInfos.get(i);

			if (!StringUtils.isEmpty(devPrice.getIsValid())) {
				if (devPrice.getIsValid().equals("1")) {
					devPrice.setIsValid("是");
				} else if (devPrice.getIsValid().equals("2")) {
					devPrice.setIsValid("否");
				} else {
					devPrice.setIsValid("");
				}
			} else {
				devPrice.setIsValid("");
			}

			if (!StringUtils.isEmpty(devPrice.getPisValid())) {
				if (devPrice.getPisValid().equals("1")) {
					devPrice.setPisValid("是");
				} else if (devPrice.getPisValid().equals("2")) {
					devPrice.setPisValid("否");
				} else {
					devPrice.setPisValid("");
				}
			} else {
				devPrice.setPisValid("");
			}

			if (devPrice.getIsValid().equals("是") && devPrice.getPisValid().equals("是")) {
				devPrice.setValid("是");
			} else {
				devPrice.setValid("否");
			}

			String avg = devPrice.getAvg();
			String pavg = devPrice.getPavg();

			Float limit = Float.valueOf(10) / 100;

			if (!StringUtils.isEmpty(avg) && CalcUtil.isDouble(avg)) {
				BigDecimal max = CalcUtil.getPriceMax(new BigDecimal(avg), limit);
				BigDecimal min = CalcUtil.getPriceMin(new BigDecimal(avg), limit);
				BigDecimal pmax = CalcUtil.getPriceMax(new BigDecimal(pavg), limit);
				BigDecimal pimin = CalcUtil.getPriceMin(new BigDecimal(pavg), limit);
				devPrice.setMax(String.valueOf(max));
				devPrice.setMin(String.valueOf(min));
				devPrice.setPmax(String.valueOf(pmax));
				devPrice.setPmin(String.valueOf(pimin));
			}

			jsonArray.add(devPrice);
		}

		/*
		 * titleList存放了2个元素,分别为titleMap和headMap
		 */
		ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
		// 1.titleMap存放了该excel的头信息
		LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
		titleMap.put("title1", "按物资描述统计结果");
		titleMap.put("title2", "https://www.sieyuan.com");
		// 2.headMap存放了该excel的列项
		LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();

		headMap.put("product", "物资描述");
		headMap.put("bidNo", "技术规范ID");
		headMap.put("pkgName", "包号");
		headMap.put("limitprice", "限价");
		headMap.put("count", "数量");
		headMap.put("price", "含税单价");
		headMap.put("totalprice", "含税总价");

		headMap.put("max", "sum最大值");
		headMap.put("min", "sum总最小值");
		headMap.put("avg", "sum总平均值");

		headMap.put("pmax", "per最大值");
		headMap.put("pmin", "per最小值");
		headMap.put("pavg", "per平均值");
		headMap.put("isValid", "sum报价是否在±" + idLimit + "%范围");
		headMap.put("pisValid", "per报价是否在±" + idLimit + "%范围");
		headMap.put("valid", "报价平均值是否在±" + idLimit + "%范围");

		titleList.add(titleMap);
		titleList.add(headMap);

		File file = new File(path);
		if (!file.exists())
			try {
				file.createNewFile();
			} catch (IOException e1) {
				e1.printStackTrace();
			}// 创建该文件夹目录
		OutputStream os = null;
		try {
			System.out.println("正在导出xlsx...");
			long start = System.currentTimeMillis();
			// .xlsx格式
			os = new FileOutputStream(path);
			SXSSFWorkbookUtil.exportExcel(titleList, jsonArray, os, false, true);
			System.out.println("导出完成...共" + allPkgBidInfos.size() + "条数据,用时" + (System.currentTimeMillis() - start) + "毫秒");
			System.out.println("文件路径：" + path);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (os != null) {
					os.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

    @SuppressWarnings("rawtypes")
    public static void exportPrice(String path, String bidNO, List<DevPrice> allPrices) {
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < allPrices.size(); i++) {
            DevPrice devPrice = allPrices.get(i);
            if (devPrice.getLnType().equals("1")) {
                devPrice.setLnType(ToolConstants.PRICE_IN);
            } else if (devPrice.getLnType().equals("2")) {
                devPrice.setLnType(ToolConstants.PRICE_OUT);
            } else {
                devPrice.setLnType(ToolConstants.PRICE_UNKOWN);
            }
            jsonArray.add(allPrices.get(i));
        }

        /*
         * titleList存放了2个元素,分别为titleMap和headMap
         */
        ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
        // 1.titleMap存放了该excel的头信息
        LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
        titleMap.put("title1", bidNO + "型号对应成本信息表");
        titleMap.put("title2", "https://www.sieyuan.com");
        // 2.headMap存放了该excel的列项
        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();

        headMap.put("number", "序号");
        headMap.put("devname", "元件名称");
        headMap.put("devtype", "规格型号");
        headMap.put("unit", "单位");
        headMap.put("count", "数量");
        headMap.put("odevtype", "规格型式");
        headMap.put("ocunnt", "数量");
        headMap.put("supply", "制造商");
        headMap.put("area", "产地");
        headMap.put("searchType", "价格选型型号");
        headMap.put("lnType", "类型");
        headMap.put("costprice", "成本价格");
        headMap.put("price", "对外报价");

        titleList.add(titleMap);
        titleList.add(headMap);

        File file = new File(path);
        if (!file.exists())
            try {
                file.createNewFile();
            } catch (IOException e1) {
                e1.printStackTrace();
            }// 创建该文件夹目录
        OutputStream os = null;
        try {
            System.out.println("正在导出xlsx...");
            long start = System.currentTimeMillis();
            // .xlsx格式
            os = new FileOutputStream(path);
            SXSSFWorkbookUtil.exportExcel(titleList, jsonArray, os, false, true);
            System.out.println("导出完成...共" + allPrices.size() + "条数据,用时" + (System.currentTimeMillis() - start) + "毫秒");
            System.out.println("文件路径：" + path);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                os.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
