package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;

import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.Product;

public class ProductHanderTest {

	@Test
	public void test() {
		Map<String, CostPrice> priceMap = new HashMap<>();
		List<Product> productlist = new ArrayList<>();
		int rowId = 1;

		String file = "e://A493-500126107-00021_变电站预制仓_1.docx";

		GeneralYZCHander productHander = new GeneralYZCHander(priceMap);
		productHander.parse(file, productlist, rowId);
		for (Product product : productlist) {
			System.out.println(product);
		}
	}

}
