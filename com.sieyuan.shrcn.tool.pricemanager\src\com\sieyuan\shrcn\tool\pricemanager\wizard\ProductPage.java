package com.sieyuan.shrcn.tool.pricemanager.wizard;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.jface.wizard.WizardPage;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Label;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.sax.ImportDirectoryParser;

public class ProductPage extends WizardPage {
    private List<String> allDocxAbsoluteFile;
    private List<String> allExcelAbsoluteFile;
    private String DIR = ".priceRoot";
    private String diretoryPath;
    private Text diretoryRoot;
    private Label label;

    private org.eclipse.swt.widgets.List lsPackages;

    private UIPreferences perference = UIPreferences.newInstance();

    protected ProductPage(String pageName) {
        super(pageName);
    }

    private void addListeners() {
        diretoryRoot.addModifyListener(new ModifyListener() {
            @Override
            public void modifyText(ModifyEvent e) {
                diretoryPath = diretoryRoot.getText();
                if (!StringUtil.isEmpty(diretoryPath)) {
                    ProgressManager.execute(new IRunnableWithProgress() {
                        @Override
                        public void run(final IProgressMonitor monitor) throws InvocationTargetException,
                            InterruptedException {
                            Display.getDefault().asyncExec(new Runnable() {
                                @Override
                                public void run() {
                                    monitor.beginTask("正在加载技术应答文件夹...", 1);
                                    loadFiles();
                                    String infopath = ProductPage.class.getName();
                                    perference.setInfo(infopath + DIR, diretoryPath);
                                    monitor.done();
                                }
                            });
                        }
                    });
                }
            }
        });
    }

    public void createControl(Composite parent) {

        // 每页的提示信息
        setTitle("技术应答信息");
        setMessage("请选择技术应答路径", INFORMATION);
        // 创建一个组件的底层面板，并使用GridLayout布局
        Composite topComp = new Composite(parent, SWT.NULL);
        topComp.setLayout(new GridLayout(3, false));

        diretoryRoot = SwtUtil.createDirectorySelector(topComp, "技术应答导入路径：", "请输入技术应答文件夹所在路径");
        GridData gdList = new GridData(GridData.FILL_HORIZONTAL);
        gdList.horizontalSpan = 3;
        Composite compList = SwtUtil.createComposite(topComp, gdList, 1);

		label = SwtUtil.createLabel(compList, "技术应答文件：", new GridData(SWT.FILL, SWT.LEFT, false, false, 1, 1));
        GridData listData = new GridData(GridData.FILL_HORIZONTAL);
        listData.heightHint = 250;
        lsPackages = SwtUtil.createMultiList(compList, listData);
        init();
        addListeners();
        this.setControl(topComp);// 必须要的一行
    }

    public List<String> getAllDocxAbsoluteFile() {
        return allDocxAbsoluteFile;
    }

    public List<String> getAllExcelAbsoluteFile() {
        return allExcelAbsoluteFile;
    }

    public String getDiretoryPath() {
        return diretoryPath;
    }

    private void init() {
        String infopath = getClass().getName();
        diretoryPath = perference.getInfo(infopath + DIR);
        if (diretoryPath.equals("")) {
            diretoryPath = DirManager.getToolDirFile();
        }
        diretoryRoot.setText(diretoryPath);
        loadFiles();
    }

    private void loadFiles() {
        ImportDirectoryParser importDirectoryParser = new ImportDirectoryParser(diretoryPath);
        importDirectoryParser.parseDirectory();
        List<String> allFile = importDirectoryParser.getAllFile();
        allDocxAbsoluteFile = importDirectoryParser.getAllAbsoluteDocxFile();
        allExcelAbsoluteFile = importDirectoryParser.getAllAbsoluteExcelFile();
        label
            .setText("技术应答文件：" + "（索引文件" + allExcelAbsoluteFile.size() + "个，技术应答文件" + allDocxAbsoluteFile.size() + "个)");
        lsPackages.setItems(allFile.toArray(new String[allFile.size()]));
    }
}
