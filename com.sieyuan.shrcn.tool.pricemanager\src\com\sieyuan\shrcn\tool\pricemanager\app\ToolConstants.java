package com.sieyuan.shrcn.tool.pricemanager.app;

import java.math.BigDecimal;

import com.sieyuan.shrcn.tool.pricemanager.utils.CalcUtil;

/**
 * @Description:工具常量
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:48:28
 */
public class ToolConstants {

    public static final String HELP_DOC = "报价管理工具使用说明.docx";

    
    public static final String CONNECT = "@@@";
    public static final String DB_NAME = "PriceDB";
    public static final String EVENT_EXP = "export";
    public static final String EVENT_PATH = "com/shrcn/price/app/eventscfg.xml";
    public static final String iedPrefix = "保护";
    public static final String INTELLIGENCE = "智能";
    public static final boolean isCK = false;
    public static final String LIMIT_FILE = "限价信息表.xlsx";
    public static final String limitSheet = "国网集招限价";
    public static final String monitorPrefix = "监控";
    public static final String monitorTemplate = "监控模板";
    public static final String noPrice = "不报价";
    public static final String NOTINTELLIGENCE = "常规";
    public static final String SEARCH = "请输入搜索的内容";
    // 自产系数
    public static final String OUT_TAX = "2";
    public static final String ORG_RATE = "1.00";
    public static final String OUTPUT_DIR = "报价结果";
    public static final String PACKAGEDATA_ITEM = "PACKAGEDATA_ITEM";
    public static final String PKG_DIR = "技术应答";
    public static final String PKG_FILE = "物料清单信息表.xlsx";
    public static final String REAL_PRICE_FILE = "参考价.xlsx";
    public static final String PKG_TITLE = "物料清单信息表";
    public static final int pLimit = 6;
    public static final int pPrice = 8;
    public static final int pRate = 3;
    public static final String precision = "0.000000000000";
    public static final String PRICE_FILE = "价格库信息表.xlsx";
    public static final String TEMPLATE_FILE = "pkgTemplate.xlsx";
    public static final String PRICE_IN = "自产";
    public static final String PRICE_OUT = "外购";
    public static final String PRICE_UNKOWN = "";
    public static final String priceSheet = "价格库";
    public static final int pValue = 6;
    public static final int rate = 13;// Excel表中的税率
    public static final String LIMIT = "5";
    public static final String rootSheet = "35～110kV继电保护和变电站计算机监控系统";
    public static final String SPITSIGN = "每面屏含";
    public static final BigDecimal taxRate = CalcUtil.getTaxRate();
    public static final String TECH_DOCX = "组件材料配置表";
    
    public static final int up_limit = 1 << 2;
    public static final int up_price = 1 << 1;
    public static final int up_products = 1 << 3;
    
    public static final String DBFILE ="price.db";
    public static final String DBFOLDER ="data";
    
    public static final String DEFAULT_PRJ ="2019虚拟项目";
    public static final String XLSX =".xlsx";
    public static final String PRICERATE = "报价方式-单价";
}
