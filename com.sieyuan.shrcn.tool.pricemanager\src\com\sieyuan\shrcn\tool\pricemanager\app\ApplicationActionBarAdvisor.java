package com.sieyuan.shrcn.tool.pricemanager.app;

import org.eclipse.jface.action.IMenuManager;
import org.eclipse.ui.application.IActionBarConfigurer;

import com.shrcn.found.ui.app.AbstractActionBarAdvisor;

/**
 * An action bar advisor is responsible for creating, adding, and disposing of the actions added to a workbench window.
 * Each window will be populated with new actions.
 */
public class ApplicationActionBarAdvisor extends AbstractActionBarAdvisor {

    /**
     * 构造函数
     * 
     * @param configurer
     */
    public ApplicationActionBarAdvisor(IActionBarConfigurer configurer) {
        super(configurer);
    }

    @Override
    protected void fillMenuBar(IMenuManager menuBar) {
        super.fillMenuBar(menuBar);
    }

}
