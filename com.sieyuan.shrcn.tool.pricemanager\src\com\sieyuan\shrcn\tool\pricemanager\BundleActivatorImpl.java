package com.sieyuan.shrcn.tool.pricemanager;

import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;
import org.eclipse.osgi.service.datalocation.Location;
import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import org.osgi.framework.Filter;
import org.osgi.framework.InvalidSyntaxException;
import org.osgi.util.tracker.ServiceTracker;

import com.shrcn.found.common.event.EventManager;

/**
 * @Description:BundleActivatorImpl
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON><PERSON>uan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午11:01:16
 
 */
public class BundleActivatorImpl implements BundleActivator {

    private static final String F_META_AREA = ".metadata"; //$NON-NLS-1$
    private static final String F_PLUGIN_DATA = ".plugins"; //$NON-NLS-1$

    private static BundleActivatorImpl instance;

    public static BundleActivatorImpl getInstance() {
        return instance;
    }

    private BundleContext context;

    @SuppressWarnings("rawtypes")
    private ServiceTracker locationServiceTracker;

    private IPath stateLocation;

    @SuppressWarnings({"rawtypes", "unchecked"})
    public IPath getStateLocation() {
        try {
            if (stateLocation == null) {
                Filter filter = context.createFilter(Location.INSTANCE_FILTER);
                if (locationServiceTracker == null) {
                    locationServiceTracker = new ServiceTracker(context, filter, null);
                    locationServiceTracker.open();
                }
                Location location = (Location)locationServiceTracker.getService();
                if (location != null) {
                    IPath path = new Path(location.getURL().getPath());
                    stateLocation =
                        path.append(F_META_AREA).append(F_PLUGIN_DATA).append(context.getBundle().getSymbolicName());
                    stateLocation.toFile().mkdirs();
                }
            }
        } catch (InvalidSyntaxException e) {
        }
        return stateLocation;
    }

    public void start(BundleContext context) throws Exception {
        instance = this;
        this.context = context;
        EventManager.getDefault().loadEventHandlers(getClass(), "com/sieyuan/shrcn/tool/pricemanager/eventscfg.xml");
    }

    public void stop(BundleContext context) throws Exception {
        this.context = null;
        instance = null;
    }
}
