package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

/**
 * 价格配置类
 * 集中管理所有价格计算相关的配置参数
 */
public class PriceConfiguration {
    
    private final String inWeight;
    private final String outWeight;
    private final String onlyIdRate;
    private final String originRate;
    
    public PriceConfiguration(String inWeight, String outWeight, String onlyIdRate, String originRate) {
        this.inWeight = inWeight;
        this.outWeight = outWeight;
        this.onlyIdRate = onlyIdRate;
        this.originRate = originRate;
    }
    
    public String getInWeight() {
        return inWeight;
    }
    
    public String getOutWeight() {
        return outWeight;
    }
    
    public String getOnlyIdRate() {
        return onlyIdRate;
    }
    
    public String getOriginRate() {
        return originRate;
    }
} 