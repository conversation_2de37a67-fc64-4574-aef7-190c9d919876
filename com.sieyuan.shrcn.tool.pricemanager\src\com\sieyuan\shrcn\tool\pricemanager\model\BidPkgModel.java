package com.sieyuan.shrcn.tool.pricemanager.model;

/**
 * BidEvaluationModel
 * 
 * <AUTHOR>
 * 
 */
public class BidPkgModel {

	// 包名
	private String pkgName;
	// 最终报价A
	private double finalBid;
	// 基准价B
	private double basePrice;

	public BidPkgModel(String pkgName, double finalBid, double basePrice) {
		super();
		this.pkgName = pkgName;
		this.finalBid = finalBid;
		this.basePrice = basePrice;
	}

	public String getPkgName() {
		return pkgName;
	}

	public void setPkgName(String pkgName) {
		this.pkgName = pkgName;
	}

	public double getFinalBid() {
		return finalBid;
	}

	public void setFinalBid(double finalBid) {
		this.finalBid = finalBid;
	}

	public double getBasePrice() {
		return basePrice;
	}

	public void setBasePrice(double basePrice) {
		this.basePrice = basePrice;
	}

}
