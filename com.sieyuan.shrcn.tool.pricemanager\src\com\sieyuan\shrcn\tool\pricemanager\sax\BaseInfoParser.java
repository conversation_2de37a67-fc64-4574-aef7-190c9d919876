/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.eclipse.core.runtime.IProgressMonitor;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.file.excel.Xls2007Parser;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.LimitPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgIndex;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.Product;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;

/**
* 
* <AUTHOR>
* @version 1.0, 2017-12-22
*/
public class BaseInfoParser {

    private List<String> allExcelAbsoluteFile = new ArrayList<>();
    private List<String> allFiles = new ArrayList<>();
    private List<PkgInfo> allPkgs = new ArrayList<>();
    private List<CostPrice> costtPrices = new ArrayList<>();
    private String limitpath;
    private List<LimitPrice> limitPrices = new ArrayList<>();

    private IProgressMonitor monitor;
    private List<PkgIndex> pkgIndexes = new ArrayList<>();
    private String pkgpath;
    private List<PkgInfo> pkgs = new ArrayList<>();
    private String pricepath;
    private List<Product> productlist = new ArrayList<>();

    private String[] selectNames;

    public BaseInfoParser(String pkgpath) {
        super();
        this.pkgpath = pkgpath;
    }

    public BaseInfoParser(String pkgpath, String limitpath) {
        super();
        this.pkgpath = limitpath;
        this.limitpath = limitpath;
    }

    public BaseInfoParser(String pkgpath, String limitpath, String pricepath) {
        super();
        this.pkgpath = limitpath;
        this.limitpath = limitpath;
        this.pricepath = pricepath;
    }

    public BaseInfoParser(String pkgpath, String limitpath, String pricepath, List<String> allFiles,
        List<String> allExcelAbsoluteFile, String[] selectNames, IProgressMonitor monitor) {
        super();
        this.pkgpath = pkgpath;
        this.limitpath = limitpath;
        this.pricepath = pricepath;
        this.allFiles = allFiles;
        this.monitor = monitor;
        this.selectNames = selectNames;
        this.allExcelAbsoluteFile = allExcelAbsoluteFile;
    }

    public void execute() {
        monitor.setTaskName("第二步：读取物料清单");
        parsePkg();
        parseIndex();
        monitor.worked(10);
        monitor.setTaskName("第三步：读取限价表");
        parseLimit();
        monitor.worked(10);
        monitor.setTaskName("第四步：读取成本价格库");
        parsePrice();
        monitor.worked(5);
        long startTime = System.currentTimeMillis(); // 获取开始时间
        monitor.setTaskName("第五步：解析技术应答");
        parseProduct();
        long endTime = System.currentTimeMillis(); // 获取结束时间
        System.out.println("程序运行时间： " + (endTime - startTime) + "ms");
    }

    public List<PkgInfo> getAllPkgs() {
        return allPkgs;
    }

    public List<CostPrice> getCostPrices() {
        return costtPrices;
    }

    public List<LimitPrice> getLimitPrices() {
        return limitPrices;
    }

    public List<PkgInfo> getPkgs() {
        return pkgs;
    }

    public List<Product> getProductlist() {
        return productlist;
    }

    public void parseAllPkg() {
        try {
            OPCPackage xlsxPackage = OPCPackage.open(pkgpath, PackageAccess.READ);
            ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
            XSSFReader xssfReader = new XSSFReader(xlsxPackage);
            StylesTable styles = xssfReader.getStylesTable();
            XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator)xssfReader.getSheetsData();
            while (iter.hasNext()) {
                InputStream stream = iter.next();
                PkgHandler pkgHandler = new PkgHandler();
                Xls2007Parser.processSheet(styles, strings, pkgHandler, stream);
                allPkgs.addAll(pkgHandler.getPkgs());
                stream.close();
            }
            xlsxPackage.close();
        } catch (Throwable e) {
        	e.printStackTrace();
        }
    }

    public void parseIndex() {
        try {
            for (String file : allExcelAbsoluteFile) {
                OPCPackage xlsxPackage = OPCPackage.open(file, PackageAccess.READ);
                ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
                XSSFReader xssfReader = new XSSFReader(xlsxPackage);
                StylesTable styles = xssfReader.getStylesTable();
                XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator)xssfReader.getSheetsData();
                while (iter.hasNext()) {
                    InputStream stream = iter.next();
                    PkgIndexHandler pkgHandler = new PkgIndexHandler();
                    Xls2007Parser.processSheet(styles, strings, pkgHandler, stream);
                    pkgIndexes.addAll(pkgHandler.getPkgIndexex());
                    stream.close();
                }
                xlsxPackage.close();
            }
        } catch (Throwable e) {
            SCTLogger.error(e.getMessage());
        }
    }

    public void parseLimit() {
        try {
            OPCPackage xlsxPackage = OPCPackage.open(limitpath, PackageAccess.READ);
            ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
            XSSFReader xssfReader = new XSSFReader(xlsxPackage);
            StylesTable styles = xssfReader.getStylesTable();
            XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator)xssfReader.getSheetsData();
            while (iter.hasNext()) {
                InputStream stream = iter.next();
                LimitPriceHandler limitHandler = new LimitPriceHandler();
                Xls2007Parser.processSheet(styles, strings, limitHandler, stream);
                limitPrices = limitHandler.getLimitPriceList();
                // }
                stream.close();
            }
            xlsxPackage.close();
        } catch (Throwable e) {
            SCTLogger.error(e.getMessage());
        }
    }

    public void parsePkg() {
        try {
            OPCPackage xlsxPackage = OPCPackage.open(pkgpath, PackageAccess.READ);
            ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
            XSSFReader xssfReader = new XSSFReader(xlsxPackage);
            StylesTable styles = xssfReader.getStylesTable();
            XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator)xssfReader.getSheetsData();
            while (iter.hasNext()) {
                InputStream stream = iter.next();
                PkgHandler pkgHandler = new PkgHandler();
                Xls2007Parser.processSheet(styles, strings, pkgHandler, stream);

                List<PkgInfo> pkgList = new ArrayList<>();
                for (PkgInfo pkg : pkgHandler.getPkgs()) {
                    if (Arrays.asList(selectNames).contains(pkg.getName())) {
                        pkgList.add(pkg);
                    }
                }
                pkgs.addAll(pkgList);
                stream.close();
            }
            xlsxPackage.close();
        } catch (Throwable e) {
            SCTLogger.error(e.getMessage());
        }
    }

    public void parsePrice() {
        try {
            OPCPackage xlsxPackage = OPCPackage.open(pricepath, PackageAccess.READ);
            ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
            XSSFReader xssfReader = new XSSFReader(xlsxPackage);
            StylesTable styles = xssfReader.getStylesTable();
            XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator)xssfReader.getSheetsData();
            while (iter.hasNext()) {
                InputStream stream = iter.next();
                CostPriceHandler priceHandler = new CostPriceHandler();
                Xls2007Parser.processSheet(styles, strings, priceHandler, stream);
                costtPrices = priceHandler.getCostPriceList();
                stream.close();
            }
            xlsxPackage.close();
        } catch (Throwable e) {
            SCTLogger.error(e.getMessage());
        }
    }

    private void parseProduct() {
        Map<String, CostPrice> priceMap = new HashMap<>();
        for (CostPrice costPrice : costtPrices) {
            if (!priceMap.containsKey(costPrice.getDevtype())) {
                priceMap.put(costPrice.getDevtype(), costPrice);
            }
        }
        Map<String, Integer> pkgIndexMap = new HashMap<>();
        for (PkgIndex pkgIndex : pkgIndexes) {
            if (!pkgIndexMap.containsKey(pkgIndex.getPkgFile())) {
                pkgIndexMap.put(pkgIndex.getPkgFile(), pkgIndex.getRowId());
            }
        }

        ProductHander wordProductParser = new ProductHander(priceMap);

        for (String file : allFiles) {
            monitor.worked(3);
            if (!FieldUtils.getFileId(file).equals("")) {
                String name = FieldUtils.getPkgName(file);
                // 如果不在选中的包内，跳过
                if (!Arrays.asList(selectNames).contains(name)) {
                    continue;
                }
                Integer rowId;
                if (pkgIndexMap.containsKey(FieldUtils.getSubStr(file, 2))) {
                    rowId = pkgIndexMap.get(FieldUtils.getSubStr(file, 2));
                } else {
                    continue;
                }

                // 预制舱式二次单独解析
                if (file.contains("预制舱式二次")) {
                    YZCProductHander yzc1 = new YZCProductHander(priceMap, 1);

                    Product product = new Product(file);
                    product.setRowId(rowId);
                    product.setBidno(FieldUtils.getFileId(file));
                    product.setName(FieldUtils.getPkgName(file));
                    product.setOrderid(0);
                    product.setQuote("否");
                    product.setNumber("一");
                    product.setDevname("预制舱舱体");
                    productlist.add(product);
                    yzc1.parse(file, productlist, rowId, 1);
                    YZCProductHander yzc2 = new YZCProductHander(priceMap, 2);

                    Product product2 = new Product(file);
                    product2.setRowId(rowId);
                    product2.setBidno(FieldUtils.getFileId(file));
                    product2.setName(FieldUtils.getPkgName(file));
                    product2.setOrderid(productlist.get(productlist.size() - 1).getOrderid());
                    product2.setQuote("否");
                    product2.setNumber("二");
                    product2.setDevname("智能变电站故障录波装置");
                    productlist.add(product2);
					int count2 = productlist.size();
					yzc2.parse(file, productlist, rowId, productlist.get(productlist.size() - 1).getOrderid());
					// 如果没有则删除
					if (productlist.size() == count2) {
						productlist.remove(product2);
					}

                    YZCProductHander yzc3 = new YZCProductHander(priceMap, 3);
                    Product product3 = new Product(file);
                    product3.setRowId(rowId);
                    product3.setBidno(FieldUtils.getFileId(file));
                    product3.setName(FieldUtils.getPkgName(file));
                    product3.setOrderid(productlist.get(productlist.size() - 1).getOrderid());
                    product3.setQuote("否");
                    product3.setNumber("三");
                    product3.setDevname("智能变电站时间同步装置");
                    productlist.add(product3);
					int count3 = productlist.size();
					yzc3.parse(file, productlist, rowId, productlist.get(productlist.size() - 1).getOrderid());
					// 如果没有则删除
					if (productlist.size() == count3) {
						productlist.remove(product3);
					}

                    YZCProductHander yzc4 = new YZCProductHander(priceMap, 4);

                    Product product4 = new Product(file);
                    product4.setRowId(rowId);
                    product4.setBidno(FieldUtils.getFileId(file));
                    product4.setName(FieldUtils.getPkgName(file));
                    product4.setOrderid(productlist.get(productlist.size() - 1).getOrderid());
                    product4.setQuote("否");
                    product4.setNumber("四");
                    product4.setDevname("智能变电站电能量采集终端");
                    productlist.add(product4);
					int count4 = productlist.size();
					yzc4.parse(file, productlist, rowId, productlist.get(productlist.size() - 1).getOrderid());
					// 如果没有则删除
					if (productlist.size() == count4) {
						productlist.remove(product4);
					}
					
                    YZCProductHander yzc5 = new YZCProductHander(priceMap, 5);
                    Product product5 = new Product(file);
                    product5.setRowId(rowId);
                    product5.setBidno(FieldUtils.getFileId(file));
                    product5.setName(FieldUtils.getPkgName(file));
                    product5.setOrderid(productlist.get(productlist.size() - 1).getOrderid());
                    product5.setQuote("否");
                    product5.setNumber("五");
                    product5.setDevname("变电站智能一体化电源系统");
                    productlist.add(product5);
                    int count5 = productlist.size();
					yzc5.parse(file, productlist, rowId, productlist.get(productlist.size() - 1).getOrderid());
					// 如果没有则删除
					if (productlist.size() == count5) {
						productlist.remove(product5);
					}
                } else if (file.contains("监控系统")) {
					if ((file.contains("110kV") || file.contains("66kV")) && !file.contains("智能")) {
						MonitorHander monitorHander = new MonitorHander(priceMap);
						monitorHander.parse(file, productlist, rowId);
					} else {
						Monitor2Hander monitorHander = new Monitor2Hander(priceMap);
						monitorHander.parse(file, productlist, rowId);
					}
                } else if (file.contains("预制仓")) {
					GeneralYZCHander generalYZCHander = new GeneralYZCHander(priceMap);
					generalYZCHander.parse(file, productlist, rowId);
                	
                } else {
                	//保护类型
                    wordProductParser.parse(file, productlist, rowId);
                }
                Product product = new Product(file);
                product.setRowId(rowId);
                product.setBidno(FieldUtils.getFileId(file));
                product.setName(FieldUtils.getPkgName(file));
                product.setOrderid(1000);
                product.setQuote("否");
                product.setDevname("其他组件材料");
                productlist.add(product);
                product = new Product(file);
                product.setBidno(FieldUtils.getFileId(file));
                product.setName(FieldUtils.getPkgName(file));
                product.setOrderid(1001);
                product.setRowId(rowId);
                product.setDevname("其他费用（运输培训设计联络现场服务等）");
                product.setArea("中国");
                product.setQuote("是");
                product.setSupply("思源弘瑞");
                product.setUnit("套");
                product.setCount("1");
                product.setOcunnt("1");
                if (file.contains("监控") || file.contains("预制舱")) {
                    product.setOdevtype("设计联络会(系统站)：其他费用（运输培训设计联络现场服务等）");
                } else {
                    product.setOdevtype("设计联络会(单装置)：其他费用（运输培训设计联络现场服务等）");
                }
                productlist.add(product);
            }
        }
    }


}
