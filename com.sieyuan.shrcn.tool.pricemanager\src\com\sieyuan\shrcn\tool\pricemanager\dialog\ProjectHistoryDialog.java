/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.util.ArrayList;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.List;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.dao.ProjectDao;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.Project;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;

/**
 * 历史工程
 * <AUTHOR>
 * @date 2019-8-6
 */
public class ProjectHistoryDialog extends WrappedDialog {
    private UIPreferences perference = UIPreferences.newInstance();

    private List projectList;
    private String selectProjectName;

    public ProjectHistoryDialog(Shell parentShell) {
        super(parentShell);
    }

    @Override
    protected void buttonPressed(int buttonId) {
        String[] selected = projectList.getSelection();
        if (buttonId == OK) {
            if (selected != null && selected.length > 0) {
                selectProjectName = projectList.getSelection()[0];
                perference.setInfo("com.shrcn.pricemangertool.curentprojectname", selectProjectName);
                GlobalData.getInstance().getSqliteHelper().destroyed();
                NavigatView.refreshTree();
                super.buttonPressed(buttonId);
            } else {
                DialogHelper.showWarning("请选择待打开工程！");
            }
        } else if (buttonId == 100) {

            if (selected != null && selected.length > 0) {
                if (DialogHelper.showConfirm("确定删除所选工程？", "是", "否")) {
                    for (String name : selected) {
                        if (name.equals(perference.getInfo("com.shrcn.pricemangertool.curentprojectname"))) {
                            DialogHelper.showWarning(name + "工程正在被使用不允许删除！");
                            continue;
                        }
                        ProjectDao.deleteProject(name);
                        FieldUtils.delDir(new File(DirManager.getProjectDir(name)));
                    }
                    initData();
                }
            } else {
                DialogHelper.showWarning("请选择待删工程！");
            }
            super.buttonPressed(buttonId);
        } else {
            super.buttonPressed(buttonId);
        }
    }

    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("历史工程");
    }

    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, OK, "打开", true);
        createButton(parent, 100, "删除", false);
        createButton(parent, CANCEL, "关闭", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        GridData gridData = new GridData(GridData.FILL_BOTH);
        gridData.heightHint = 290;
        projectList = SwtUtil.createList(parent, gridData);
        initData();
        projectList.addMouseListener(new MouseListener() {

            @Override
            public void mouseDoubleClick(MouseEvent e) {
                buttonPressed(OK);
            }

            @Override
            public void mouseDown(MouseEvent e) {}

            @Override
            public void mouseUp(MouseEvent e) {}
        });
        return super.createDialogArea(parent);
    }

    @Override
    protected Point getInitialSize() {
        return new Point(500, 320);
    }

    public String getSelectDevName() {
        return selectProjectName;
    }
    
	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

    private void initData() {
        java.util.List<String> lis = new ArrayList<>();
        java.util.List<Project> projectlis = new ArrayList<>();
        projectlis = ProjectDao.findAll();
        for (Project project : projectlis) {
            lis.add(project.getProjectName());
        }
        if (lis.size() > 0) {
            projectList.setItems(lis.toArray(new String[lis.size()]));
            projectList.select(0);
        }
    }
}
