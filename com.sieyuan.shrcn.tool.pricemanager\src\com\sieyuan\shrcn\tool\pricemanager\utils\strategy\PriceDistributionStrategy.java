package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.util.List;
import java.util.Map;

import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;

/**
 * 价格分配策略接口
 */
public interface PriceDistributionStrategy {
    
    /**
     * 分配价格到产品
     * 
     * @param result 价格计算结果
     * @param productMap 产品映射
     */
    void distributePrices(PriceCalculationResult result, Map<Integer, List<PkgProduct>> productMap);
} 