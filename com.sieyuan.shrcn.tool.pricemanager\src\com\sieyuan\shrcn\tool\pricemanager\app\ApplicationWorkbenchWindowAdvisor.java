package com.sieyuan.shrcn.tool.pricemanager.app;

import org.eclipse.ui.IWorkbenchWindow;
import org.eclipse.ui.application.ActionBarAdvisor;
import org.eclipse.ui.application.IActionBarConfigurer;
import org.eclipse.ui.application.IWorkbenchWindowConfigurer;

import com.shrcn.found.ui.app.AbstractWorkbenchWindowAdvisor;

/**
 * @Description: ApplicationWorkbenchWindowAdvisor
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON><PERSON>uan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:47:08
 */
public class ApplicationWorkbenchWindowAdvisor extends AbstractWorkbenchWindowAdvisor {

    public ApplicationWorkbenchWindowAdvisor(IWorkbenchWindowConfigurer configurer) {
        super(configurer);
    }

    public ActionBarAdvisor createActionBarAdvisor(IActionBarConfigurer configurer) {
        return new ApplicationActionBarAdvisor(configurer);
    }

    public void preWindowOpen() {
		super.preWindowOpen();
		IWorkbenchWindowConfigurer configurer = getWindowConfigurer();
		configurer.setShowStatusLine(true);
		configurer.setShowProgressIndicator(true);
    }
    
	public void postWindowOpen() {
		super.postWindowOpen();
		IWorkbenchWindow window = getWindowConfigurer().getWindow();
		//窗口最大化
		window.getShell().setMaximized(true);
	}

	@Override
	public void postWindowCreate() {
		super.postWindowCreate();
		getWindowConfigurer().getWindow().getShell().setMaximized(true);   

	}
}
