package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Combo;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.file.util.FileManipulate;
import com.shrcn.found.ui.UIConstants;
import com.shrcn.found.ui.app.WrappedTitleAreaDialog;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.ToolHandler;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.model.BidEvaluationResult;
import com.sieyuan.shrcn.tool.pricemanager.model.BidPkgModel;
import com.sieyuan.shrcn.tool.pricemanager.utils.ExcelExportUtil;
import com.sieyuan.shrcn.tool.pricemanager.views.table.TableFactory;

/**
 * 开标测算
 * 
 * <AUTHOR>
 * 
 */
public class BidEvalResultDialog extends WrappedTitleAreaDialog implements ToolHandler {

	private static final String N_TEXT = "nText";
	private static final String M_TEXT = "mText";
	private static final String A_TEXT = "aText";
	private static final String BUSINESS_TEXT = "businessText";
	private static final String PRICE_TEXT = "priceText";
	private static final String TECH_TEXT = "techText";

	private RKTable rkTb;

	private Text techText;
	private Text priceText;
	private Text businessText;
	private Text aText;
	private Text mText;
	private Text nText;
	private Text finalBidText;
	private Text basePriceText;

	private UIPreferences pref = UIPreferences.newInstance();
	private double tech;
	private double price;
	private double business;
	private double aValue;
	private double mValue;
	private double nValue;
	private Combo pkgCombo;

	private Map<String, BidEvaluationResult[]> ranks;
	private List<BidPkgModel> pkgs;

	public BidEvalResultDialog(Shell parentShell, Map<String, BidEvaluationResult[]> ranks, List<BidPkgModel> pkgs) {
		super(parentShell);
		this.pkgs = pkgs;
		this.ranks = ranks;
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		setTitle("测算结果");
		setMessage("查看测算结果并导出测算结果。");
		Composite container = new Composite(parent, SWT.FILL);
		container.setLayout(new GridLayout(1, false));
		container.setLayoutData(new GridData(GridData.FILL_BOTH));

		Composite topComp = SwtUtil.createComposite(container, new GridData(GridData.FILL_HORIZONTAL), 20);

		String[] names = new String[pkgs.size()];
		for (int i = 0; i < pkgs.size(); i++) {
			names[i] = pkgs.get(i).getPkgName();
		}
		pkgCombo = SwtUtil.createLabelCombo(topComp, "包名:", names);
		techText = SwtUtil.createLabelText(topComp, "技术(%):");
		priceText = SwtUtil.createLabelText(topComp, "价格(%):");
		businessText = SwtUtil.createLabelText(topComp, "商务(%):");
		aText = SwtUtil.createLabelText(topComp, "a(%):");
		mText = SwtUtil.createLabelText(topComp, "m:");
		nText = SwtUtil.createLabelText(topComp, "n:");

		finalBidText = SwtUtil.createLabelText(topComp, "最终报价A:");
		basePriceText = SwtUtil.createLabelText(topComp, "基准价B:");

		techText.setEditable(false);
		priceText.setEditable(false);
		businessText.setEditable(false);
		aText.setEditable(false);
		mText.setEditable(false);
		nText.setEditable(false);
		finalBidText.setEditable(false);
		basePriceText.setEditable(false);

		finalBidText.setBackground(UIConstants.YELLOW);
		basePriceText.setBackground(UIConstants.YELLOW);

		Composite tbottomComp = SwtUtil.createComposite(container, new GridData(GridData.FILL_BOTH), 1);

		this.rkTb = TableFactory.getBidEvalResultTable(tbottomComp);
		rkTb.getTable().setLayoutData(new GridData(GridData.FILL_BOTH));

		pkgCombo.addSelectionListener(new SelectionListener() {

			@Override
			public void widgetSelected(SelectionEvent e) {
				refreshTable();
			}

			@Override
			public void widgetDefaultSelected(SelectionEvent e) {
				// widgetDefaultSelected
			}
		});
		pkgCombo.select(0);

		initData();

		return container;
	}

	private void refreshTable() {
		String pkgName = pkgCombo.getText();
		BidEvaluationResult[] rankArray = ranks.get(pkgName);
		rkTb.setInput(Arrays.asList(rankArray));
		for (BidPkgModel pkg : pkgs) {
			if (pkg.getPkgName().equals(pkgName)) {
				finalBidText.setText(String.valueOf(pkg.getFinalBid()));
				basePriceText.setText(String.valueOf(pkg.getBasePrice()));
			}
		}
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == IDialogConstants.OK_ID) {
			exportExcel();
			return;
		}
		super.buttonPressed(buttonId);
	}

	private void exportExcel() {

		final String filePath = DialogHelper.selectDirectory(getShell(), SWT.SAVE, "导出测算结果", "请选择导出测算结果保存的路径");
		ProgressManager.execute(new IRunnableWithProgress() {
			@Override
			public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
				exportResult(filePath, monitor);
				DialogHelper.showAsynInformation("测算投标评估结果导出成功");
			}
		});
	}

	private void exportResult(String filePath, IProgressMonitor monitor) {

		monitor.beginTask("导出测算投标评估结果", pkgs.size());
		for (BidPkgModel pkgModel : pkgs) {
			monitor.setTaskName("导出测算包" + pkgModel.getPkgName() + "投标评估结果");

			// 计算最终报价A
			double finalBid = pkgModel.getFinalBid();

			// 计算基准价B
			double basePrice = pkgModel.getBasePrice();

			// 获取价格得分排名、价格分差、总分差和技术补分
			BidEvaluationResult[] rankings = ranks.get(pkgModel.getPkgName());
			// 输出结果
			SCTLogger.debug("最终报价A: " + String.format("%.2f", finalBid));
			SCTLogger.debug("基准价B: " + String.format("%.2f", basePrice));
			SCTLogger.debug("价格得分排名及价格分差、总分差、技术补分：");

			// 1. 定义表头和数据
			String[] headers = { "序号", "公司全称", "报价", "价格得分", "价格排名", "价格分差", "总分差", "技术补分" };
			List<Object[]> data = new ArrayList<>();

			int index = 0;
			for (BidEvaluationResult ranking : rankings) {
				index++;
				data.add(new Object[] { index, ranking.getCompanyName(), ranking.getBid(), ranking.getScore(), ranking.getRank(), ranking.getPriceDiff(), ranking.getTotalDiff(), ranking.getTechBonus() });
				SCTLogger.debug("厂家 " + ranking.getCompanyName() + " 报价 " + String.format("%.2f", ranking.getBid()) + " 的得分: " + String.format("%.2f", ranking.getScore()) + " 排名: " + ranking.getRank() + " 价格分差: " + String.format("%.2f", ranking.getPriceDiff()) + " 总分差: "
						+ String.format("%.2f", ranking.getTotalDiff()) + " 技术补分: " + String.format("%.2f", ranking.getTechBonus()));
			}

			// 2. 定义底部信息（技术%、价格%、商务%、a、m、n）
			Map<String, Object> footerInfo = new LinkedHashMap<>();
			footerInfo.put("技术(%)", tech);
			footerInfo.put("价格(%)", price);
			footerInfo.put("商务(%)", business);
			footerInfo.put("a", aValue);
			footerInfo.put("m", mValue);
			footerInfo.put("n", nValue);

			footerInfo.put("最终报价A", Double.parseDouble(String.format("%.2f", finalBid)));
			footerInfo.put("基准价B", Double.parseDouble(String.format("%.2f", basePrice)));

			// 3. 调用工具类导出Excel
			String fileName = "包" + pkgModel.getPkgName() + "投标评估结果.xlsx";
			String file = filePath + File.separator + fileName;
			if (FileManipulate.exist(file)) {
				FileManipulate.deleteFile(file);
			}
			ExcelExportUtil.exportToExcel(file, "投标评估结果", headers, data, footerInfo);
			monitor.worked(1);
		}
		monitor.done();

	}

	private void initData() {
		techText.setText(pref.getInfo(BidEvalDialog.class.getName() + TECH_TEXT));
		priceText.setText(pref.getInfo(BidEvalDialog.class.getName() + PRICE_TEXT));
		businessText.setText(pref.getInfo(BidEvalDialog.class.getName() + BUSINESS_TEXT));
		aText.setText(pref.getInfo(BidEvalDialog.class.getName() + A_TEXT));
		mText.setText(pref.getInfo(BidEvalDialog.class.getName() + M_TEXT));
		nText.setText(pref.getInfo(BidEvalDialog.class.getName() + N_TEXT));

		tech = Double.parseDouble(techText.getText());
		price = Double.parseDouble(priceText.getText());
		business = Double.parseDouble(businessText.getText());
		aValue = Double.parseDouble(aText.getText());
		mValue = Double.parseDouble(mText.getText());
		nValue = Double.parseDouble(nText.getText());

		refreshTable();
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {

		createButton(parent, IDialogConstants.OK_ID, "导出", true);
		createButton(parent, IDialogConstants.CANCEL_ID, IDialogConstants.CANCEL_LABEL, false);
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("测算结果");
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(950, 600);
	}

	@Override
	public void handle(String arg0) {
		SCTLogger.debug(arg0);
	}

}
