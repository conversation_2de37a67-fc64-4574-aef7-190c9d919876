/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.TimeCounter;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;

/**
 * @Description:PkgOutTemplateDialog
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 */
public class ProductOutTemplateDialog extends WrappedDialog {

    private String INPUT = "input";
    private Text inputPath;
    private String OUTPUT = "output";
    private Text outputPATH;

    private UIPreferences perference = UIPreferences.newInstance();

    public ProductOutTemplateDialog(Shell parentShell) {
        super(parentShell);
    }

    @Override
    protected void buttonPressed(int buttonId) {
        if (buttonId == OK) {
            String infopath = getClass().getName();
            final String input = inputPath.getText();
            final String output = outputPATH.getText();
            String msg = checkInput();
            if (msg != null) {
                DialogHelper.showWarning(msg);
                return;
            }
            perference.setInfo(infopath + INPUT, input);
            perference.setInfo(infopath + OUTPUT, output);

            ProgressManager.execute(new IRunnableWithProgress() {
                @Override
                public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {

                    List<String> paths = new ArrayList<>();
                    paths = FieldUtils.getAllFilePaths(new File(input), paths);
                    monitor.beginTask("正在生成数据中，请稍候...", 3 * paths.size());
                    for (String path : paths) {
                        String bidNo = FieldUtils.getFileId(path);
                        if (!StringUtils.isEmpty(bidNo) && path.endsWith(".xlsx")) {
                            try {
                                String destFile = output + File.separator + bidNo + ".xlsx";
                                if (!new File(destFile).exists()) {
                                    FieldUtils.copyFile(path, destFile);
                                    monitor.worked(3);
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    TimeCounter.end("生成总耗时");
                    ConsoleManager.getInstance().append("模板文件生成成功！");
                    monitor.done();
                }
            });
        }
        super.buttonPressed(buttonId);
    }

    private String checkInput() {
        return null;
    }

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("生成开标文件模板");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "生成", true);
        createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        Composite container = (Composite)super.createDialogArea(parent);
        container.setLayout(new GridLayout(3, false));
        inputPath = SwtUtil.createDirectorySelector(container, "开标文件夹：", "请选择开标文件夹!");
        outputPATH = SwtUtil.createDirectorySelector(container, "生成模板文件夹：", "请选择生成模板文件存放的文件夹!");
        init();
        return container;
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(600, 200);
    }

    private void init() {
        String infopath = getClass().getName();
        String input = perference.getInfo(infopath + INPUT);
        String output = perference.getInfo(infopath + OUTPUT);
        if (!StringUtils.isEmpty(input)) {
            inputPath.setText(input);
        }
        if (!StringUtils.isEmpty(output)) {
            outputPATH.setText(output);
        }
    }

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
