<?xml version="1.0" encoding="UTF-8"?>
<plugin>
   <extension
         id="application"
         point="org.eclipse.core.runtime.applications">
      <application
            cardinality="singleton-global"
            thread="main"
            visible="true">
         <run
            class="com.sieyuan.shrcn.tool.pricemanager.app.Application">
         </run>
      </application>
   </extension>
   <extension
         point="org.eclipse.ui.perspectives">
      <perspective
            class="com.sieyuan.shrcn.tool.pricemanager.app.Perspective"
            id="com.sieyuan.shrcn.tool.pricemanager.app.perspective"
            icon="icons/price16.png"
            name="%app.name">
      </perspective>
   </extension>
   <extension
         point="org.eclipse.ui.views">
      <view
            class="com.sieyuan.shrcn.tool.pricemanager.views.NavigatViewPart"
            icon="icons/price16.png"
            id="com.sieyuan.shrcn.tool.pricemanager.app.navigatview"
            name="%app.nav"
            restorable="true">
      </view>
      <view
            class="com.sieyuan.shrcn.tool.pricemanager.views.DetailViewPart"
            id="com.sieyuan.shrcn.tool.pricemanager.app.detailview"
            icon="icons/deviceEditor.png"
            name="%app.view"
            restorable="true">
      </view>
      <view
            class="com.sieyuan.shrcn.tool.pricemanager.views.InfoViewPart"
            id="com.sieyuan.shrcn.tool.pricemanager.app.infoview"
            icon="icons/console.gif"
            name="%app.info"
            restorable="true">
      </view>
   </extension>
   <extension
        point="org.eclipse.ui.bindings">
        <scheme
            id="miner.accelerator"
            name="myAccelerator"
            parentId="org.eclipse.ui.defaultAcceleratorConfiguration">
       </scheme>
        <key
              commandId="com.sieyuan.shrcn.tool.pricemanager.action.SaveProjectAction"
              schemeId="miner.accelerator"
              sequence="CTRL+S">
        </key>
        <key
              commandId="com.sieyuan.shrcn.tool.pricemanager.action.NewProjectAction"
              schemeId="miner.accelerator"
              sequence="CTRL+N">
        </key>
		<key
              commandId="com.sieyuan.shrcn.tool.pricemanager.action.EnvirmentSettingAction"
              schemeId="miner.accelerator"
              sequence="CTRL+E">
        </key>
		<key
              commandId="com.sieyuan.shrcn.tool.pricemanager.action.LogSettingAction"
              schemeId="miner.accelerator"
              sequence="CTRL+L">
        </key>
		<key
              commandId="com.sieyuan.shrcn.tool.pricemanager.action.ProductTemplateAction"
              schemeId="miner.accelerator"
              sequence="CTRL+M">
        </key>
    </extension>
	<extension
          point="org.eclipse.ui.commands">
        <command
              id="com.sieyuan.shrcn.tool.pricemanager.action.NewProjectAction"
              name="New">
        </command>
        <command
              id="com.sieyuan.shrcn.tool.pricemanager.action.SaveProjectAction"
              name="Save">
        </command>
		<command
              id="com.sieyuan.shrcn.tool.pricemanager.action.EnvirmentSettingAction"
              name="Edit">
        </command>
		<command
              id="com.sieyuan.shrcn.tool.pricemanager.action.LogSettingAction"
              name="Log">
        </command>
		<command
              id="com.sieyuan.shrcn.tool.pricemanager.action.ProductTemplateAction"
              name="Tool">
        </command>
    </extension>
   <extension
         id="product"
         point="org.eclipse.core.runtime.products">
      <product
            application="com.sieyuan.shrcn.tool.pricemanager.application"
            description="%app.name"
            name="%app.name">
	     <property
               name="preferenceCustomization"
               value="customization.ini">
         </property>
         <property
               name="appName"
               value="%app.name">
         </property>
         <property
               name="windowImages"
               value="icons/price16.png,icons/price32.png">
         </property>
         <property
               name="preferenceCustomization"
               value="AppPref.ini">
         </property>
         <property
               name="startupProgressRect"
               value="0,248,530,10">
         </property>
         <property
               name="startupMessageRect"
               value="0,225,300,16">
         </property>
      </product>
   </extension>
   
   <extension
         point="com.shrcn.found.ui.AppMenuExtension">  
         <menu perspective="com.sieyuan.shrcn.tool.pricemanager.app.perspective"
            id="menu.deviceMenu"
            text="%menu.file">
         <action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.NewProjectAction"
               icon="new_project.png"
               text="%action.NewProjectAction"
               style="3"
               >
         </action>
		 <action
               class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.SaveProjectAction"
               icon="save_edit.gif"
               style="3"
               text="%action.SaveProjectAction" >
         </action>
         <action
               class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.HistoryProjectAction"
               icon="open_project.png"
               style="3"
               text="%action.OpenProjectAction" >
         </action>
         <action
               class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.ImportProjectAction"
               icon="import_project.png"
               style="3"
               text="%action.ImportProjectAction" >
         </action>
         <action
               class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.ExportProjectAction"
               icon="export_project.png"
               style="3"
               text="%action.ExportProjectAction" >
         </action>
	 </menu>
	 <menu perspective="com.sieyuan.shrcn.tool.pricemanager.app.perspective"
            id="menu.deviceMenu"
            text="%menu.export">
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.AnalysisByNameAction"
               icon="statistics_analysis.png"
               text="%action.AnalysisByNameAction"
               style="3"
               >
         </action>
		 <action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.AnalysisByIdAction"
               icon="analysis_id.png"
               text="%action.AnalysisByIdAction"
               style="1"
               >
         </action>
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.AnalysisByDiffIdAction"
               icon="product_doc.png"
               text="%action.AnalysisByDiffIdAction"
               style="1"
               >
		</action>
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.AnalysisByProductNameAction"
               icon="product_doc.png"
               text="%action.AnalysisByProductNameAction"
               style="1"
               >
		</action>
	    <action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.AnalysisByPriceRateAction"
               icon="product_doc.png"
               text="%action.AnalysisByPriceRateAction"
               style="1"
               >
        </action>
        <action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.AnalysisAllAction"
               icon="product_doc.png"
               text="%action.AnalysisAllAction"
               style="1"
               >
		</action>
      </menu>
      <menu perspective="com.sieyuan.shrcn.tool.pricemanager.app.perspective"
            id="menu.deviceMenu"
            text="%menu.tool">
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.ProductTemplateAction"
               icon="id_template.png"
               text="%action.ProductTemplateAction"
               style="1"
               >
		</action>
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.ProductMergeAction"
               icon="product_merge.pngx"
               text="%action.ProductMergeAction"
               style="1"
               >
		</action>
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.ProductModifyAction"
               icon="oname_item.png"
               text="%action.ProductModifyAction"
               style="1"
               >
		</action>
	    <action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.LineQuotationAction"
               icon="line_tool.png"
               text="%action.LineQuotationAction"
               style="1"
               >
		</action>
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.BidEvalAction"
               icon="oname_item.png"
               text="%action.BidEvalAction"
               style="1"
               >
		</action>
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.CheckCompConfigAction"
               icon="oname_item.png"
               text="%action.CheckCompConfigAction"
               style="1"
               >
		</action>
		<action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.TechParamCheckAction"
               icon="oname_item.png"
               text="%action.TechParamCheckAction"
               style="1"
               >
		</action>
      </menu>
	  <menu perspective="com.sieyuan.shrcn.tool.pricemanager.app.perspective"
            id="menu.deviceMenu"
            text="%menu.setting">
         <action
			   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.EnvirmentSettingAction"
               icon="envirment_setting.png"
               text="%action.EnvirmentSettingAction"
               style="3"
               >
         </action>
         <action
               class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.LogSettingAction"
               icon="log_setting.png"
               style="1"
               text="%action.LogSettingAction" >
         </action>
          <action
               class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.OptionAction"
               icon="options.png"
               style="1"
               text="%action.OptionAction" >
         </action>
	 </menu>
      <menu perspective="com.sieyuan.shrcn.tool.pricemanager.app.perspective"
            id="menu.help"
            text="%menu.help">
	
         <action class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.HelpAction" 
         	icon="help.gif"
         	text="%action.HelpAction"/>
         
         <action
         	   class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.AboutAction"
               icon="about.png"
               text="%action.AboutAction">
         </action>
         <action class="org.eclipse.jface.action.Separator"/>
	     <action
	        class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.action.QuitAction"
            icon="quit16.png"
            style="3"
            text="%action.QuitAction"/>
      </menu>
   </extension>

</plugin>
