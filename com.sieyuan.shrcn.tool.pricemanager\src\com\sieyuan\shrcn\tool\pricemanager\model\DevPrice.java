package com.sieyuan.shrcn.tool.pricemanager.model;

import java.util.Objects;

/**
 * @Description:包数据信息
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-23 上午11:04:56
 */
public class DevPrice {
    private Integer id;// 主键
    private String area;// 区域
    private String bidno;// 技术规范id
    private String costprice = ""; // 成本价格
    private String count;// 数量
    private String devname = "";// 元件名称
    private String devtype = "";
    private String lnType = ""; // 自产或者外购类型
    private String name;// 名称
    private String number = "";
    private String ocunnt;// 数量
    private String odevtype = "";
    private Integer orderid;// 序号
    private String price = ""; // 成本价格
    private String searchType = "";
    private String supply = ""; // 制造商
    private String unit = ""; // 单位
    private String quote;// 是否报价
    
    private String file; // 文件名

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        DevPrice devPrice = (DevPrice)o;
        return Objects.equals(bidno, devPrice.bidno) && Objects.equals(devname, devPrice.devname) 
            && Objects.equals(odevtype, devPrice.odevtype) && Objects.equals(number, devPrice.number) && Objects.equals(quote, devPrice.quote);
    }

    public String getArea() {
        return area;
    }

    public String getBidno() {
        return bidno;
    }

    public String getCostprice() {
        return costprice;
    }

    public String getCount() {
        return count;
    }

    public String getDevname() {
        return devname;
    }

    public String getDevtype() {
        return devtype;
    }

    public Integer getId() {
        return id;
    }

    public String getLnType() {
        return lnType;
    }

    public String getName() {
        return name;
    }

    public String getNumber() {
        return number;
    }

    public String getOcunnt() {
        return ocunnt;
    }

    public String getOdevtype() {
        return odevtype;
    }

    public Integer getOrderid() {
        return orderid;
    }

    public String getPrice() {
        return price;
    }

    public String getSearchType() {
        return searchType;
    }

    public String getSupply() {
        return supply;
    }

    public String getUnit() {
        return unit;
    }

    @Override
    public int hashCode() {
        return Objects.hash(bidno, odevtype);
    }

    public void setArea(String area) {
        this.area = area;
    }

    public void setBidno(String bidno) {
        this.bidno = bidno;
    }

    public void setCostprice(String costprice) {
        this.costprice = costprice;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public void setDevname(String devname) {
        this.devname = devname;
    }

    public void setDevtype(String devtype) {
        this.devtype = devtype;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setLnType(String lnType) {
        this.lnType = lnType;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public void setOcunnt(String ocunnt) {
        this.ocunnt = ocunnt;
    }

    public void setOdevtype(String odevtype) {
        this.odevtype = odevtype;
    }

    public void setOrderid(Integer orderid) {
        this.orderid = orderid;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public void setSupply(String supply) {
        this.supply = supply;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getQuote() {
        return quote;
    }

    public void setQuote(String quote) {
        this.quote = quote;
    }

	public String getFile() {
		return file;
	}

	public void setFile(String file) {
		this.file = file;
	}
    
    
}
