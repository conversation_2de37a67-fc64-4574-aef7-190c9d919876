package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.List;

import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;
import com.sieyuan.shrcn.tool.pricemanager.model.LimitPrice;

/**
 * @Description:限价处理类
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON>eyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-23 上午11:18:45
 
 */
@SuppressWarnings("rawtypes")
public class LimitPriceHandler extends SheetsHandler {

    private LimitPrice limitPrice;
    private List<LimitPrice> limitPriceList;

    public LimitPriceHandler() {
        limitPriceList = new ArrayList<>();
        limitPrice = new LimitPrice();
    }

    @Override
    public void cell(String cellReference, String formattedValue, XSSFComment comment) {
        super.cell(cellReference, formattedValue, comment);
        if (currentRow > 1 && !isEmpty(formattedValue)) {
            if (currentCol == 3) {
                limitPrice.setProduct(formattedValue);
            } else if (currentCol == 4) {
                limitPrice.setCount(Integer.valueOf(formattedValue));
            } else if (currentCol == 5) {
                limitPrice.setUnit(formattedValue);
            } else if (currentCol == 6) { // 价格
                limitPrice.setLimitprice(formattedValue);
            }
        }
    }

    @Override
    public void endRow(int rowNum) {
        if (limitPrice != null && limitPrice.getProduct() != null) {
            limitPriceList.add(limitPrice);
        }
    }

    public List<LimitPrice> getLimitPriceList() {
        return limitPriceList;
    }

    public void setLimitPriceList(List<LimitPrice> limitPriceList) {
        this.limitPriceList = limitPriceList;
    }

    @Override
    public void startRow(int rowNum) {
        super.startRow(rowNum);
        this.limitPrice = new LimitPrice();
    }

}
