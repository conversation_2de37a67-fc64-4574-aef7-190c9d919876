/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.dnd.DND;
import org.eclipse.swt.dnd.DropTarget;
import org.eclipse.swt.dnd.FileTransfer;
import org.eclipse.swt.dnd.Transfer;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.file.excel.Xls2007Parser;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.dialog.MessageDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.sieyuan.shr.u21.ui.table.Table;
import com.sieyuan.shrcn.tool.pricemanager.dao.CostPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.DevPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgErrInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.DevPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.FileItem;
import com.sieyuan.shrcn.tool.pricemanager.sax.DevPriceHandler;
import com.sieyuan.shrcn.tool.pricemanager.sax.PackageImporter;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.utils.RealPriceResolver2;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;
import com.sieyuan.shrcn.tool.pricemanager.views.table.FileTableMode;

/**
 * @Description:价格导入
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 
 */
public class CalImportDialog extends WrappedDialog {

    private FileTableMode fileTableMode;
    private Table table;
    // 相同ID计算表替换
    private Boolean isAll;
    private Integer rowId;

    public CalImportDialog(Shell parentShell, boolean isAll, Integer rowId) {
        super(parentShell);
        this.isAll = isAll;
        this.rowId = rowId;
    }

    @SuppressWarnings("unchecked")
    @Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == OK) {
	        List<FileItem> fileItems = fileTableMode.getItems();
			if (fileItems == null || fileItems.size() == 0) {
				DialogHelper.showAsynWarning("请选择导入的计算表文件！");
				return;
			}
			if (!isAll && fileItems.size() > 1) {
				DialogHelper.showAsynWarning("只能选择一个计算表进行导入！");
				return;
			}
			importAll();
			MessageDialog.openInformation(this.getShell(), "导入计算表数据", "导入计算表数据成功");
			NavigatView.refreshTree();
		} else if (buttonId == IDialogConstants.OPEN_ID) {
			FileDialog dialog = new FileDialog(this.getShell(), SWT.OPEN | SWT.MULTI);
			String[] filter = { "*.xlsx" };// 指定文件格式
			dialog.setFilterExtensions(filter);
			@SuppressWarnings("unused")
			String fileName = dialog.open();// 返回最后一个选择文件的全路径
			String[] fileNames = dialog.getFileNames();// 返回所有选择的文件名，不包括路径
			String path = dialog.getFilterPath();// 返回选择的路径，这个和fileNames配合可以得到所有的文件的全路径
			List<FileItem> items = fileTableMode.getItems();
			FileItem item = new FileItem();
			for (String fileItem : fileNames) {
				item = new FileItem();
				item.setChecked(true);
				item.setStatus("");
				item.setFileName(fileItem);
				item.setFileType(fileItem.substring(fileItem.lastIndexOf(".") + 1));
				File file = new File(path + File.separator + fileItem);
				item.setAbsFileName(file.getAbsolutePath());
				item.setFileSize(FieldUtils.getFileSize(file));
				items.add(item);
			}
			fileTableMode.setItems(items);
			table.redraw();
			return;
		} else if (buttonId == IDialogConstants.NEXT_ID) {
			List<FileItem> itemsList = new ArrayList<>();
			List<FileItem> items = fileTableMode.getItems();
			for (FileItem fileItem : items) {
				if (!fileItem.isChecked()) {
					itemsList.add(fileItem);
				}
			}
			fileTableMode.setItems(itemsList);
			table.redraw();
			return;
		}
		super.buttonPressed(buttonId);
	}

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("导入计算表");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        createButton(parent, IDialogConstants.OK_ID, "导入", true);
        createButton(parent, IDialogConstants.OPEN_ID, "添加文件", false);
        createButton(parent, IDialogConstants.NEXT_ID, "移除文件", false);
        createButton(parent, IDialogConstants.CANCEL_ID, "关闭", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        Composite container = (Composite)super.createDialogArea(parent);
        container.setLayout(new GridLayout(6, false));

        table = new Table(container, SWT.BORDER | SWT.V_SCROLL | SWT.H_SCROLL);
        table.setLayout(new GridLayout(1, false));
        DropTarget dropTarget = new DropTarget(table, DND.DROP_NONE); // dropCom为你的
        Transfer[] transfer = new Transfer[] {FileTransfer.getInstance()};
        dropTarget.setTransfer(transfer);
        GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 6, 10);
        gd_table.widthHint = 479;
        table.setLayoutData(gd_table);
        fileTableMode = new FileTableMode();
        table.setModel(fileTableMode);

        return container;
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(700, 400);
    }
    
	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

    @SuppressWarnings("unchecked")
    private void importAll() {
        final List<FileItem> fileItems = fileTableMode.getItems();
        List<CostPrice> costPriceList = CostPriceDao.getCostPrices();
        final Map<String, CostPrice> priceMap = new HashMap<>();
        for (CostPrice costPrice : costPriceList) {
            if (!priceMap.containsKey(costPrice.getDevtype())) {
                priceMap.put(costPrice.getDevtype(), costPrice);
            }
        }
        ProgressManager.execute(new IRunnableWithProgress() {
            @Override
            public void run(final IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
                monitor.beginTask("正在执行导入操作...", fileItems.size() * 10 + 20);
                List<DevPrice> priceList = new ArrayList<DevPrice>();
                List<String> bidNos = new ArrayList<>();
                for (final FileItem fileItem : fileItems) {
                    String templatePath = fileItem.getAbsFileName();
                    try {
                        OPCPackage xlsxPackage = OPCPackage.open(templatePath, PackageAccess.READ);
                        ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
                        XSSFReader xssfReader = new XSSFReader(xlsxPackage);
                        StylesTable styles = xssfReader.getStylesTable();
                        XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator)xssfReader.getSheetsData();
                        while (iter.hasNext()) {
                            InputStream stream = iter.next();
                            String bidNo = FieldUtils.getFileId(templatePath);
                            bidNos.add(bidNo);
                            DevPriceHandler devPriceHandler = new DevPriceHandler(bidNo, priceMap);
                            Xls2007Parser.processSheet(styles, strings, devPriceHandler, stream);
                            priceList.addAll(devPriceHandler.getDevPriceList());
                            // }
                            stream.close();
                        }
                        xlsxPackage.close();
                    } catch (Throwable e) {
                        SCTLogger.error(e.getMessage());
                    }

                    monitor.worked(10);
                    Display.getDefault().asyncExec(new Runnable() {
                        @Override
                        public void run() {
                            fileItem.setStatus("解析数据完成");
                            table.redraw();
                        }
                    });
                }
                DevPriceDao.truncateDevPrice(bidNos);
                DevPriceDao.saveDevPriceInfo(priceList);
                new PackageImporter().updatePkgProduct(bidNos , rowId);
                PkgErrInfoDao.savePkgErrInfo();
                
    			RealPriceResolver2 real = new RealPriceResolver2();
    			real.updateOrgPrices();
    			real.updateRealPrices();
    			
                monitor.worked(20);
                monitor.done();
            }
        });
    }

}
