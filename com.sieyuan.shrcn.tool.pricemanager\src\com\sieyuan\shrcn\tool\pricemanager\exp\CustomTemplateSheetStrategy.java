package com.sieyuan.shrcn.tool.pricemanager.exp;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;

/**
 * 设置Sheet名称
 * <AUTHOR>
 *
 */
public class CustomTemplateSheetStrategy implements SheetWriteHandler {

	private Integer sheetNo;
	private String sheetName;

	public CustomTemplateSheetStrategy(Integer sheetNo, String sheetName) {
		this.sheetNo = sheetNo;
		this.sheetName = sheetName;

	}

	@Override
	public void afterSheetCreate(WriteWorkbookHolder arg0, WriteSheetHolder arg1) {
		// afterSheetCreate
	}

	@Override
	public void beforeSheetCreate(WriteWorkbookHolder arg0, WriteSheetHolder arg1) {
		if (sheetName == null) {
			return;
		}
		if (sheetNo == null) {
			sheetNo = 0;
		}
		arg0.getCachedWorkbook().setSheetName(sheetNo, sheetName);

	}

}
