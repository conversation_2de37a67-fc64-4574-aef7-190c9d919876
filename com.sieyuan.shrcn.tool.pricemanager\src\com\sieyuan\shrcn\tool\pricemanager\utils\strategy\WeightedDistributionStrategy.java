package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.shrcn.found.common.util.StringUtil;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.utils.CalcUtil;

/**
 * 加权分配策略
 * 根据内购外购权重分配价格到产品
 */
public class WeightedDistributionStrategy implements PriceDistributionStrategy {

    @Override
    public void distributePrices(PriceCalculationResult result, Map<Integer, List<PkgProduct>> productMap) {
        for (Map.Entry<Integer, List<PkgProduct>> entry : productMap.entrySet()) {
            Integer pkgId = entry.getKey();
            List<PkgProduct> products = entry.getValue();
            
            if (products == null || products.isEmpty()) {
                continue;
            }
            
            distributePricesForPackage(pkgId, products, result);
        }
    }
    
    /**
     * 为单个包分配价格
     */
    private void distributePricesForPackage(Integer pkgId, List<PkgProduct> products, PriceCalculationResult result) {
        // 过滤有效产品
        List<PkgProduct> validProducts = filterValidProducts(products);
        
        if (validProducts.isEmpty()) {
            return;
        }
        
        // 计算权重成本
        BigDecimal totalWeightCost = calculateTotalWeightCost(validProducts);
        
        if (totalWeightCost.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        
        // 分配价格
        distributePricesToProducts(validProducts, totalWeightCost, result);
    }
    
    /**
     * 过滤有效产品
     */
    private List<PkgProduct> filterValidProducts(List<PkgProduct> products) {
        List<PkgProduct> validProducts = new ArrayList<>();
        
        for (PkgProduct product : products) {
            if (isValidProduct(product)) {
                validProducts.add(product);
            }
        }
        
        return validProducts;
    }
    
    /**
     * 判断产品是否有效
     */
    private boolean isValidProduct(PkgProduct product) {
        return !StringUtil.isEmpty(product.getName()) &&
               !StringUtil.isEmpty(product.getOcunnt()) &&
               "是".equals(product.getQuote()) &&
               NumberUtils.isNumber(product.getOcunnt()) &&
               !"0".equals(product.getOcunnt()) &&
               !StringUtil.isEmpty(product.getCostprice());
    }
    
    /**
     * 计算总权重成本
     */
    private BigDecimal calculateTotalWeightCost(List<PkgProduct> products) {
        BigDecimal totalWeightCost = BigDecimal.ZERO;
        
        for (PkgProduct product : products) {
            BigDecimal costPrice = new BigDecimal(product.getCostprice());
            BigDecimal count = new BigDecimal(product.getOcunnt());
            BigDecimal weight = getProductWeight(product);
            
            BigDecimal weightCost = costPrice.multiply(count).multiply(weight);
            totalWeightCost = totalWeightCost.add(weightCost);
        }
        
        return totalWeightCost;
    }
    
    /**
     * 获取产品权重
     */
    private BigDecimal getProductWeight(PkgProduct product) {
        if (product.getLnType() == 1) {
            return new BigDecimal("2"); // 内购权重
        } else {
            return new BigDecimal("1"); // 外购权重
        }
    }
    
    /**
     * 分配价格到产品
     */
    private void distributePricesToProducts(List<PkgProduct> products, BigDecimal totalWeightCost, PriceCalculationResult result) {
        for (PkgProduct product : products) {
            BigDecimal costPrice = new BigDecimal(product.getCostprice());
            BigDecimal count = new BigDecimal(product.getOcunnt());
            BigDecimal weight = getProductWeight(product);
            
            // 计算权重成本
            BigDecimal weightCost = costPrice.multiply(count).multiply(weight);
            
            // 计算价格比例
            BigDecimal priceRatio = weightCost.divide(totalWeightCost, 8, BigDecimal.ROUND_HALF_UP);
            
            // 计算单价
            BigDecimal unitPrice = priceRatio.divide(count, ToolConstants.pPrice, BigDecimal.ROUND_HALF_UP);
            BigDecimal totalPrice = unitPrice.multiply(count);
            
            // 设置产品价格
            product.setPrice(CalcUtil.getBigDecimalString(unitPrice));
            product.setTotalprice(CalcUtil.getBigDecimalString(totalPrice));
            product.setWeight(weight.toString());
        }
    }
} 