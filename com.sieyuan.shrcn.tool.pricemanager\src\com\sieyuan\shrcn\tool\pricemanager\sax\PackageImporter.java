/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.CostPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.DevPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.LimitPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgAdjustDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgErrInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.ProductDao;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.DevPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.LimitPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgErrInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.model.Product;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;

/**
 * @Description:物料解析
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-16 下午4:04:58
 */
public class PackageImporter {

    private List<String> allExcelAbsoluteFile = new ArrayList<>();
    private List<String> allFiles = new ArrayList<>();
    private List<CostPrice> costtPrices = new ArrayList<>();
    private Map<String, DevPrice> devMap;
    private String limitPath;

    private List<LimitPrice> limitPrices = new ArrayList<>();
    private String pkgFilePath;
    private List<PkgInfo> pkgs = new ArrayList<>();
    private String pricePath;
    private List<Product> productlist = new ArrayList<>();

    private String[] selectNames;

    public PackageImporter() {
        super();
    }

    public PackageImporter(String pkgFilePath, List<String> allFiles, List<String> allExcelAbsoluteFile,
        String limitPath, String pricePath, String[] selectNames) {
        this.pkgFilePath = pkgFilePath;
        this.allFiles = allFiles;
        this.limitPath = limitPath;
        this.pricePath = pricePath;
        this.selectNames = selectNames;
        this.allExcelAbsoluteFile = allExcelAbsoluteFile;
    }

    public void execute(IProgressMonitor monitor) {
        initBase(monitor);
    }

    private void initBase(IProgressMonitor monitor) {
        BaseInfoParser parser =
            new BaseInfoParser(pkgFilePath, limitPath, pricePath, allFiles, allExcelAbsoluteFile, selectNames, monitor);
        parser.execute();
        pkgs = parser.getPkgs();
        limitPrices = parser.getLimitPrices();
        costtPrices = parser.getCostPrices();
        productlist = parser.getProductlist();

        monitor.setTaskName("第六步：保存处理数据");
        LimitPriceDao.saveLimitPricess(limitPrices);
        CostPriceDao.saveCostPricess(costtPrices);
        ProductDao.saveProductlist(productlist);
        PkgInfoDao.savePkgs(pkgs);
        PkgProductDao.savePkgProductlist();

        // 去掉重复
        Set<DevPrice> devPriceLsit = new HashSet<>();
        DevPrice devPrice;
        for (Product product : productlist) {
            devPrice = new DevPrice();
            devPrice.setOrderid(product.getOrderid());
            devPrice.setNumber(product.getNumber());
            devPrice.setName(product.getName());
            devPrice.setBidno(product.getBidno());
            devPrice.setDevname(product.getDevname());
            devPrice.setDevtype(product.getDevtype());
            devPrice.setUnit(product.getUnit());
            devPrice.setCount(product.getCount());
            devPrice.setOdevtype(product.getOdevtype());
            devPrice.setOcunnt(product.getOcunnt());
            devPrice.setSupply(product.getSupply());
            devPrice.setArea(product.getArea());
            devPrice.setQuote(product.getQuote());
            devPrice.setSearchType("");
            devPrice.setLnType("");
            devPrice.setCostprice("");
            devPrice.setPrice("");
            devPrice.setFile(product.getFile());
            devPriceLsit.add(devPrice);
        }
        Map<String, CostPrice> priceMap = new HashMap<>();
        for (CostPrice costPrice : costtPrices) {
         	String type = costPrice.getDevtype().replaceAll("：", ":").replaceAll("（", "(").replaceAll("）", ")").replaceAll("-", "-").trim();
         	if (!priceMap.containsKey(type)) {
                priceMap.put(type, costPrice);
            }
        }
        // 更新价格
        devMap = new HashMap<>();
		for (DevPrice devPriceTmp : devPriceLsit) {
			updatePriceType(priceMap, devPriceTmp);
		}
        DevPriceDao.saveDevPriceInfo(devPriceLsit);
        // 更新产品价格
        updatePkgProduct(null, null);
        monitor.worked(5);
        // 检查写入错误信息

        monitor.setTaskName("第七步：记录错误信息");
        PkgErrInfoDao.savePkgErrInfo();
        recordErrInfo();
        
        // 是否为相同的ID，相同的ID更新字段
        updateSameId();
        
        saveAdjustData();
    }

	public void updatePriceType(Map<String, CostPrice> priceMap, DevPrice devPriceTmp) {
		String devType = devPriceTmp.getOdevtype();
		if (StringUtils.isEmpty(devType) || devType.contains(ToolConstants.SPITSIGN)) {
			devPriceTmp.setQuote("否");
		} else {
			devType = devType.replaceAll("：", ":").replaceAll("（", "(").replaceAll("）", ")").replaceAll("-", "-").trim();

			if (devType.contains(":")) {
				devType = StringUtils.trim(devType.split(":")[0]);
			}
			Set<String> priceKeys = priceMap.keySet(); // 取出所有的key值
			List<String> keys = new ArrayList<String>();
			for (String key : priceKeys) {
//				if (key.contains(devType)) {
//					keys.add(key);
//				}
				if (key.equals(devType)) {
					keys.clear();
					keys.add(key);
				}
			}

			// 如果有多个取最满足的一个
			if (keys != null && keys.size() > 0) {
				String priceType = keys.get(0);
				if (StringUtils.isEmpty(priceType)) {
					devPriceTmp.setQuote("否");
				} else {
					if (StringUtils.isEmpty(priceMap.get(priceType).getPrice())) {
						devPriceTmp.setQuote("否");
						devPriceTmp.setLnType("");
						devPriceTmp.setPrice("");
						devPriceTmp.setSearchType("");
						devPriceTmp.setCostprice("");
						devPriceTmp.setSupply("");
					} else {
						devPriceTmp.setQuote("是");
						devPriceTmp.setLnType(String.valueOf(priceMap.get(priceType).getType()));
						devPriceTmp.setPrice(priceMap.get(priceType).getPrice());
						devPriceTmp.setSearchType(priceMap.get(priceType).getDevtype());
						devPriceTmp.setCostprice(priceMap.get(priceType).getCostPrice());
						devPriceTmp.setSupply(priceMap.get(priceType).getSupply());
					}
				}
			}
		}
	}


	/**
	 * 查找最合适的价格
	 * 
	 * @param keys 主键
	 * @param devPriceTmp 
	 * @param priceMap 
	 * @return
	 */
	@SuppressWarnings("unused")
	private String getAadaptType(List<String> keys, DevPrice devPriceTmp, Map<String, CostPrice> priceMap) {
		String file = devPriceTmp.getFile();
		if (keys.size() == 1) {
			return keys.get(0);
		}

		// 按照常规智能
		List<String> intelligenceKeys = new ArrayList<>();
		String intelligence = "";
		if (StringUtils.isEmpty(file)) {
			return "";
		}
		if (file.contains(ToolConstants.INTELLIGENCE)) {
			intelligence = ToolConstants.INTELLIGENCE;
			for (String key : keys) {
				if (key.contains(intelligence)) {
					intelligenceKeys.add(key);
				}
			}
		} else {
			intelligence = ToolConstants.INTELLIGENCE;
			for (String key : keys) {
				if (!key.contains(intelligence)) {
					intelligenceKeys.add(key);
				}
			}
		}
		if (intelligenceKeys.size() == 0) {
			intelligenceKeys = keys;
		}

		if (intelligenceKeys.size() == 1) {
			return intelligenceKeys.get(0);
		}

		// 按照电压等级
		String voltage = getVoltageGradeByProjectName(file);
		List<String> voltageKeys = new ArrayList<>();
		if (StringUtils.isEmpty(voltage)) {
			voltageKeys = intelligenceKeys;
			;
		} else {
			for (String key : intelligenceKeys) {
				if (key.contains(voltage)) {
					voltageKeys.add(key);
				}
			}
		}

		if (voltageKeys.size() == 0) {
			return intelligenceKeys.get(0);
		}
		return voltageKeys.get(0);
	}
	
    public String getVoltageGradeByProjectName(String projectName) {
		if (StringUtils.isEmpty(projectName)) {
			return "";
		}
        // 正则表达式，用于匹配非数字串，+号用于匹配出多个非数字串
        String regEx = "([0-9]{1,3})(KV|Kv|kV|kv|千伏)";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(projectName);
        // 用定义好的正则表达式拆分字符串，把字符串中的数字留出来
        while (matcher.find()) {
            String voltage = matcher.group();
            return voltage.substring(0, voltage.length() - 2);
        }
        return "";
    }

	/**
	 * 更新相同ID的字段信息
	 * 
	 * @param pkgsAll
	 */
	private void updateSameId() {
		List<PkgInfo> pkgsAll = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
		Map<String, Set<String>> map = new HashMap<>();
		for (PkgInfo pkgInfo : pkgsAll) {
			String bidNo = pkgInfo.getBidNo();
			Set<String> pkgNames = new HashSet<>();
			if (map.containsKey(bidNo)) {
				pkgNames = map.get(bidNo);
			}
			pkgNames.add(pkgInfo.getName());
			map.put(bidNo, pkgNames);
		}
		
		for (PkgInfo pkgInfo : pkgsAll) {
			String isSameId = "1";
			HashSet<String> mapValue = (HashSet<String>) map.get(pkgInfo.getBidNo());
			if (mapValue.size() > 1) {
				isSameId = "2";
			}
			pkgInfo.setIsSameId(isSameId);
		}
		PkgInfoDao.updatePkgInfoSameId(pkgsAll);
	}

	/**
     * 日志信息记录
     */
    private void recordErrInfo() {
        try {
            List<PkgErrInfo> pkgErrInfoList = PkgErrInfoDao.getPkgErrInfo();
            // 创建输出文件 result.txt
            File file = new File(DirManager.getPriceLogRecordFile());
            OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(file, true));

            for (PkgErrInfo pkgErrInfo : pkgErrInfoList) {
                writer.write(pkgErrInfo.getId() + " " + pkgErrInfo.getName() + " 错误类型" + pkgErrInfo.getErrtype() + " "
                    + pkgErrInfo.getErrdesc() + " " + pkgErrInfo.getProjectName() + " " + pkgErrInfo.getProduct() + "_"
                    + pkgErrInfo.getCount() + "_" + pkgErrInfo.getBidno() + "\n");

                ConsoleManager.getInstance().append(
                    pkgErrInfo.getId() + " " + pkgErrInfo.getName() + " 错误类型" + pkgErrInfo.getErrtype() + " "
                        + pkgErrInfo.getErrdesc() + " " + pkgErrInfo.getProjectName() + " " + pkgErrInfo.getProduct()
                        + "_" + pkgErrInfo.getCount() + "_" + pkgErrInfo.getBidno());
            }
            writer.flush();
            writer.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存调价信息
     */
	private void saveAdjustData() {
		Map<String, PkgAdjust> map = PkgInfoDao.getPkgAdjust();
		Map<String, Boolean> onlyIdMap = FieldUtils.getBidCountMap();
		
		List<PkgAdjust> newPkgAdjusts2 = new ArrayList<>();
		for (String pkgName : selectNames) {
			PkgAdjust old = map.get(pkgName);
			PkgAdjust pkgAdjust = new PkgAdjust();
			pkgAdjust.setPkgname(pkgName);
			pkgAdjust.setTargetprice("");
			pkgAdjust.setRate("");
			pkgAdjust.setRate2("");
			pkgAdjust.setRor("1");
			pkgAdjust.setRor2("1");
			pkgAdjust.setRealPrice("");
			pkgAdjust.setResult("");
			pkgAdjust.setSeq(0);
			if (onlyIdMap.containsKey(pkgName)) {
				pkgAdjust.setHasOnlyId(true);
				pkgAdjust.setIsPriority(false);
			} else {
				pkgAdjust.setHasOnlyId(false);
				pkgAdjust.setIsPriority(true);
			}
			pkgAdjust.setLimitprice(old.getLimitprice());
			newPkgAdjusts2.add(pkgAdjust);
		}
		PkgAdjustDao.saveNewPkgAdjusts(newPkgAdjusts2);

	}

	/**
     * 更新价格按照ID
     * @param bidNos
     */
    public void updatePkgProduct(List<String> bidNos, Integer rowId) {
        UIPreferences perference = UIPreferences.newInstance();
        String IN_TAX = ".inTax";
        String inWight = perference.getInfo(EnvirmentSettingDialog.class.getName() + IN_TAX);
        if (StringUtils.isEmpty(inWight)) {
            inWight = "2";
        }
        String outweight = "1";

        devMap = new HashMap<>();
        Map<String, DevPrice> odevMap = new HashMap<>();
        List<DevPrice> devPriceLsit = DevPriceDao.getAllProductPrices();
        for (DevPrice devPriceTmp : devPriceLsit) {

            odevMap.put(devPriceTmp.getBidno() + "_" + StringUtils.trimToEmpty(devPriceTmp.getNumber()) + "_"
                + devPriceTmp.getDevname() + "_" + devPriceTmp.getOdevtype(), devPriceTmp);

            devMap.put(devPriceTmp.getBidno() + "_" + StringUtils.trimToEmpty(devPriceTmp.getNumber()) + "_"
                + devPriceTmp.getDevname(), devPriceTmp);
        }
        // 查询对应的价格进行更新
        List<PkgProduct> pkgProductList = PkgProductDao.getPkgProductByParentid(null);
        List<PkgProduct> updatePkgProductList = new ArrayList<>();
		for (PkgProduct pkgProduct : pkgProductList) {
			if (bidNos == null || (bidNos != null && bidNos.contains(pkgProduct.getBidNo()))) {
				// 单个计算表的导入
				if (rowId != null && !pkgProduct.getRowId().equals(rowId)) {
					continue;
				}
				pkgProduct.setLnType(0);
				pkgProduct.setSearchdevtype("");
				pkgProduct.setCostprice("");
				pkgProduct.setPrice("");
				pkgProduct.setWeight("");
				if ((!pkgProduct.getOcunnt().trim().equals("0")) && (!pkgProduct.getOcunnt().trim().equals(""))) {
					DevPrice devPrice = odevMap.get(pkgProduct.getBidNo() + "_" + StringUtils.trimToEmpty(pkgProduct.getNumber()) + "_" + pkgProduct.getDevname() + "_" + pkgProduct.getOdevtype());

					if (devPrice == null) {
						devPrice = devMap.get(pkgProduct.getBidNo() + "_" + StringUtils.trimToEmpty(pkgProduct.getNumber()) + "_" + pkgProduct.getDevname());
					}

					if (devPrice != null) {
						if (StringUtil.isEmpty(devPrice.getLnType())) {
							pkgProduct.setLnType(0);
						} else {
							if (!StringUtils.isNumeric(devPrice.getLnType())) {
								pkgProduct.setLnType(0);
							} else {
								pkgProduct.setLnType(Integer.valueOf(devPrice.getLnType()));
								if (devPrice.getLnType().equals("1") && devPrice.getQuote().equals("是")) {
									pkgProduct.setWeight(inWight);
								}
								if (devPrice.getLnType().equals("2") && devPrice.getQuote().equals("是")) {
									pkgProduct.setWeight(outweight);
								}
							}
						}
						pkgProduct.setQuote(devPrice.getQuote());
						pkgProduct.setSearchdevtype(devPrice.getSearchType());
						if (pkgProduct.getQuote().equals("是")) {
							pkgProduct.setPrice(devPrice.getPrice());
							pkgProduct.setCostprice(devPrice.getCostprice());
							pkgProduct.setSupply(devPrice.getSupply());
							pkgProduct.setArea(devPrice.getArea());
						} else {
							pkgProduct.setPrice("");
							pkgProduct.setCostprice("");
						}
						updatePkgProductList.add(pkgProduct);
					}
				}
			}
		}
        PkgProductDao.updatePkgProductPrice(updatePkgProductList);
    }

}
