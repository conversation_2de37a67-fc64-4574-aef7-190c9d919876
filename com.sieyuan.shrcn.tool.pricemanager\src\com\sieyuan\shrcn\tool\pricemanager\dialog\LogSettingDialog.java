/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.dialog.MessageDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;

/**
 * @Description:日志设置
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 
 */
public class LogSettingDialog extends WrappedDialog {

    private Text diretoryRoot;
    private String LOGDIR = ".logDir";

    private UIPreferences perference = UIPreferences.newInstance();

    public LogSettingDialog(Shell parentShell) {
        super(parentShell);
    }

    @Override
    protected void buttonPressed(int buttonId) {
        if (buttonId == OK) {
            String infopath = getClass().getName();
            String logdir = diretoryRoot.getText();
            String msg = checkInput();
            if (msg != null) {
                DialogHelper.showWarning(msg);
                return;
            }
            perference.setInfo(infopath + LOGDIR, logdir);
            MessageDialog.openInformation(this.getShell(), "日志设置", "日志路径设置成功");
        }
        super.buttonPressed(buttonId);
    }

    private String checkInput() {
        return null;
    }

    /**
     * 配置对话框.
     */
    @Override
    protected void configureShell(Shell newShell) {
        super.configureShell(newShell);
        newShell.setText("日志设置");
    }

    /**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
        // Button btn = createButton(parent, IDialogConstants.OK_ID, "确定", true);
        // btn.setEnabled(false);
        createButton(parent, IDialogConstants.CANCEL_ID, "关闭", false);
    }

    @Override
    protected Control createDialogArea(Composite parent) {
        Composite container = (Composite)super.createDialogArea(parent);
        container.setLayout(new GridLayout(3, false));
        diretoryRoot = SwtUtil.createFileSelector(container, "日志输出路径（Txt）：", "*.txt");
        init();
        return container;
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(600, 200);
    }

    private void init() {
        String infopath = getClass().getName();
        String diretoryPath = perference.getInfo(infopath + LOGDIR);
        if (diretoryPath.equals("")) {
            diretoryPath = DirManager.getPriceLogRecordFile();
        }
        diretoryRoot.setText(diretoryPath);
    }

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
