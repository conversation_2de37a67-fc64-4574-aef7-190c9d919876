/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;

import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;

/**
* 
* <AUTHOR>
* @version 1.0, 2017-12-16
*/
public class CalcUtil {
    public static BigDecimal divide(String a, String b) {
        return getBigDecimal(a).divide(getBigDecimal(b), ToolConstants.pPrice, BigDecimal.ROUND_HALF_DOWN);
    }

    public static BigDecimal divideRate(String a, String b) {
        return getBigDecimal(a).divide(getBigDecimal(b), ToolConstants.pLimit, BigDecimal.ROUND_HALF_DOWN);
    }

    public static BigDecimal getAverageValue(BigDecimal sum, int num) {
        return sum.divide(new BigDecimal(num), ToolConstants.pPrice, BigDecimal.ROUND_HALF_DOWN);
    }

    public static BigDecimal getBigDecimal() {
        return getBigDecimal(ToolConstants.precision);
    }

    public static BigDecimal getBigDecimal(BigDecimal value) {
        BigDecimal setScale = value.setScale(6, BigDecimal.ROUND_HALF_DOWN);
        return setScale;
    }

    public static BigDecimal getBigDecimal2(BigDecimal value) {
        BigDecimal setScale = value.setScale(10, BigDecimal.ROUND_HALF_DOWN);
        return setScale;
    }

    public static BigDecimal getBigDecimal(String value) {
        BigDecimal d =
            (value == null) ? getBigDecimal() : new BigDecimal(value, new MathContext(30, RoundingMode.HALF_UP));
        return d.setScale(ToolConstants.pValue, BigDecimal.ROUND_HALF_DOWN);
    }

    public static BigDecimal getBigDecimalRate(String value) {
        BigDecimal d =
            (value == null) ? getBigDecimal() : new BigDecimal(value, new MathContext(4, RoundingMode.HALF_UP));
        return d.setScale(ToolConstants.pRate, BigDecimal.ROUND_HALF_UP);
    }

    public static String getBigDecimalString(BigDecimal value) {
        BigDecimal setScale = value.setScale(6, BigDecimal.ROUND_HALF_DOWN);
        return String.valueOf(setScale);
    }

    /**
     * 四舍五入
     * @param value
     * @return
     */
    public static String getBigDecimalStringDown(BigDecimal value) {
        BigDecimal setScale = value.setScale(6, BigDecimal.ROUND_HALF_UP);
        return String.valueOf(setScale);
    }
    
    /**
     * 四舍六入五
     * @param value
     * @return
     */
    public static String getBigDecimalStringHalfEven(BigDecimal value) {
        BigDecimal setScale = value.setScale(6, BigDecimal.ROUND_HALF_EVEN);
        return String.valueOf(setScale);
    }

    public static BigDecimal getMulti(BigDecimal v, int num) {
        return v.multiply(new BigDecimal(num));
    }

    public static BigDecimal getPercentValue(BigDecimal target, BigDecimal limit) {
        return target.divide(limit, ToolConstants.pPrice, BigDecimal.ROUND_HALF_DOWN);
    }

    public static BigDecimal getPriceMax(BigDecimal avg, float dRate) {
        BigDecimal d = avg.multiply(new BigDecimal(dRate));
        return avg.add(d).setScale(ToolConstants.pPrice, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal getPriceMin(BigDecimal avg, float dRate) {
        BigDecimal d = avg.multiply(new BigDecimal(dRate));
        return avg.subtract(d).setScale(ToolConstants.pPrice, BigDecimal.ROUND_HALF_UP);
    }
    

    public static String getPriceMulti(String avg,  String dRate) {
    	if(StringUtils.isEmpty(avg)){
    		return "";
    	}
        BigDecimal value = new BigDecimal(avg).multiply(new BigDecimal(dRate));
        return getBigDecimalString(value);
    }

    public static BigDecimal getTaxRate() {
        BigDecimal d = getBigDecimal("1." + UIPreferencesUtil.getTaxRate());
        return d.setScale(ToolConstants.pRate, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal getWithOutTaxBigDecimal(BigDecimal value) {
        BigDecimal setScale = value.setScale(4, BigDecimal.ROUND_HALF_DOWN);
        return setScale;
    }

    public static boolean isBigThan(BigDecimal s, BigDecimal t) {
        return s.subtract(t).doubleValue() > 0;
    }

    public static boolean isDouble(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("^[-\\+]?\\d+(\\.\\d*)?|\\.\\d+$");
        return pattern.matcher(str).matches();
    }

    public static boolean isEqual(BigDecimal b1, BigDecimal b2) {
        return (b1.doubleValue() - b2.doubleValue()) == 0;
    }

    /**
     * 检查同ID产品价格差是否在±10%以内
     * @param totals
     * @return
     */
    public static List<String> checkAvageValues(List<String> warnings, List<String> pkgNames, List<PkgInfo> pkgAllList,
        String idLimit) {

        Float limit = Float.valueOf(idLimit) / 100;
        // 按照包号形成MAP
        Map<String, List<PkgInfo>> pkgNameMaps = new HashMap<>();
        for (PkgInfo pkgInfo : pkgAllList) {
            String pname = pkgInfo.getName();
            List<PkgInfo> pkgList = pkgNameMaps.get(pname);
            if (pkgList == null) {
                pkgList = new ArrayList<>();
                pkgNameMaps.put(pname, pkgList);
            }
            pkgList.add(pkgInfo);
        }

        // 按照包统计
        Map<String, List<PkgInfo>> pkgsMap = new HashMap<>();
        
		for (String pkgname : pkgNames) {
			List<PkgInfo> pkgs = pkgNameMaps.get(pkgname);
			for (PkgInfo pkg : pkgs) {
				String bidNo = pkg.getBidNo();
				List<PkgInfo> pkgList = pkgsMap.get(bidNo);
				if (pkgList == null) {
					pkgList = new ArrayList<>();
					pkgsMap.put(bidNo, pkgList);
				}
				pkgList.add(pkg);
			}

		}
        // 计算平均值
        Map<String, BigDecimal> priceTaxAvgMap = new HashMap<>();
        Map<String, BigDecimal> pricePTaxAvgMap = new HashMap<>();
        
		for (String bidNo : pkgsMap.keySet()) {
			List<PkgInfo> pkgList = pkgsMap.get(bidNo);
			BigDecimal sum = new BigDecimal("0");
			BigDecimal psum = new BigDecimal("0");
			int count = 0;
			int pcount = 0;
			for (PkgInfo pkg : pkgList) {
				sum = sum.add(new BigDecimal(pkg.getTotalPrice()));
				count += Float.parseFloat(pkg.getCount());
				psum = psum.add(new BigDecimal(pkg.getPrice()));
				pcount++;
			}
			BigDecimal avg = CalcUtil.getAverageValue(sum, count);
			priceTaxAvgMap.put(bidNo, avg);

			BigDecimal pavg = CalcUtil.getAverageValue(psum, pcount);
			pricePTaxAvgMap.put(bidNo, pavg);
		}
        
        // 更新
        for (String name : pkgNames) {
            List<PkgInfo> pkgs = pkgNameMaps.get(name);

            int i = 1;
            for (PkgInfo pkg : pkgs) {
                String bidNo = pkg.getBidNo();
                BigDecimal avg = priceTaxAvgMap.get(bidNo);

                BigDecimal priceMax = CalcUtil.getPriceMax(avg, limit);
                BigDecimal priceMin = CalcUtil.getPriceMin(avg, limit);
                
                BigDecimal pavg = pricePTaxAvgMap.get(bidNo);
                BigDecimal perMax = CalcUtil.getPriceMax(pavg, limit);
                BigDecimal perMin = CalcUtil.getPriceMin(pavg, limit);

                String priceTax = pkg.getPrice();
                BigDecimal d = getBigDecimal(priceTax).subtract(avg);
                if (CalcUtil.isBigThan(new BigDecimal(0.000001), avg)) {
                    warnings.add(name + "-" + i + " " + bidNo + " 均价小于0。");
                } else {
                    BigDecimal rate = d.divide(avg, ToolConstants.pRate, BigDecimal.ROUND_HALF_UP);
                    pkg.setAvgRate(rate.toString());
                    pkg.setAvg(avg.toString());
                    BigDecimal priceLimit = divide(pkg.getTotalLimitPrice(), pkg.getCount());
                    boolean valid =
                        CalcUtil.isBigThan(priceMax, new BigDecimal(priceTax))
                            && CalcUtil.isBigThan(new BigDecimal(priceTax), priceMin);
                    if (valid) {
                        pkg.setIsValid("1");
                    } else {
                        pkg.setIsValid("2");
                    }
                    
                    pkg.setPavg(pavg.toString());
                    boolean pvalid =
                        CalcUtil.isBigThan(perMax, new BigDecimal(priceTax))
                            && CalcUtil.isBigThan(new BigDecimal(priceTax), perMin);
                    
					if (valid) {
						pkg.setIsValid("1");
					} else {
						pkg.setIsValid("2");
					}
					if (pvalid) {
						pkg.setPisValid("1");
					} else {
						pkg.setPisValid("2");
					}

					if (valid && pvalid) {
						pkg.setValid("1");
					} else {
						pkg.setValid("2");
					}

                    if (!valid) {
                        warnings.add(name + "-" + i + " " + bidNo + " 不在加权均价±" + idLimit + "%范围以内！当前价：" + priceTax
                            + "，均价：" + avg + " [" + priceMin + "，" + priceMax + "]，限价：" + priceLimit + "。");
                    }
                    if (!pvalid) {
                        warnings.add(name + "-" + i + " " + bidNo + " 不在算术均价±" + idLimit + "%范围以内！当前价：" + priceTax
                            + "，均价：" + pavg + " [" + perMin + "，" + perMax + "]，限价：" + priceLimit + "。");
                    }
                }
                i++;
            }
        }
        return warnings;
    }
    
    /**
     * 检查同物资名称产品价格差是否在±10%以内
     * @param totals
     * @return
     */
	public static List<String> checkProductNameAvageValues(List<String> warnings, List<String> pkgNames, List<PkgInfo> pkgAllList) {
		Float limit = Float.valueOf(10) / 100;
		// 按照包号形成MAP
		Map<String, List<PkgInfo>> pkgNameMaps = new HashMap<>();
		for (PkgInfo pkgInfo : pkgAllList) {
			String pname = pkgInfo.getName();
			List<PkgInfo> pkgList = pkgNameMaps.get(pname);
			if (pkgList == null) {
				pkgList = new ArrayList<>();
				pkgNameMaps.put(pname, pkgList);
			}
			pkgList.add(pkgInfo);
		}

		// 按照包统计
		Map<String, List<PkgInfo>> pkgsMap = new HashMap<>();

		for (String pkgname : pkgNames) {
			List<PkgInfo> pkgs = pkgNameMaps.get(pkgname);
			for (PkgInfo pkg : pkgs) {
				String product = pkg.getProductDesc();
				List<PkgInfo> pkgList = pkgsMap.get(product);
				if (pkgList == null) {
					pkgList = new ArrayList<>();
					pkgsMap.put(product, pkgList);
				}
				pkgList.add(pkg);
			}

		}
		// 计算平均值
		Map<String, BigDecimal> priceTaxAvgMap = new HashMap<>();
		Map<String, BigDecimal> pricePTaxAvgMap = new HashMap<>();

		for (String productName : pkgsMap.keySet()) {
			List<PkgInfo> pkgList = pkgsMap.get(productName);
			BigDecimal sum = new BigDecimal("0");
			BigDecimal psum = new BigDecimal("0");
			int count = 0;
			int pcount = 0;
			for (PkgInfo pkg : pkgList) {
				sum = sum.add(new BigDecimal(pkg.getTotalPrice()));
				count += Float.parseFloat(pkg.getCount());
				psum = psum.add(new BigDecimal(pkg.getPrice()));
				pcount++;
			}
			BigDecimal avg = CalcUtil.getAverageValue(sum, count);
			priceTaxAvgMap.put(productName, avg);

			BigDecimal pavg = CalcUtil.getAverageValue(psum, pcount);
			pricePTaxAvgMap.put(productName, pavg);
		}

		// 更新
		for (String name : pkgNames) {
			List<PkgInfo> pkgs = pkgNameMaps.get(name);

			int i = 1;
			for (PkgInfo pkg : pkgs) {
				String productName = pkg.getProductDesc();
				BigDecimal avg = priceTaxAvgMap.get(productName);

				BigDecimal priceMax = CalcUtil.getPriceMax(avg, limit);
				BigDecimal priceMin = CalcUtil.getPriceMin(avg, limit);

				BigDecimal pavg = pricePTaxAvgMap.get(productName);
				BigDecimal perMax = CalcUtil.getPriceMax(pavg, limit);
				BigDecimal perMin = CalcUtil.getPriceMin(pavg, limit);

				String priceTax = pkg.getPrice();
				BigDecimal d = getBigDecimal(priceTax).subtract(avg);
				if (CalcUtil.isBigThan(new BigDecimal(0.000001), avg)) {
					warnings.add(name + "-" + i + " " + productName + " 均价为0。");
				} else {
					BigDecimal rate = d.divide(avg, ToolConstants.pRate, BigDecimal.ROUND_HALF_UP);
					pkg.setWavgRate(rate.toString());
					pkg.setWavg(avg.toString());
					BigDecimal priceLimit = divide(pkg.getTotalLimitPrice(), pkg.getCount());
					boolean valid = CalcUtil.isBigThan(priceMax, new BigDecimal(priceTax)) && CalcUtil.isBigThan(new BigDecimal(priceTax), priceMin);
					if (valid) {
						pkg.setWisValid("1");
					} else {
						pkg.setWisValid("2");
					}

					pkg.setWpavg(pavg.toString());
					boolean pvalid = CalcUtil.isBigThan(perMax, new BigDecimal(priceTax)) && CalcUtil.isBigThan(new BigDecimal(priceTax), perMin);

					if (valid) {
						pkg.setWisValid("1");
					} else {
						pkg.setWisValid("2");
					}
					if (pvalid) {
						pkg.setWpisValid("1");
					} else {
						pkg.setWpisValid("2");
					}

					if (valid && pvalid) {
						pkg.setWvalid("1");
					} else {
						pkg.setWvalid("2");
					}

					if (!valid) {
						warnings.add(name + "-" + i + " " + productName + " 不在加权均价±" + UIPreferencesUtil.getIdLimit() + "%范围以内！当前价：" + priceTax + "，均价：" + avg + " [" + priceMin + "，" + priceMax + "]，限价：" + priceLimit + "。");
					}
					if (!pvalid) {
						warnings.add(name + "-" + i + " " + productName + " 不在算术均价±" + UIPreferencesUtil.getIdLimit() + "%%范围以内！当前价：" + priceTax + "，均价：" + pavg + " [" + perMin + "，" + perMax + "]，限价：" + priceLimit + "。");
					}
				}
				i++;
			}
		}
		return warnings;
	}

}
