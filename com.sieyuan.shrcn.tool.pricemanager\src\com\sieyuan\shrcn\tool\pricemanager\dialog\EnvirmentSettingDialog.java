/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.DateTime;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.dialog.MessageDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.CostPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.DevPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.LimitPriceDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgAdjustDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgErrInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.DevPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.LimitPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.sax.BaseInfoParser;
import com.sieyuan.shrcn.tool.pricemanager.sax.PackageImporter;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.utils.RealPriceResolver2;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;

/**
 * @Description:环境设置
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-17 上午11:11:12
 
 */
public class EnvirmentSettingDialog extends WrappedDialog {

	private UIPreferences perference = UIPreferences.newInstance();
	private Text outTax;
	private Text idLimit;
	private Text id; // 招标编号
	private Text taxrate;// 税率
	private Text txtLimit;// 限价表
	private Text txtPkg; // 包数据表
	private Text txtPrice;// 成本价
	private Text txtOriginRate;// 参考价系数
	
	private Text realPriceText;// 参考价表
	private DateTime calendarDropDown;

	private String CURDATE = ".curdate";
	private String ID = ".id";
	private String ID_LIMIT = "idLimit";
	private String IN_TAX = ".inTax";
	private String LIMIT = ".limitRoot";
	private String PKG = ".pkgRoot";
	private String PRICE = ".priceRoot";
	private String TAXRATE = ".taxrate";
	private String ORIGINRATE = ".originrate";
	private String REALPRICESET = ".realPriceSet";
	
	public EnvirmentSettingDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected void buttonPressed(int buttonId) {

		if (buttonId == OK) {
			String idText = id.getText();
			String taxrateText = taxrate.getText();
			String pkgPath = txtPkg.getText();
			String limitPath = txtLimit.getText();
			String pricePath = txtPrice.getText();
			String outTaxPath = outTax.getText();
			String idLimitPath = idLimit.getText();
			String orgRate = txtOriginRate.getText();
			String realPricePath = realPriceText.getText();

			int year = calendarDropDown.getYear(); // Calendar.DAY_OF_MONTH
			int month = calendarDropDown.getMonth(); // Calendar.MONTH
			int day = calendarDropDown.getDay();

			String msg = checkInput();
			if (msg != null) {
				DialogHelper.showWarning(msg);
				return;
			}
			String infopath = getClass().getName();
			perference.setInfo(infopath + ID, idText);

			perference.setInfo(infopath + CURDATE + "year", String.valueOf(year));
			perference.setInfo(infopath + CURDATE + "month", String.valueOf(month));
			perference.setInfo(infopath + CURDATE + "day", String.valueOf(day));

			perference.setInfo(infopath + TAXRATE, taxrateText);
			perference.setInfo(infopath + IN_TAX, outTaxPath);
			perference.setInfo(infopath + ORIGINRATE, orgRate);
			perference.setInfo(infopath + PKG, pkgPath);
			perference.setInfo(infopath + LIMIT, limitPath);
			perference.setInfo(infopath + PRICE, pricePath);
			perference.setInfo(infopath + ID_LIMIT, idLimitPath);
			perference.setInfo(infopath + REALPRICESET, realPricePath);
			
			updateRealPrice();
			
			MessageDialog.openInformation(this.getShell(), "环境设置", "环境设置成功");
		} else if (buttonId == 100) {
			String msg = checkInput();
			if (msg != null) {
				DialogHelper.showWarning(msg);
				return;
			}
			String limitPath = txtLimit.getText();
			BaseInfoParser parser = new BaseInfoParser("", limitPath);
			parser.parseLimit();
			List<LimitPrice> limitPrices = parser.getLimitPrices();
			LimitPriceDao.saveLimitPricess(limitPrices);
			PkgErrInfoDao.savePkgErrInfo();
			saveAdjustData();
			MessageDialog.openInformation(this.getShell(), "环境设置", "限价信息刷新成功");
		} else if (buttonId == 101) {
			String pricePath = txtPrice.getText();
			BaseInfoParser parser = new BaseInfoParser("", "", pricePath);
			parser.parsePrice();

			List<CostPrice> costPrices = parser.getCostPrices();
			CostPriceDao.saveCostPricess(costPrices);
			Map<String, CostPrice> priceMap = new HashMap<>();
			for (CostPrice costPrice : costPrices) {
				String type = costPrice.getDevtype().replaceAll("：", ":").replaceAll("（", "(").replaceAll("）", ")").replaceAll("-", "-").trim();
				if (!priceMap.containsKey(type)) {
					priceMap.put(type, costPrice);
				}
			}

			List<DevPrice> devPriceLsit = DevPriceDao.getAllProductPrices();

			for (DevPrice devPriceTmp : devPriceLsit) {
				new PackageImporter().updatePriceType(priceMap, devPriceTmp);
			}
			DevPriceDao.updateDevPriceInfo(devPriceLsit);

			new PackageImporter().updatePkgProduct(null, null);
			PkgErrInfoDao.savePkgErrInfo();
			MessageDialog.openInformation(this.getShell(), "环境设置", "价格库信息刷新成功");
			NavigatView.refreshTree();
		} else if (buttonId == IDialogConstants.BACK_ID) {
			updateRealPrice();
			MessageDialog.openInformation(this.getShell(), "环境设置", "参考价表刷新完成");
		}
		super.buttonPressed(buttonId);
	}

	private void updateRealPrice() {
		String prjName = GlobalData.getInstance().getProjectName();
		if (!StringUtils.isEmpty(prjName)) {
			RealPriceResolver2 real = new RealPriceResolver2();
			real.updateOrgPrices();
			real.updateRealPrices();
		}
	}

	/**
	 * 保存调价信息
	 */
	private void saveAdjustData() {
		Map<String, PkgAdjust> map = PkgInfoDao.getPkgAdjust();
		Map<String, Boolean> onlyIdMap = FieldUtils.getBidCountMap();
		
		List<PkgAdjust> newPkgAdjusts2 = new ArrayList<>();
		for (Map.Entry<String, PkgAdjust> entry : map.entrySet()) {
			PkgAdjust old = entry.getValue();
			PkgAdjust pkgAdjust = new PkgAdjust();
			pkgAdjust.setPkgname(entry.getKey());
			pkgAdjust.setTargetprice("");
			pkgAdjust.setRate("");
			pkgAdjust.setRor("");
			pkgAdjust.setRate2("");
			pkgAdjust.setRor2("");
			pkgAdjust.setRealPrice("");
			pkgAdjust.setResult("");
			pkgAdjust.setSeq(0);
			if (onlyIdMap.containsKey(entry.getKey())) {
				pkgAdjust.setHasOnlyId(true);
				pkgAdjust.setIsPriority(false);
			} else {
				pkgAdjust.setHasOnlyId(false);
				pkgAdjust.setIsPriority(true);
			}
			pkgAdjust.setLimitprice(old.getLimitprice());
			newPkgAdjusts2.add(pkgAdjust);
		}
		PkgAdjustDao.saveNewPkgAdjusts(newPkgAdjusts2);
	}
	


	private String checkInput() {
		String idText = id.getText();
		String taxrateText = taxrate.getText();
		String pkgPath = txtPkg.getText();
		String limitPath = txtLimit.getText();
		String pricePath = txtPrice.getText();
		String outTaxPath = outTax.getText();
		String orgTaxPath = txtOriginRate.getText();
		String idLimitPath = idLimit.getText();
		String realPricePath = realPriceText.getText();
		if (StringUtils.isEmpty(idText)) {
			return "招标编号不能为空！";
		}
		if (StringUtils.isEmpty(taxrateText)) {
			return "税率不能为空！";
		}
		if (StringUtils.isEmpty(outTaxPath)) {
			return "自产系数不能为空！";
		}
		if (StringUtils.isEmpty(idLimitPath)) {
			return "同一ID含税单价偏差不能为空！";
		}
		if (StringUtils.isEmpty(orgTaxPath)) {
			return "参考价系数不能为空！";
		}
		if (!new File(pkgPath).exists()) {
			return "物料清单信息表" + pkgPath + "不存在！";
		}
		if (!new File(limitPath).exists()) {
			return "限价信息表" + limitPath + "不存在！";
		}
		if (!new File(pricePath).exists()) {
			return "成本信息表" + pricePath + "不存在！";
		}
		if (!new File(realPricePath).exists()) {
			return "参考价格表" + realPricePath + "不存在！";
		}
		return null;
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("环境设置");
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, "确定", true);
		createButton(parent, 100, "刷新限价", false);
		createButton(parent, 101, "刷新价格库", false);
		createButton(parent, IDialogConstants.BACK_ID, "刷新参考价", false);
		createButton(parent, IDialogConstants.CANCEL_ID, "取消", false);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		container.setLayout(new GridLayout(3, false));

		GridData gd = new GridData(SWT.LEFT, SWT.LEFT, false, false, 2, 1);
		gd.widthHint = 300;

		SwtUtil.createLabel(container, "投标日期：", new GridData(SWT.LEFT, SWT.LEFT, false, false, 1, 1));
		calendarDropDown = new DateTime(container, SWT.DROP_DOWN | SWT.DATE);
		calendarDropDown.setLayoutData(gd);

		id = SwtUtil.createLabelText(container, "招标编号：", gd);
		outTax = SwtUtil.createLabelText(container, "自产系数：", gd);
		idLimit = SwtUtil.createLabelText(container, "同一ID含税单价偏差(%)：", gd);
		txtOriginRate = SwtUtil.createLabelText(container, "参考价系数：", gd);

		taxrate = SwtUtil.createLabelText(container, "税率：", gd);
		// taxrate.setEditable(false);

		txtPkg = SwtUtil.createFileSelector(container, "物料清单信息表(Excel)：", "*.xlsx");
		txtLimit = SwtUtil.createFileSelector(container, "限价信息表(Excel)：", "*.xlsx");
		txtPrice = SwtUtil.createFileSelector(container, "价格库信息表(Excel)：", "*.xlsx");
		realPriceText = SwtUtil.createFileSelector(container, "参考价格表(Excel)：", "*.xlsx");

		init();
		return container;
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(630, 420);
	}

	private void init() {
		String infopath = getClass().getName();

		String idText = perference.getInfo(infopath + ID);
		if (idText.equals("")) {
			idText = "0711-19OTL020";
		}
		id.setText(idText);

		String taxTxt = perference.getInfo(infopath + TAXRATE);
		if (taxTxt.equals("")) {
			taxTxt = String.valueOf(ToolConstants.rate);
		}
		taxrate.setText(taxTxt);

		String idLimitTxt = perference.getInfo(infopath + ID_LIMIT);
		if (idLimitTxt.equals("")) {
			idLimitTxt = ToolConstants.LIMIT;
		}
		idLimit.setText(idLimitTxt);

		String orgRateTxt = perference.getInfo(infopath + ORIGINRATE);
		if (orgRateTxt.equals("")) {
			orgRateTxt = ToolConstants.ORG_RATE;
		}
		txtOriginRate.setText(orgRateTxt);

		String year = perference.getInfo(infopath + CURDATE + "year");
		String month = perference.getInfo(infopath + CURDATE + "month");
		String day = perference.getInfo(infopath + CURDATE + "day");
		if (!year.equals("") && !month.equals("") && !day.equals("")) {
			calendarDropDown.setYear(Integer.valueOf(year));
			calendarDropDown.setMonth(Integer.valueOf(month));
			calendarDropDown.setDay(Integer.valueOf(day));
		}

		String pkgPath = perference.getInfo(infopath + PKG);
		if (pkgPath.equals("")) {
			pkgPath = DirManager.getToolPkgFile();
		}
		txtPkg.setText(pkgPath);

		String limitPath = perference.getInfo(infopath + LIMIT);
		if (limitPath.equals("")) {
			limitPath = DirManager.getToolLimitFile();
		}
		txtLimit.setText(limitPath);

		String out = perference.getInfo(infopath + IN_TAX);
		if (out.equals("")) {
			out = ToolConstants.OUT_TAX;
		}
		outTax.setText(out);

		String pricePath = perference.getInfo(infopath + PRICE);
		if (pricePath.equals("")) {
			pricePath = DirManager.getToolPriceFile();
		}
		txtPrice.setText(pricePath);

		String realPricePath = perference.getInfo(infopath + REALPRICESET);
		if (StringUtils.isEmpty(realPricePath)) {
			realPricePath = DirManager.getRealPriceFile();;
		}
		realPriceText.setText(realPricePath);

	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
