package com.sieyuan.shrcn.tool.pricemanager.data;

import org.eclipse.swt.widgets.TreeItem;

import com.sieyuan.shrcn.tool.pricemanager.dao.SqliteHelper;

/**
 * 
 * @Description:全局数据列表
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON>eyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:51:10
 
 */
public class GlobalData {

    /** 装置数据表数据源实例. */
    private static GlobalData instance = new GlobalData();

    public static GlobalData getInstance() {
        synchronized (GlobalData.class) {
            if (null == instance) {
                instance = new GlobalData();
            }
        }
        return instance;
    }

    public static void setInstance(GlobalData instance) {
        GlobalData.instance = instance;
    }

    private TreeItem currentTreeItem;
    private String projectName;
    private Boolean show = true;
    private SqliteHelper sqliteHelper;

    private Integer viewType = 1;

    public TreeItem getCurrentTreeItem() {
        return currentTreeItem;
    }

    public String getProjectName() {
        return projectName;
    }

    public Boolean getShow() {
        return show;
    }

    public SqliteHelper getSqliteHelper() {
        return sqliteHelper;
    }

    public Integer getViewType() {
        return viewType;
    }

    public void setCurrentTreeItem(TreeItem currentTreeItem) {
        this.currentTreeItem = currentTreeItem;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public void setShow(Boolean show) {
        this.show = show;
    }

    public void setSqliteHelper(SqliteHelper sqliteHelper) {
        this.sqliteHelper = sqliteHelper;
    }

    public void setViewType(Integer viewType) {
        this.viewType = viewType;
    }

}
