/*
 * @(#) TimeUtils.java
 * 
 * Copyright (c) 2008 - 2012 上海思源弘瑞电力自动化有限公司. All rights reserved. 基于 Eclipse E4 a next generation platform (e.g., the
 * CSS styling, dependency injection, Modeled UI) Rich Client Application 开发的平台工具软件.
 */
package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description:TODO
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午11:14:56
 */

public class TimeUtils {

    public static String time() {
        SimpleDateFormat format = new SimpleDateFormat("yy/MM/dd HH:mm:ss");
        String time = format.format(new Date(System.currentTimeMillis()));
        return time;
    }
    public static String timePre() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        String time = format.format(new Date(System.currentTimeMillis()));
        return time;
    }
}
