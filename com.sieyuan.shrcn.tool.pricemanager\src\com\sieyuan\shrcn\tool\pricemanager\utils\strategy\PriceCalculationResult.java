package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 价格计算结果类
 * 封装价格计算的所有结果数据
 */
public class PriceCalculationResult {
    
    private BigDecimal targetPrice;
    private final Map<String, BigDecimal> packagePrices;
    private final Map<String, BigDecimal> finalPrices;
    private BigDecimal totalCalculatedPrice;
    
    public PriceCalculationResult() {
        this.packagePrices = new HashMap<>();
        this.finalPrices = new HashMap<>();
        this.totalCalculatedPrice = BigDecimal.ZERO;
    }
    
    public BigDecimal getTargetPrice() {
        return targetPrice;
    }
    
    public void setTargetPrice(BigDecimal targetPrice) {
        this.targetPrice = targetPrice;
    }
    
    public Map<String, BigDecimal> getPackagePrices() {
        return packagePrices;
    }
    
    public Map<String, BigDecimal> getFinalPrices() {
        return finalPrices;
    }
    
    public BigDecimal getTotalCalculatedPrice() {
        return totalCalculatedPrice;
    }
    
    public void setTotalCalculatedPrice(BigDecimal totalCalculatedPrice) {
        this.totalCalculatedPrice = totalCalculatedPrice;
    }
    
    public void addPackagePrice(String bidNo, BigDecimal price) {
        packagePrices.put(bidNo, price);
        totalCalculatedPrice = totalCalculatedPrice.add(price);
    }
    
    public void updatePackagePrice(String bidNo, BigDecimal price) {
        BigDecimal oldPrice = packagePrices.getOrDefault(bidNo, BigDecimal.ZERO);
        packagePrices.put(bidNo, price);
        totalCalculatedPrice = totalCalculatedPrice.subtract(oldPrice).add(price);
    }
    
    public void addFinalPrice(String bidNo, BigDecimal price) {
        finalPrices.put(bidNo, price);
    }
    
    public BigDecimal getPackagePrice(String bidNo) {
        return packagePrices.getOrDefault(bidNo, BigDecimal.ZERO);
    }
    
    public BigDecimal getFinalPrice(String bidNo) {
        return finalPrices.getOrDefault(bidNo, BigDecimal.ZERO);
    }
} 