package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;

/**
 * @Description:环境设置界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-12 下午7:07:40
 */
public class EnvirmentSettingAction extends MenuAction {

    public EnvirmentSettingAction(String text) {
        super(text);
		setAccelerator(SWT.CTRL + 'E');
    }

    @Override
    public void run() {
        EnvirmentSettingDialog expDlg = new EnvirmentSettingDialog(Display.getDefault().getActiveShell());
        expDlg.open();
    }
}
