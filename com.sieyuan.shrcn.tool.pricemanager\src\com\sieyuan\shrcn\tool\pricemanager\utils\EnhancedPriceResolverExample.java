/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.util.List;

import org.eclipse.core.runtime.IProgressMonitor;

import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;

/**
 * 增强版价格解析器使用示例
 * 展示如何使用新的模块化价格管理算法
 * 
 * <AUTHOR> (mailto:<EMAIL>)
 * @version 2.0, 2024-01-01
 */
public class EnhancedPriceResolverExample {

    /**
     * 使用示例：基本价格调整
     */
    public static void basicPriceAdjustmentExample(List<PkgAdjust> adjustList, IProgressMonitor monitor) {
        // 创建增强版价格解析器
        EnhancedPriceResolver resolver = new EnhancedPriceResolver();
        
        try {
            // 执行价格调整
            resolver.adjustPrices(adjustList, monitor);
            
            // 更新计算结果
            resolver.updateCalResult();
            
            // 更新结果
            resolver.updateResult(adjustList);
            
            // 获取警告信息
            List<String> warnings = resolver.getWarnings();
            if (!warnings.isEmpty()) {
                System.out.println("发现以下警告：");
                for (String warning : warnings) {
                    System.out.println("- " + warning);
                }
            }
            
        } catch (Exception e) {
            System.err.println("价格调整过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 使用示例：计算独有ID比率
     */
    public static void onlyIdRateCalculationExample(List<PkgAdjust> adjustList) {
        EnhancedPriceResolver resolver = new EnhancedPriceResolver();
        
        try {
            // 计算独有ID比率
            resolver.calOnlyRate(adjustList);
            
            System.out.println("独有ID比率计算完成");
            
        } catch (Exception e) {
            System.err.println("独有ID比率计算过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 使用示例：完整的价格管理流程
     */
    public static void completePriceManagementExample(List<PkgAdjust> adjustList, IProgressMonitor monitor) {
        EnhancedPriceResolver resolver = new EnhancedPriceResolver();
        
        try {
            // 1. 计算独有ID比率
            System.out.println("步骤1: 计算独有ID比率...");
            resolver.calOnlyRate(adjustList);
            
            // 2. 执行价格调整
            System.out.println("步骤2: 执行价格调整...");
            resolver.adjustPrices(adjustList, monitor);
            
            // 3. 更新计算结果
            System.out.println("步骤3: 更新计算结果...");
            resolver.updateCalResult();
            
            // 4. 更新结果
            System.out.println("步骤4: 更新结果...");
            resolver.updateResult(adjustList);
            
            // 5. 检查警告
            System.out.println("步骤5: 检查警告信息...");
            List<String> warnings = resolver.getWarnings();
            if (!warnings.isEmpty()) {
                System.out.println("发现以下警告：");
                for (String warning : warnings) {
                    System.out.println("- " + warning);
                }
            } else {
                System.out.println("没有发现警告信息");
            }
            
            System.out.println("价格管理流程完成");
            
        } catch (Exception e) {
            System.err.println("价格管理流程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 