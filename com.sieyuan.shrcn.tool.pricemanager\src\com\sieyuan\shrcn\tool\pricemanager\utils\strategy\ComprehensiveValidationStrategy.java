package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.math.BigDecimal;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.shrcn.found.common.util.StringUtil;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.utils.CalcUtil;

/**
 * 综合验证策略
 * 提供全面的价格验证功能
 */
public class ComprehensiveValidationStrategy implements PriceValidationStrategy {

    @Override
    public void validatePackage(PkgInfo pkg, List<String> warnings) {
        // 验证价格是否为正数
        validatePositivePrice(pkg, warnings);
        
        // 验证是否超出限价
        validateLimitPrice(pkg, warnings);
        
        // 验证价格合理性
        validatePriceReasonableness(pkg, warnings);
    }
    
    /**
     * 验证价格是否为正数
     */
    private void validatePositivePrice(PkgInfo pkg, List<String> warnings) {
        try {
            if (!StringUtils.isEmpty(pkg.getPrice())) {
                BigDecimal price = new BigDecimal(pkg.getPrice());
                if (price.compareTo(BigDecimal.ZERO) <= 0) {
                    String msg = String.format("包 %s_%s_%s_%s 存在非正价格: %s", 
                        pkg.getName(), pkg.getProjectName(), pkg.getProduct(), pkg.getBidNo(), pkg.getPrice());
                    warnings.add(msg);
                }
            }
        } catch (NumberFormatException e) {
            String msg = String.format("包 %s_%s_%s_%s 价格格式错误: %s", 
                pkg.getName(), pkg.getProjectName(), pkg.getProduct(), pkg.getBidNo(), pkg.getPrice());
            warnings.add(msg);
        }
    }
    
    /**
     * 验证是否超出限价
     */
    private void validateLimitPrice(PkgInfo pkg, List<String> warnings) {
        if (!StringUtil.isEmpty(pkg.getTotalLimitPrice())) {
            try {
                BigDecimal limitPrice = new BigDecimal(pkg.getTotalLimitPrice());
                BigDecimal totalPrice = new BigDecimal(pkg.getTotalPrice());
                
                if (CalcUtil.isBigThan(totalPrice, limitPrice)) {
                    String msg = String.format("包 %s_%s_%s_%s 报价 %s 超出限价 %s", 
                        pkg.getName(), pkg.getProjectName(), pkg.getProduct(), pkg.getBidNo(), 
                        totalPrice, limitPrice);
                    warnings.add(msg);
                }
            } catch (NumberFormatException e) {
                String msg = String.format("包 %s_%s_%s_%s 限价格式错误: %s", 
                    pkg.getName(), pkg.getProjectName(), pkg.getProduct(), pkg.getBidNo(), pkg.getTotalLimitPrice());
                warnings.add(msg);
            }
        }
    }
    
    /**
     * 验证价格合理性
     */
    private void validatePriceReasonableness(PkgInfo pkg, List<String> warnings) {
        try {
            if (!StringUtils.isEmpty(pkg.getRealPrice()) && !StringUtils.isEmpty(pkg.getTotalPrice())) {
                BigDecimal realPrice = new BigDecimal(pkg.getRealPrice());
                BigDecimal totalPrice = new BigDecimal(pkg.getTotalPrice());
                
                // 检查价格是否偏离市场价过大（超过50%）
                BigDecimal deviation = totalPrice.subtract(realPrice).abs()
                    .divide(realPrice, 4, BigDecimal.ROUND_HALF_UP);
                
                if (deviation.compareTo(new BigDecimal("0.5")) > 0) {
                    String msg = String.format("包 %s_%s_%s_%s 价格偏离市场价过大: 偏离率 %.2f%%", 
                        pkg.getName(), pkg.getProjectName(), pkg.getProduct(), pkg.getBidNo(), 
                        deviation.multiply(new BigDecimal("100")));
                    warnings.add(msg);
                }
            }
        } catch (NumberFormatException e) {
            // 忽略格式错误，已在其他验证中处理
        }
    }
} 