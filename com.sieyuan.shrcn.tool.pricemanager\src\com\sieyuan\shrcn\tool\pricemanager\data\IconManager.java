package com.sieyuan.shrcn.tool.pricemanager.data;

import org.eclipse.swt.graphics.Image;

import com.shrcn.found.ui.util.ImgDescManager;

/**
 * @Description:图标管理器
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:51:43
 
 */
public class IconManager {

    public static final Image idItemImg = ImgDescManager.createImage("id_item.png");
    public static final Image nameItemImg = ImgDescManager.createImage("name_item.png");
    public static final Image oidItemImg = ImgDescManager.createImage("oid_item.png");
    public static final Image onameItemImg = ImgDescManager.createImage("oname_item.png");
    public static final Image price404 = ImgDescManager.createImage("pricemanager_404.gif");
    public static final Image priceWelcome = ImgDescManager.createImage("pricemanager_welcome.pngx");
    public static final Image productItemImg = ImgDescManager.createImage("product_item.png");
    public static final Image projectImg = ImgDescManager.createImage("price_item.png");
    public static final Image workItemImg = ImgDescManager.createImage("work_item.png");
}
