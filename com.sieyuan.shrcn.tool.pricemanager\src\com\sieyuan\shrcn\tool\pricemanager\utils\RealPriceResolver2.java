/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.file.excel.Xls2007Parser;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgAdjustDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.sax.RealPriceHandler;

/**
 * 新版本调价算法
 * 
 * <AUTHOR> (mailto:<EMAIL>)
 * @version 1.0, 2022-11-16
 */
public class RealPriceResolver2 {

	private UIPreferences perference = UIPreferences.newInstance();
	private String ORIGINRATE = ".originrate";
	private String REALPRICESET = ".realPriceSet";

	private List<PkgInfo> allPkgList;
	private List<PkgAdjust> adjustList;
	private Map<String, String> realtPrices;

	public RealPriceResolver2() {
		super();
		realtPrices = new HashMap<>();
	}

	public void updateOrgPrices() {

		this.allPkgList = new ArrayList<>();
		adjustList = PkgAdjustDao.getNewPkgAdjust();
		Map<String, PkgAdjust> adjustMap = new HashMap<>();
		for (PkgAdjust adj : adjustList) {
			adjustMap.put(adj.getPkgname(), adj);
		}

		// 获取所有PKG，按照包名组装为MAP
		Map<String, List<PkgInfo>> pkgMap = getPkgMap();
		parseRealPrice();
		for (Map.Entry<String, List<PkgInfo>> entry : pkgMap.entrySet()) {
			List<PkgInfo> pkgs = entry.getValue();
			for (PkgInfo pkg : pkgs) {
				String realPrice = realtPrices.get(pkg.getApplyId());
				if (StringUtils.isEmpty(realPrice)) {
					ConsoleManager.getInstance().append(pkg.getApplyId() + "参考价不能为空！");
					realPrice = "1";
				} else if (!NumberUtils.isNumber(realPrice)) {
					ConsoleManager.getInstance().append(pkg.getApplyId() + "参考价不合法！");
					realPrice = "1";
				}
				pkg.setOrgPrice(String.valueOf(realPrice));
				allPkgList.add(pkg);
			}
		}
		PkgInfoDao.updateRealPricePkgInfo(allPkgList);
	}

	public void parseRealPrice() {
		try {
			String realPricePath = perference.getInfo(EnvirmentSettingDialog.class.getName() + REALPRICESET);
			OPCPackage xlsxPackage = OPCPackage.open(realPricePath, PackageAccess.READ);
			ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
			XSSFReader xssfReader = new XSSFReader(xlsxPackage);
			StylesTable styles = xssfReader.getStylesTable();
			XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
			while (iter.hasNext()) {
				InputStream stream = iter.next();
				RealPriceHandler realPriceHandler = new RealPriceHandler();
				Xls2007Parser.processSheet(styles, strings, realPriceHandler, stream);
				realtPrices = realPriceHandler.getRealPrcies();
				stream.close();
			}
			xlsxPackage.close();
		} catch (Throwable e) {
			SCTLogger.error(e.getMessage());
		}
	}

	public void updateRealPrices() {

		if (GlobalData.getInstance().getSqliteHelper() == null) {
			return;
		}
		this.allPkgList = new ArrayList<>();
		String orgRate = perference.getInfo(EnvirmentSettingDialog.class.getName() + ORIGINRATE);
		if (StringUtils.isEmpty(orgRate)) {
			orgRate = "1.0";
		}

		adjustList = PkgAdjustDao.getNewPkgAdjust();
		Map<String, PkgAdjust> adjustMap = new HashMap<>();
		for (PkgAdjust adj : adjustList) {
			adjustMap.put(adj.getPkgname(), adj);
		}

		// 获取所有PKG，按照包名组装为MAP
		Map<String, List<PkgInfo>> pkgMap = getPkgMap();

		for (Map.Entry<String, List<PkgInfo>> entry : pkgMap.entrySet()) {

			PkgAdjust adjust2 = adjustMap.get(entry.getKey());
			BigDecimal allRealPrice = CalcUtil.getBigDecimal();// 市场价

			List<PkgInfo> pkgs = entry.getValue();
			for (PkgInfo pkg : pkgs) {
				if (StringUtils.isEmpty(pkg.getOrgPrice())) {
					continue;
				}
				BigDecimal orgPrice = new BigDecimal(pkg.getOrgPrice());
				BigDecimal realPrice = orgPrice.multiply(new BigDecimal(orgRate));
				pkg.setRealPrice(String.valueOf(realPrice));

				if (StringUtil.isEmpty(pkg.getTotalLimitPrice()) || pkg.getTotalLimitPrice().equals("0")) {
					allRealPrice = allRealPrice.add(realPrice);
				}
				allPkgList.add(pkg);
			}

			adjust2.setRealPrice(String.valueOf(allRealPrice));
		}

		PkgInfoDao.updateRealPricePkgInfo(allPkgList);
		PkgAdjustDao.updateRealPricePkgInfo(adjustList);
	}

	// 查询货物清单信息
	private Map<String, List<PkgInfo>> getPkgMap() {
		Map<String, List<PkgInfo>> pkgMaps = new HashMap<>();
		List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
		List<PkgInfo> plist = null;
		for (PkgInfo pkgInfo : pkgs) {
			plist = new ArrayList<PkgInfo>();
			if (pkgMaps.containsKey(pkgInfo.getName())) {
				plist = pkgMaps.get(pkgInfo.getName());
				if (plist == null) {
					plist = new ArrayList<PkgInfo>();
				}
			}
			plist.add(pkgInfo);
			pkgMaps.put(pkgInfo.getName(), plist);
		}
		return pkgMaps;
	}

}