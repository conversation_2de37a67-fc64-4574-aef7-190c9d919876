package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;

@SuppressWarnings("rawtypes")
public class PkgHandler extends SheetsHandler {

    private PkgInfo pkgInfo;

    // 所有的物料
    private List<PkgInfo> skgs;

    public PkgHandler() {
        skgs = new ArrayList<>();
    }

    @Override
	public void cell(String cellReference, String formattedValue,
			XSSFComment comment) {
		super.cell(cellReference, formattedValue, comment);

		if (currentRow > 0 && !isEmpty(formattedValue)) {
			if (currentCol == 0) {
				pkgInfo.setName(formattedValue);
			} else if (currentCol == 1) {
				pkgInfo.setProjectOwner(formattedValue);
			} else if (currentCol == 2) {
				pkgInfo.setProjectName(formattedValue);
			} else if (currentCol == 3) {
				pkgInfo.setProduct(formattedValue);
				if (formattedValue.contains(ToolConstants.INTELLIGENCE)) {
					pkgInfo.setIntelligence(ToolConstants.INTELLIGENCE);
				} else {
					pkgInfo.setIntelligence(ToolConstants.NOTINTELLIGENCE);
				}
				String[] array = formattedValue.split(",");
				if (array.length == 2) {
					pkgInfo.setVoltageGrade(array[1]);
				} else {
					String voltageGradeByProjectName = getVoltageGradeByProjectName(pkgInfo
							.getProjectName());
					pkgInfo.setVoltageGrade(voltageGradeByProjectName);
				}
			} else if (currentCol == 4) {
				pkgInfo.setProductDesc(formattedValue);
			} else if (currentCol == 5) {
				pkgInfo.setUnit(formattedValue);
			} else if (currentCol == 6) {
				pkgInfo.setCount(formattedValue);
			} else if (currentCol == 7) {
				pkgInfo.setBidNo(formattedValue);
			} else if (currentCol == 8) {
				pkgInfo.setApplyId(formattedValue);
			}

		}
	}

    @Override
    public void endRow(int rowNum) {
        if (pkgInfo.getName() != null) {
            skgs.add(pkgInfo);
        }

    }

    public List<PkgInfo> getPkgs() {
        return skgs;
    }

    public String getVoltageGradeByProjectName(String projectName) {
		if (StringUtils.isEmpty(projectName)) {
			return "";
		}
        // 正则表达式，用于匹配非数字串，+号用于匹配出多个非数字串
        String regEx = "([0-9]{1,3})(KV|Kv|kV|kv|千伏)";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(projectName);
        // 用定义好的正则表达式拆分字符串，把字符串中的数字留出来
        while (matcher.find()) {
            String voltage = matcher.group();
            return voltage;
        }
        return "";
    }

    @Override
    public void startRow(int rowNum) {
        super.startRow(rowNum);
        this.pkgInfo = new PkgInfo();
        pkgInfo.setRowId(rowNum + 1);

    }
}
