package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgTotal;

/**
 * @Description:统计信息Dao
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-20 下午8:12:05
 */
public class PkgTotalDao {

    /**
     * 保存统计信息
     * @param pkgTotals
     */
    public static void deletePkgTotals(List<PkgTotal> pkgTotals) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgTotal pkgTotal : pkgTotals) {
            String sql = "delete from pkgtotal where pkgname = '" + pkgTotal.getPkgname() + "'";
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }

    public static List<PkgTotal> getPkgTotal() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PkgTotal> sList = new ArrayList<>();
        try {
            sList = sqliteHelper.executeQuery("select * from pkgtotal", new RowMapper<PkgTotal>() {
                @Override
                public PkgTotal mapRow(ResultSet rs, int index) throws SQLException {
                    PkgTotal pkgTotal = new PkgTotal();
                    pkgTotal.setId(rs.getInt("id"));
                    pkgTotal.setPkgname(rs.getString("pkgname"));
                    pkgTotal.setCostTax(rs.getString("costTax"));

                    pkgTotal.setCost(rs.getString("cost"));
                    pkgTotal.setRateIn(rs.getString("rateIn"));
                    pkgTotal.setRateOut(rs.getString("rateOut"));
                    pkgTotal.setLimitprice(rs.getString("limitprice"));
                    pkgTotal.setRate(rs.getString("rate"));
                    pkgTotal.setRate2(rs.getString("rate2"));
                    pkgTotal.setTargetprice(rs.getString("targetprice"));
                    pkgTotal.setPrice(rs.getString("price"));
                    pkgTotal.setContribution(rs.getString("contribution"));
                    return pkgTotal;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    /**
     * 保存统计信息
     * @param pkgTotals
     */
    public static void savePkgTotals(List<PkgTotal> pkgTotals) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgTotal pkgTotal : pkgTotals) {

            String pkgname = pkgTotal.getPkgname();
            String costTax = StringUtils.trimToEmpty(pkgTotal.getCostTax());
            String cost = StringUtils.trimToEmpty(pkgTotal.getCost());
            String rateIn = StringUtils.trimToEmpty(pkgTotal.getRateIn());
            String rateOut = StringUtils.trimToEmpty(pkgTotal.getRateOut());
            String limitprice = StringUtils.trimToEmpty(pkgTotal.getLimitprice());
            String rate = StringUtils.trimToEmpty(pkgTotal.getRate());
            String rate2 = StringUtils.trimToEmpty(pkgTotal.getRate2());
            String targetprice = StringUtils.trimToEmpty(pkgTotal.getTargetprice());
            String price = StringUtils.trimToEmpty(pkgTotal.getPrice());
            String contribution = StringUtils.trimToEmpty(pkgTotal.getContribution());

            String sql =
                "INSERT INTO pkgtotal (pkgname, costTax, cost, rateIn, rateOut, limitprice, rate, rate2, targetprice, price, contribution) "
                    + "VALUES (" + "'" + pkgname + "', '" + costTax + "', '" + cost + "','" + rateIn + "','" + rateOut
                    + "','" + limitprice + "','" + rate +  "','" + rate2 + "','" + targetprice + "','" + price + "','" + contribution
                    + "'" + ")";
            sqls.add(sql);
        }
        try {
            // String sql1 = "delete from pkgtotal;";
            // String sql2 = "update sqlite_sequence SET seq = 0 where name ='pkgtotal';";
            // sqliteHelper.executeUpdate(sql1);
            // sqliteHelper.executeUpdate(sql2);

            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
}
