package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;

/**
 * @Description:成本价格库
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午4:53:09
 */
public class CostPriceDao {

    public static List<CostPrice> getCostPrices() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<CostPrice> sList = new ArrayList<>();
        try {
            sList = sqliteHelper.executeQuery("select * from costprice", new RowMapper<CostPrice>() {
                @Override
                public CostPrice mapRow(ResultSet rs, int index) throws SQLException {
                    CostPrice costPrice = new CostPrice();
                    costPrice.setId(rs.getInt("id"));
                    costPrice.setDevtype(rs.getString("devtype"));
                    costPrice.setType(rs.getInt("type"));
                    costPrice.setCostPrice(rs.getString("costprice"));
                    costPrice.setPrice(rs.getString("price"));
                    costPrice.setArea(rs.getString("area"));
                    costPrice.setSupply(rs.getString("supply"));
                    return costPrice;
                }
            });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static List<CostPrice> getCostPricesByDevType(String devType) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<CostPrice> sList = new ArrayList<>();
        try {
            sList =
                sqliteHelper.executeQuery("select * from costprice where devtype like '%" + devType + "%'",
                    new RowMapper<CostPrice>() {
                        @Override
                        public CostPrice mapRow(ResultSet rs, int index) throws SQLException {
                            CostPrice costPrice = new CostPrice();
                            costPrice.setId(rs.getInt("id"));
                            costPrice.setDevtype(rs.getString("devtype"));
                            costPrice.setType(rs.getInt("type"));
                            costPrice.setCostPrice(rs.getString("costprice"));
                            costPrice.setPrice(rs.getString("price"));
                            costPrice.setSupply(rs.getString("supply"));
                            costPrice.setArea(rs.getString("area"));
                            return costPrice;
                        }
                    });
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }

    public static void saveCostPricess(List<CostPrice> costtPrices) {

        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();

        for (CostPrice costPrice : costtPrices) {
            String devtype = StringUtils.trimToEmpty(costPrice.getDevtype());
            Integer type = costPrice.getType();
            String costprice = StringUtils.trimToEmpty(costPrice.getCostPrice());
            String price = StringUtils.trimToEmpty(costPrice.getPrice());
            String supply = StringUtils.trimToEmpty(costPrice.getSupply());
            String area = StringUtils.trimToEmpty(costPrice.getArea());

            String sql =
                "INSERT INTO costprice (devtype, type, costprice, price, supply, area) " + "VALUES (" + "'" + devtype
                    + "'" + ", " + type + ", " + "'" + costprice + "'" + ", " + "'" + price + "'" + ", " + "'" + supply
                    + "'" + ", " + "'" + area + "'" + ")";
            sqls.add(sql);
        }
        try {
            String sql1 = "delete from costprice;";
            String sql2 = "update sqlite_sequence SET seq = 0 where name ='costprice';";
            sqliteHelper.executeUpdate(sql1);
            sqliteHelper.executeUpdate(sql2);

            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
}
