/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgAdjustDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;

/**
 * 新版本调价算法
 * 
 * <AUTHOR> (mailto:<EMAIL>)
 * @version 1.0, 2022-11-16
 */
public class RealPriceResolver {

	private UIPreferences perference = UIPreferences.newInstance();
	private String ORIGINRATE = ".originrate";

	private List<PkgInfo> allPkgList;
	private List<String> warnings;
	private List<PkgAdjust> adjustList;

	public RealPriceResolver() {
		super();
		this.warnings = new ArrayList<>();
	}

	public void updateOrgPrices() {

		this.allPkgList = new ArrayList<>();
		adjustList = PkgAdjustDao.getNewPkgAdjust();
		Map<String, PkgAdjust> adjustMap = new HashMap<>();
		for (PkgAdjust adj : adjustList) {
			adjustMap.put(adj.getPkgname(), adj);
		}

		// 获取所有PKG，按照包名组装为MAP
		Map<String, List<PkgInfo>> pkgMap = getPkgMap();
		// 获取所有PRODUCT按照父节点组装为MAP
		Map<Integer, List<PkgProduct>> productMap = getProductMap();

		for (Map.Entry<String, List<PkgInfo>> entry : pkgMap.entrySet()) {

			List<PkgInfo> pkgs = entry.getValue();
			for (PkgInfo pkg : pkgs) {

				BigDecimal realPrice = CalcUtil.getBigDecimal();// 市场价
				List<PkgProduct> products = productMap.get(pkg.getId());
				// 内购权重 外购权重
				if (products == null || products.size() == 0) {
					return;
				}
				for (PkgProduct product : products) {
					String bidName = product.getName();
					String count = product.getOcunnt();
					if (StringUtil.isEmpty(bidName)) {
						continue;
					}
					if (StringUtil.isEmpty(count)) {
						continue;
					}
					if (!product.getQuote().equals("是")) {
						continue;
					}
					if (!NumberUtils.isNumber(count)) {
						continue;
					}
					if (count.equals("0")) {
						continue;
					}
					if (StringUtil.isEmpty(product.getCostprice())) {
						continue;
					}
					if (StringUtil.isEmpty(product.getPrice())) {
						continue;
					}
					BigDecimal price = new BigDecimal(product.getPrice());
					BigDecimal cost = price.multiply(new BigDecimal(count));
					realPrice = realPrice.add(cost);

				}
				pkg.setOrgPrice(String.valueOf(realPrice));

				allPkgList.add(pkg);
			}
		}
		PkgInfoDao.updateRealPricePkgInfo(allPkgList);
	}

	public void updateRealPrices() {
		
		if(GlobalData.getInstance().getSqliteHelper() == null){
			return;
		}
		this.allPkgList = new ArrayList<>();
		String orgRate = perference.getInfo(EnvirmentSettingDialog.class.getName() + ORIGINRATE);
		if (StringUtils.isEmpty(orgRate)) {
			orgRate = "1.0";
		}

		adjustList = PkgAdjustDao.getNewPkgAdjust();
		Map<String, PkgAdjust> adjustMap = new HashMap<>();
		for (PkgAdjust adj : adjustList) {
			adjustMap.put(adj.getPkgname(), adj);
		}

		// 获取所有PKG，按照包名组装为MAP
		Map<String, List<PkgInfo>> pkgMap = getPkgMap();

		for (Map.Entry<String, List<PkgInfo>> entry : pkgMap.entrySet()) {

			PkgAdjust adjust2 = adjustMap.get(entry.getKey());
			BigDecimal allRealPrice = CalcUtil.getBigDecimal();// 市场价

			List<PkgInfo> pkgs = entry.getValue();
			for (PkgInfo pkg : pkgs) {
				if (StringUtils.isEmpty(pkg.getOrgPrice())) {
					continue;
				}
				BigDecimal orgPrice = new BigDecimal(pkg.getOrgPrice());
				BigDecimal realPrice = orgPrice.multiply(new BigDecimal(orgRate));
				pkg.setRealPrice(String.valueOf(realPrice));

				if (StringUtil.isEmpty(pkg.getTotalLimitPrice()) || pkg.getTotalLimitPrice().equals("0")) {
					allRealPrice = allRealPrice.add(realPrice);
				}
				allPkgList.add(pkg);
			}

			adjust2.setRealPrice(String.valueOf(allRealPrice));
		}

		PkgInfoDao.updateRealPricePkgInfo(allPkgList);
		PkgAdjustDao.updateRealPricePkgInfo(adjustList);

	}

	// 查询货物清单信息
	private Map<String, List<PkgInfo>> getPkgMap() {
		Map<String, List<PkgInfo>> pkgMaps = new HashMap<>();
		List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
		List<PkgInfo> plist = null;
		for (PkgInfo pkgInfo : pkgs) {
			plist = new ArrayList<PkgInfo>();
			if (pkgMaps.containsKey(pkgInfo.getName())) {
				plist = pkgMaps.get(pkgInfo.getName());
				if (plist == null) {
					plist = new ArrayList<PkgInfo>();
				}
			}
			plist.add(pkgInfo);
			pkgMaps.put(pkgInfo.getName(), plist);
		}
		return pkgMaps;
	}

	// 获取货物里子项的信息
	private Map<Integer, List<PkgProduct>> getProductMap() {
		Map<Integer, List<PkgProduct>> pkgProductMaps = new HashMap<>();
		List<PkgProduct> pkgLists = PkgProductDao.getPkgProductByParentid(null);
		List<PkgProduct> plist = null;
		for (PkgProduct pkgProduct : pkgLists) {
			plist = new ArrayList<PkgProduct>();
			if (pkgProductMaps.containsKey(pkgProduct.getParentid())) {
				plist = pkgProductMaps.get(pkgProduct.getParentid());
				if (plist == null) {
					plist = new ArrayList<PkgProduct>();
				}
			}
			plist.add(pkgProduct);
			pkgProductMaps.put(pkgProduct.getParentid(), plist);
		}
		return pkgProductMaps;
	}

	public List<String> getWarnings() {
		return warnings;
	}

}