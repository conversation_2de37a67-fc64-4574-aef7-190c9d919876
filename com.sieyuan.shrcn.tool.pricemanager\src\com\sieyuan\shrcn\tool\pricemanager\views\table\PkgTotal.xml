<?xml version="1.0" encoding="UTF-8"?>
<table name="BaseInfo" desc="基本信息" class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.model.PkgTotal" 
tableClass="com.shrcn.found.ui/com.shrcn.found.ui.table.RKTable" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
  <field fixed="true" desc="序号" editor="none" name="index" width="80" default=""/>  
  <field desc="包名" editor="none" name="pkgname" width="80" default=""/>
  <field desc="含税成本（万元）" editor="none" name="costTax" width="100" default=""/> 
  <field desc="国网限价（万元）" editor="none" name="limitprice" width="100" default=""/> 
  <field desc="110KV折扣（%）" editor="none" name="rate" width="80" default=""/> 
  <field desc="220KV折扣（%）" editor="none" name="rate" width="80" default=""/> 
  <field desc="设定价（万元）" editor="none" name="targetprice" width="100" default=""/> 
  <field desc="最终报价（万元）" editor="none" name="price" width="100" default=""/> 
  <field desc="边际贡献额（万元）" editor="none" name="contribution" width="100" default=""/> 
</table>