package com.sieyuan.shrcn.tool.pricemanager.model;

import java.io.Serializable;

/**
 * BidEvaluationModel
 * <AUTHOR>
 *
 */
public class BidEvaluationModel implements Serializable{
	
	private static final long serialVersionUID = -8396651309420622481L;
	
	// 包名
    private String packageName;
    // 北京四方
    private String beijingSF;
    // 长园深瑞
    private String changyuanSR;
    // 国电南自
    private String guodianNZ;
    // 国电南瑞
    private String guodianNR;
    // 南瑞继保
    private String nanruiJB;
    // 许继
    private String xuji;
    // 思源弘瑞
    private String siyuanHR;

    // 无参构造函数
    public BidEvaluationModel() {
    }
    
    public BidEvaluationModel(String packageName) {
        this.packageName = packageName;
    }

    // 全参构造函数
    public BidEvaluationModel(String packageName, String beijingSF, String changyuanSR, String guodianNZ, String guodianNR, String nanruiJB, String xuji, String siyuanHR) {
        this.packageName = packageName;
        this.beijingSF = beijingSF;
        this.changyuanSR = changyuanSR;
        this.guodianNZ = guodianNZ;
        this.guodianNR = guodianNR;
        this.nanruiJB = nanruiJB;
        this.xuji = xuji;
        this.siyuanHR = siyuanHR;
    }

    // Getter 和 Setter 方法
    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getBeijingSF() {
        return beijingSF;
    }

    public void setBeijingSF(String beijingSF) {
        this.beijingSF = beijingSF;
    }

    public String getChangyuanSR() {
        return changyuanSR;
    }

    public void setChangyuanSR(String changyuanSR) {
        this.changyuanSR = changyuanSR;
    }

    public String getGuodianNZ() {
        return guodianNZ;
    }

    public void setGuodianNZ(String guodianNZ) {
        this.guodianNZ = guodianNZ;
    }

    public String getGuodianNR() {
        return guodianNR;
    }

    public void setGuodianNR(String guodianNR) {
        this.guodianNR = guodianNR;
    }

    public String getNanruiJB() {
        return nanruiJB;
    }

    public void setNanruiJB(String nanruiJB) {
        this.nanruiJB = nanruiJB;
    }

    public String getXuji() {
        return xuji;
    }

    public void setXuji(String xuji) {
        this.xuji = xuji;
    }

    public String getSiyuanHR() {
        return siyuanHR;
    }

    public void setSiyuanHR(String siyuanHR) {
        this.siyuanHR = siyuanHR;
    }

    @Override
    public String toString() {
        return "BidEvaluationModel{" +
                "packageName='" + packageName + '\'' +
                ", beijingSF='" + beijingSF + '\'' +
                ", changyuanSR='" + changyuanSR + '\'' +
                ", guodianNZ='" + guodianNZ + '\'' +
                ", guodianNR='" + guodianNR + '\'' +
                ", nanruiJB='" + nanruiJB + '\'' +
                ", xuji='" + xuji + '\'' +
                ", siyuanHR='" + siyuanHR + '\'' +
                '}';
    }
}
