/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PriceRateDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PriceRate;

/**
 * 行报价计算
 * 
 * <AUTHOR>
 * @version 1.0, 2020-12-4
 * 
 */
public class LinePriceResolver {

	public LinePriceResolver() {
		super();
	}

	public boolean adjustPrices(List<PriceRate> allPriceRateList) {

		// 获取所有的申请号物资信息
		List<PkgInfo> pkgList = PkgInfoDao.getPkgs();
		Map<String, PkgInfo> map = new HashMap<>();
		for (PkgInfo pkgInfo : pkgList) {
			map.put(pkgInfo.getApplyId(), pkgInfo);
		}

		List<PriceRate> updateList = new ArrayList<>();
		PriceRateDao.savePriceRates(allPriceRateList);

		// 按照申请号排序
		List<PkgInfo> updatePkgList = new ArrayList<>();
		List<PkgInfo> pkgInfoList = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
		Map<String, String> pkgInfoMap = new HashMap<String, String>();
		for (PkgInfo pkgInfo : pkgInfoList) {
			pkgInfoMap.put(pkgInfo.getApplyId(), pkgInfo.getWithoutTaxPrice());
		}

		List<PriceRate> priceRateList = PriceRateDao.getPriceRates();
		Map<String, List<PriceRate>> pkgRateMap = new HashMap<String, List<PriceRate>>();
		for (PriceRate priceRate : priceRateList) {
			String applyId = priceRate.getOrderid();
			List<PriceRate> rates = new ArrayList<>();
			if (pkgRateMap.containsKey(applyId)) {
				rates = (List<PriceRate>) pkgRateMap.get(applyId);
			}
			rates.add(priceRate);
			pkgRateMap.put(applyId, rates);
		}

		for (Map.Entry<String, List<PriceRate>> entry : pkgRateMap.entrySet()) {
			String appyId = entry.getKey();
			String withoutPrice = pkgInfoMap.get(appyId);
			if (StringUtils.isEmpty(withoutPrice)) {
				continue;
			}
			List<PriceRate> rateList = entry.getValue();
			BigDecimal pvalue = new BigDecimal(0);
			for (PriceRate priceRate : rateList) {
				pvalue = pvalue.add(new BigDecimal(priceRate.getFloatRate()).multiply(new BigDecimal(priceRate.getRate())));
			}

			BigDecimal percentValue = CalcUtil.divide("10000", String.valueOf(pvalue));
			BigDecimal perValue = percentValue.multiply(new BigDecimal(withoutPrice));
			
			String rateValue = "";

			for (PriceRate priceRate : rateList) {
				priceRate.setTaxrate(String.valueOf(UIPreferencesUtil.getTaxRate()));
				BigDecimal fPrice = perValue.multiply(new BigDecimal(priceRate.getFloatRate())).divide(new BigDecimal(100));
				BigDecimal taxRate = CalcUtil.getTaxRate();
				if (!StringUtils.isEmpty(priceRate.getfLimitPrice())) {

					// 与限价比较。含税
					BigDecimal fValue = CalcUtil.getBigDecimal(fPrice.multiply(taxRate));
					if (CalcUtil.isBigThan(fValue, new BigDecimal(priceRate.getfLimitPrice()))) {

						String msg = "分包名称:" + priceRate.getPkgName() + "，网省采购申请行号:" + priceRate.getOrderid() + "，技术规范书ID：" + priceRate.getPid() + "，辅参含税单价：" + fValue + "超出限价：" + priceRate.getfLimitPrice();
						ConsoleManager.getInstance().append(msg);
						return false;
					}
				}
				priceRate.setFprice(CalcUtil.getBigDecimalStringHalfEven(fPrice));
				
				if (CalcUtil.isEqual(new BigDecimal(priceRate.getFloatRate()), new BigDecimal("100"))) {
					rateValue = priceRate.getFprice();
				}
			}
			
			if(StringUtils.isEmpty(rateValue)){
				String msg = "网省采购申请行号:" + appyId + "未设置浮动 比例100%，请检查！";
				ConsoleManager.getInstance().append(msg);
			}
			updateList.addAll(rateList);
			String priceRateValue = CalcUtil.getBigDecimalStringHalfEven(new BigDecimal(rateValue).multiply(CalcUtil.getTaxRate()));
			PkgInfo currentPkgInfo = map.get(appyId);
			if (currentPkgInfo == null) {
				continue;
			}
			currentPkgInfo.setPricerate(priceRateValue);
			updatePkgList.add(currentPkgInfo);
		}
		PriceRateDao.updatePriceRates(updateList);
		PkgInfoDao.updatePkgInfoPriceRateById(updatePkgList);
		List<String> warnings = new ArrayList<>();
		final List<String> fwarnings = checkProductAvageValues(warnings, updateList);
        // 弹出窗口告警提示
		if (warnings != null && warnings.size() > 0) {
			Display.getDefault().syncExec(new Runnable() {
				public void run() {
					DialogHelper.showAsynWarning("辅参数部分ID报价不在均价±" + UIPreferencesUtil.getIdLimit() + "%范围以内，请参考控制台或日志记录检查！");
					ConsoleManager.getInstance().append("----------------报价不在均价±" + UIPreferencesUtil.getIdLimit() + "%范围以内----------------");
					LogUtils.recordErrInfo(fwarnings);
				}
			});
		}
		PriceRateDao.updatePriceRates(updateList);
		return true;
	}

	/**
	 * 检查偏差是否在10%
	 * @param warnings 告警信息
	 * @param updateList 待更新数据
	 * @return 
	 */
	private List<String> checkProductAvageValues(List<String> warnings, List<PriceRate> updateList) {

		Map<String, List<PriceRate>> pkgNameMaps = new HashMap<>();
		Float limit = Float.valueOf(UIPreferencesUtil.getIdLimit()) / 100;
		for (PriceRate priceRate : updateList) {
			String id = priceRate.getPid(); // 技术规范书ID
			String name = priceRate.getFunit();// 辅参数组合
			String key = id + "_" + name;
			List<PriceRate> rateList = pkgNameMaps.get(key);
			if (rateList == null) {
				rateList = new ArrayList<>();
				pkgNameMaps.put(key, rateList);
			}
			rateList.add(priceRate);
		}

		// 计算平均值
		Map<String, BigDecimal> priceAvgMap = new HashMap<>();
		Map<String, BigDecimal> pricePAvgMap = new HashMap<>();

		for (String key : pkgNameMaps.keySet()) {
			List<PriceRate> priceRateList = pkgNameMaps.get(key);
			BigDecimal sum = new BigDecimal("0");
			BigDecimal psum = new BigDecimal("0");
			int count = 0;
			int pcount = 0;
			for (PriceRate priceRate : priceRateList) {
				sum = sum.add(new BigDecimal(priceRate.getFprice()).multiply(new BigDecimal(priceRate.getCount())));
				count += Float.parseFloat(priceRate.getCount());
				psum = psum.add(new BigDecimal(priceRate.getFprice()));
				pcount++;
			}
			BigDecimal avg = CalcUtil.getAverageValue(sum, count);
			priceAvgMap.put(key, avg);

			BigDecimal pavg = CalcUtil.getAverageValue(psum, pcount);
			pricePAvgMap.put(key, pavg);
		}

		// 更新
		for (PriceRate priceRate : updateList) {
			String id = priceRate.getPid(); // 技术规范书ID
			String name = priceRate.getFunit();// 辅参数组合
			String key = id + "_" + name;
			BigDecimal avg = priceAvgMap.get(key);

			BigDecimal priceMax = CalcUtil.getPriceMax(avg, limit);
			BigDecimal priceMin = CalcUtil.getPriceMin(avg, limit);

			BigDecimal pavg = pricePAvgMap.get(key);
			BigDecimal perMax = CalcUtil.getPriceMax(pavg, limit);
			BigDecimal perMin = CalcUtil.getPriceMin(pavg, limit);

			String priceTax = priceRate.getFprice();
			if (CalcUtil.isBigThan(new BigDecimal(0.000001), avg)) {
				warnings.add(key + " 均价为0。");
			} else {
				priceRate.setAvg(avg.toString());
				boolean valid = CalcUtil.isBigThan(priceMax, new BigDecimal(priceTax)) && CalcUtil.isBigThan(new BigDecimal(priceTax), priceMin);
				if (valid) {
					priceRate.setValid("1");
				} else {
					priceRate.setValid("2");
				}

				priceRate.setPavg(pavg.toString());
				boolean pvalid = CalcUtil.isBigThan(perMax, new BigDecimal(priceTax)) && CalcUtil.isBigThan(new BigDecimal(priceTax), perMin);

				if (valid) {
					priceRate.setValid("1");
				} else {
					priceRate.setValid("2");
				}
				if (pvalid) {
					priceRate.setPvalid("1");
				} else {
					priceRate.setPvalid("2");
				}

				if (valid && pvalid) {
					priceRate.setValid("1");
				} else {
					priceRate.setValid("2");
				}

				if (!valid) {
					warnings.add(key + " 不在加权均价±" + UIPreferencesUtil.getIdLimit() + "%范围以内！当前价：" + priceTax + "，均价：" + avg + " [" + priceMin + "，" + priceMax + "]。");
				}
				if (!pvalid) {
					warnings.add(key + " 不在算术均价±" + UIPreferencesUtil.getIdLimit() + "%范围以内！当前价：" + priceTax + "，均价：" + pavg + " [" + perMin + "，" + perMax + "]。");
				}
			}
		}
		return warnings;
	}

	@Override
	public String toString() {
		return "LinePriceResolver []";
	}

}