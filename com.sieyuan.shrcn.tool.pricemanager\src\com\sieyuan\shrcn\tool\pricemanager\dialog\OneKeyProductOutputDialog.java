package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.ModifyEvent;
import org.eclipse.swt.events.ModifyListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.TimeCounter;
import com.shrcn.found.file.util.FileManipulate;
import com.shrcn.found.ui.app.WrappedTitleAreaDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.TypeRule;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.utils.ProductExportUtil;

/**
 * 一键导出开标结果
 * 
 * <AUTHOR>
 * 
 */
public class OneKeyProductOutputDialog extends WrappedTitleAreaDialog {

	private Text typeRule;
	private Text orginDir;
	
	private String TYPERULE = ".typeRule";
	private String ORGINDIR = ".orginDir";
	
	private Button selectBtn;
	private Map<String, String> priceMaps;
	private UIPreferences perference = UIPreferences.newInstance();

	public OneKeyProductOutputDialog(Shell parentShell) {
		super(parentShell);
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("一键导出开标");
	}

	@Override
	protected Control createDialogArea(Composite parent) {

		setTitle("一键导出开标");
		setMessage("导出开标文件+开标合并文件+开标一纸文件");
		Composite container = new Composite(parent, SWT.FILL);
		container.setLayout(new GridLayout(3, false));
		container.setLayoutData(new GridData(GridData.FILL_BOTH));

		typeRule = SwtUtil.createFileSelector(container, "型号品牌填写规则：", "*.xlsx");
		orginDir = SwtUtil.createDirectorySelector(container, "行报价分析表：", "设置行报价分析表路径");
		
		initData();
		addListeners();
		return container;
	}
	
	private void initData() {
		typeRule.setText(perference.getInfo(OneKeyProductOutputDialog.class.getName() + TYPERULE));
		orginDir.setText(perference.getInfo(OneKeyProductOutputDialog.class.getName() + ORGINDIR));
	}

	/**
	 * 添加Listener
	 */
	private void addListeners() {
		typeRule.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				perference.setInfo(OneKeyProductOutputDialog.class.getName() + TYPERULE, typeRule.getText());
			}
		});
		orginDir.addModifyListener(new ModifyListener() {
			@Override
			public void modifyText(ModifyEvent e) {
				perference.setInfo(OneKeyProductOutputDialog.class.getName() + ORGINDIR, orginDir.getText());
			}
		});
	
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == OK) {

			final String pkgPath = orginDir.getText();
			final String outRulePath = typeRule.getText();
			if (StringUtils.isEmpty(pkgPath)) {
				DialogHelper.showAsynError("行报价分析表不能为空！");
				return;
			}
			if (!selectBtn.getSelection()) { // 非协议库存
				if (StringUtils.isEmpty(outRulePath)) {
					DialogHelper.showAsynError("型号品牌填写规则不能为空！");
					return;
				}
			}

			Shell shell = Display.getDefault().getActiveShell();
			final String directory = DialogHelper.selectDirectory(shell, SWT.SAVE, "设置文件保存文件", "请设置文件保存文件");
			if (StringUtils.isEmpty(directory)) {
				return;
			}

			final boolean selection = selectBtn.getSelection();
			ProgressManager.execute(new IRunnableWithProgress() {
				@Override
				public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
					monitor.beginTask("正在导出开标文件中，请稍候...", 100000);
					TimeCounter.begin();
					String kbFolder = directory + File.separator + "开标文件";
					ProductExportUtil.export(kbFolder, "全部", true, 1, monitor);
					TimeCounter.end("导出总耗时");
					ConsoleManager.getInstance().append("导出开标文件完成！");

					final File[] files = new File(kbFolder).listFiles();
					if (files == null) {
						DialogHelper.showAsynWarning("开标文件夹下没有文件，请检查!");
						return;
					}
					final List<String> paths = new ArrayList<>();
					for (File f : files) {
						if (f.isDirectory()) {
							paths.add(f.getPath());
						}
					}

					if (paths == null || paths.size() == 0) {
						DialogHelper.showAsynWarning("开标文件夹下没有包文件夹，请检查!");
						return;
					}
					String mergeFolder = directory + File.separator + "开标合并";
					monitor.beginTask("正在合并开标文件数据中，请稍候...", 100 * (paths.size() + 1));
					ProductExportUtil.mergeProductFiles(mergeFolder, paths, monitor);
					monitor.worked(100);
					TimeCounter.end("合并总耗时");
					ConsoleManager.getInstance().append("开标文件合并成功！");

					String proFolder = directory + File.separator + "开标一纸";
					Map<String, TypeRule> typeMap = new HashMap<>();
					if (!selection) { // 非协议库存
						monitor.beginTask("正在解析型号品牌填写规则...", 5);
						typeMap = ProductExportUtil.parseTypeRule(outRulePath);
					}
					monitor.beginTask("正在解析单价汇总表...", 5);
					List<PkgInfo> pkgList = new ArrayList<>();
					// 2、按照包号获取物料清单
					priceMaps = new HashMap<>();

					List<String> allFiles = new ArrayList<String>();
					FieldUtils.getAllFilePaths(new File(kbFolder), allFiles);

					// 1、解析货物清单
					for (String fileName : allFiles) {
						String priceFile = new File(fileName).getName();
						if (priceFile.contains("包") && priceFile.endsWith(ToolConstants.XLSX)) {
							ProductExportUtil.parsePkg(fileName, pkgList);
						}
					}

					for (PkgInfo pkgInfo : pkgList) {
						priceMaps.put(pkgInfo.getApplyId(), pkgInfo.getWithoutTaxPrice());
					}

					monitor.worked(10);
					monitor.done();

					List<File> fileList = FileManipulate.getSubFiles(pkgPath);
					monitor.beginTask("正在生成开标文件一纸文件...", fileList.size() * 30);

					List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
					Map<String, PkgInfo> pkgMap = new HashMap<>();
					for (PkgInfo pkgInfo : pkgs) {
						pkgMap.put(pkgInfo.getApplyId(), pkgInfo);
					}

					for (final File fileItem : fileList) {
						String templatePath = fileItem.getAbsolutePath();
						String fileName = fileItem.getName();
						String templateOutput = proFolder + File.separator + fileName;
						FileManipulate.initDir(proFolder);
						FileManipulate.copyByChannel(templatePath, templateOutput);
						generateExcute(templateOutput, typeMap, pkgMap, selection, monitor);
						monitor.worked(30);
					}
					ConsoleManager.getInstance().append("开标一纸导出成功！");
					monitor.done();

					DialogHelper.showAsynInformation("一键导出开标成功");
				}
			}, false);
			return;
		}
		super.buttonPressed(buttonId);
	}

	/**
	 * 执行生成过程
	 * 
	 * @param typeMap
	 * @param pkgProductMap
	 * @param pkgMap
	 * @param selection
	 * 
	 * @param bidMaps2
	 * 
	 * @param needModify
	 * 
	 */
	public void generateExcute(String templateOutput, Map<String, TypeRule> typeMap, Map<String, PkgInfo> pkgMap, boolean selection, IProgressMonitor monitor) {
		// 处理excel文件
		if (selection) {
			ProductExportUtil.writeExcelPOI(templateOutput, priceMaps);
		} else {
			ProductExportUtil.writeExcelPOI(templateOutput, typeMap, pkgMap, priceMaps);
		}
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(600, 300);
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		((GridLayout) parent.getLayout()).numColumns++;
		selectBtn = SwtUtil.createCheckBox(parent, "旧版本格式", new GridData());

		createButton(parent, IDialogConstants.OK_ID, IDialogConstants.OK_LABEL, true);
		createButton(parent, IDialogConstants.CANCEL_ID, IDialogConstants.CANCEL_LABEL, false);
	}
}
