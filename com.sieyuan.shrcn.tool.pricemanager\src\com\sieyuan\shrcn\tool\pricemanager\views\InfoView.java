package com.sieyuan.shrcn.tool.pricemanager.views;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.layout.FillLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;

import com.sieyuan.shrcn.tool.pricemanager.utils.TimeUtils;

/**
 * @Description:信息view
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午11:08:22
 */
public class InfoView {

    private static StyledText text;

    public static StyledText getText() {
        return text;
    }

    public static void logInfo(String info) {
        if (info != null) {
            text.append(TimeUtils.time() + "    " + info + "\n");
        }
    }

    public static void logInfoInthread(final String info) {
        Display.getDefault().asyncExec(new Runnable() {
            @Override
            public void run() {
                text.append(TimeUtils.time() + "    " + info + "\n");
            }
        });
    }

    public InfoView(Composite parent) {
        final Composite composite = new Composite(parent, SWT.NONE);
        FillLayout fl_composite = new FillLayout();
        fl_composite.marginWidth = 5;
        fl_composite.marginHeight = 5;
        composite.setLayout(fl_composite);

        text = new StyledText(composite, SWT.BORDER | SWT.H_SCROLL | SWT.V_SCROLL | SWT.CANCEL);

    }

}
