package com.sieyuan.shrcn.tool.pricemanager.composite;

import java.util.ArrayList;
import java.util.List;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Label;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.UIConstants;
import com.sieyuan.shr.u21.ui.table.Table;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.TreeData;
import com.sieyuan.shrcn.tool.pricemanager.views.table.PkgInfoTableMode;

/**
 * @Description:PkgInfoComposite
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-14 下午1:43:05
 */
public class PkgInfoComposite extends Composite {

    private PkgInfoTableMode pkgInfoTableMode;
    // private GlobalData instance;
    private List<PkgInfo> pkgitemList = new ArrayList<PkgInfo>();

    private Table table;
    private TreeData treeData;

    public PkgInfoComposite(CTabFolder parent, TreeData treeData, int none) {
        super(parent, SWT.NONE);
        this.treeData = treeData;
        createCompisiteArea(this);
    }

    private void createCompisiteArea(Composite container) {
        container.setLayout(new GridLayout(6, false));

        Label gridlabel = new Label(container, SWT.NONE);
        gridlabel.setLayoutData(new GridData(SWT.LEFT, SWT.FILL, false, false, 6, 1));
        gridlabel.setText(ToolConstants.PKG_TITLE);

        table = new Table(container, UIConstants.KTABLE_CELL_STYLE);
        table.setLayout(new GridLayout(1, false));
        GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 6, 10);
        gd_table.widthHint = 479;
        table.setLayoutData(gd_table);
        this.pkgInfoTableMode = new PkgInfoTableMode() {
            @Override
            public void doSetContentAt(int col, int row, Object value) {
                super.doSetContentAt(col, row, value);
                if (col == 0 && row > 0) {
                    table.redraw();
                }
            }
        };
        table.setModel(pkgInfoTableMode);

        int type = treeData.getType();

        // 不同类型显示不同界面
        if (type == 1) {
            pkgitemList = (List<PkgInfo>)PkgInfoDao.getPkgsWithLimitPrice("", "", "");
        } else if (type == 2) {
            pkgitemList = (List<PkgInfo>)PkgInfoDao.getPkgsWithLimitPrice("", "", "");
        } else if (type == 3) {
            pkgitemList = (List<PkgInfo>)PkgInfoDao.getPkgsWithLimitPrice(treeData.getKey(), "", "");
        } else if (type == 4) {
            String[] array = treeData.getKey().split(ToolConstants.CONNECT);
            pkgitemList = (List<PkgInfo>)PkgInfoDao.getPkgsWithLimitPrice(array[0], array[1], "");
        } else if (type == 5) {
            pkgitemList = (List<PkgInfo>)PkgInfoDao.getPkgsWithLimitPrice("", "", treeData.getKey());
        } else if (type == 6) {
            String[] array = treeData.getKey().split(ToolConstants.CONNECT);
            pkgitemList = (List<PkgInfo>)PkgInfoDao.getPkgsWithLimitPrice(array[1], "", array[0]);
        }

        List<Integer> noLimitPriceList = new ArrayList<>();
        for (int i = 0; i < pkgitemList.size(); i++) {
            if (StringUtil.isEmpty(pkgitemList.get(i).getLimitPrice())) {
                noLimitPriceList.add(i + 1);
            }
        }
        pkgInfoTableMode.setNoLimitPriceList(noLimitPriceList);
        pkgInfoTableMode.setItems(pkgitemList);

        table.redraw();
    }

}
