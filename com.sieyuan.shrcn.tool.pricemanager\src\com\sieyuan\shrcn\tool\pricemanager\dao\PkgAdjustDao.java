package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;

public class PkgAdjustDao {

	public static List<PkgAdjust> getNewPkgAdjust() {
		SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
		List<PkgAdjust> sList = new ArrayList<>();
		try {
			sList = sqliteHelper.executeQuery("select * from pkgadjust", new RowMapper<PkgAdjust>() {
				@Override
				public PkgAdjust mapRow(ResultSet rs, int index) throws SQLException {
					PkgAdjust pkgAdjust = new PkgAdjust();
					pkgAdjust.setId(rs.getInt("id"));
					pkgAdjust.setPkgname(rs.getString("pkgname"));
					pkgAdjust.setTargetprice(rs.getString("targetprice"));
					pkgAdjust.setRate(rs.getString("rate"));
					pkgAdjust.setRate2(rs.getString("rate2"));
					pkgAdjust.setLimitprice(rs.getString("limitprice"));
					pkgAdjust.setRealPrice(rs.getString("realPrice"));
					pkgAdjust.setRor(rs.getString("ror"));
					pkgAdjust.setRor2(rs.getString("ror2"));
					pkgAdjust.setResult(rs.getString("result"));
					pkgAdjust.setSeq(rs.getInt("seq"));
					pkgAdjust.setHasOnlyId(Boolean.valueOf(rs.getString("hasOnlyId")));
					pkgAdjust.setIsPriority(Boolean.valueOf(rs.getString("isPriority")));
					return pkgAdjust;
				}
			});
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return sList;
	}

	/**
	 * 保存统计信息
	 * 
	 * @param pkgTotals
	 */
	public static void saveNewPkgAdjusts(List<PkgAdjust> pkgAdjusts) {
		SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
		List<String> sqls = new ArrayList<String>();
		for (PkgAdjust pkgAdjust : pkgAdjusts) {

			String pkgname = pkgAdjust.getPkgname();
			String targetPrice = StringUtils.trimToEmpty(pkgAdjust.getTargetprice());
			String rate = StringUtils.trimToEmpty(pkgAdjust.getRate());
			String rate2 = StringUtils.trimToEmpty(pkgAdjust.getRate2());
			String realprice = StringUtils.trimToEmpty(pkgAdjust.getRealPrice());
			String limitprice = StringUtils.trimToEmpty(pkgAdjust.getLimitprice());
			String ror = StringUtils.trimToEmpty(pkgAdjust.getRor());
			String ror2 = StringUtils.trimToEmpty(pkgAdjust.getRor2());
			String result = StringUtils.trimToEmpty(pkgAdjust.getResult());
			String hasOnlyId = StringUtils.trimToEmpty(String.valueOf(pkgAdjust.getHasOnlyId()));
			String isPriority = StringUtils.trimToEmpty(String.valueOf(pkgAdjust.getIsPriority()));
			String seq = StringUtils.trimToEmpty(String.valueOf(pkgAdjust.getSeq()));

			String sql = "INSERT INTO pkgadjust (pkgname, rate, rate2, result, seq, targetprice, limitprice, realprice, hasOnlyId, isPriority, ror, ror2) " + "VALUES (" + "'" 
					+ pkgname + "', '" + rate + "', '" + rate2 + "', '" + result + "', '" + seq + "', '" + targetPrice + "','" + limitprice + "','" + realprice
					+ "','" + hasOnlyId + "','" + isPriority + "','" + ror + "','" + ror2 + "')";
			sqls.add(sql);
		}
		try {
			String sql1 = "delete from pkgadjust;";
			String sql2 = "update sqlite_sequence SET seq = 0 where name ='pkgadjust';";

			sqliteHelper.executeUpdate(sql1);
			sqliteHelper.executeUpdate(sql2);

			sqliteHelper.executeUpdate(sqls);

		} catch (ClassNotFoundException | SQLException e) {
			e.printStackTrace();
		}
	}
	
    public static void updateRealPricePkgInfo(List<PkgAdjust> pkgAdjusts) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgAdjust pkgInfo : pkgAdjusts) {
            Integer id = pkgInfo.getId();
            String realPrice = pkgInfo.getRealPrice();
            
            String sql =
                "update pkgadjust set realprice='" + realPrice + "' where id=" + id;
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
    
    public static void updateResultPricePkgInfo(List<PkgAdjust> pkgAdjusts) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
        for (PkgAdjust pkgInfo : pkgAdjusts) {
            Integer id = pkgInfo.getId();
            String result = pkgInfo.getResult();
            
            String sql =
                "update pkgadjust set result='" + result + "' where id=" + id;
            sqls.add(sql);
        }
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
    
}
