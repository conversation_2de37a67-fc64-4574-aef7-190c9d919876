package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.util.List;
import java.util.Map;

import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;

/**
 * 价格数据上下文类
 * 封装价格计算过程中需要的所有数据
 */
public class PriceDataContext {
    
    private final Map<String, List<PkgInfo>> pkgMap;
    private final Map<Integer, List<PkgProduct>> productMap;
    private final PackageCategories categories;
    private final PriceConfiguration config;
    
    public PriceDataContext(Map<String, List<PkgInfo>> pkgMap, 
                           Map<Integer, List<PkgProduct>> productMap, 
                           PackageCategories categories, 
                           PriceConfiguration config) {
        this.pkgMap = pkgMap;
        this.productMap = productMap;
        this.categories = categories;
        this.config = config;
    }
    
    public Map<String, List<PkgInfo>> getPkgMap() {
        return pkgMap;
    }
    
    public Map<Integer, List<PkgProduct>> getProductMap() {
        return productMap;
    }
    
    public PackageCategories getCategories() {
        return categories;
    }
    
    public PriceConfiguration getConfig() {
        return config;
    }
} 