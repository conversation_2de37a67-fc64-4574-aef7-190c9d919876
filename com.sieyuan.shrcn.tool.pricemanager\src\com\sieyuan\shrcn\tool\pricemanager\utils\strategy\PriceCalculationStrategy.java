package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;

/**
 * 价格计算策略接口
 * 遵循策略模式，支持不同的价格计算算法
 */
public interface PriceCalculationStrategy {
    
    /**
     * 计算价格
     * 
     * @param pkgList 包信息列表
     * @param adjust 调价信息
     * @param config 配置信息
     * @param priceMap 价格映射
     * @return 价格计算结果
     */
    PriceCalculationResult calculatePrices(List<PkgInfo> pkgList, 
                                         PkgAdjust adjust, 
                                         PriceConfiguration config, 
                                         Map<String, BigDecimal> priceMap);
} 