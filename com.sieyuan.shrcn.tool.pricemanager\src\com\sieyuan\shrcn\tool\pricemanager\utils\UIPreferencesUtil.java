package com.sieyuan.shrcn.tool.pricemanager.utils;

import org.apache.commons.lang.StringUtils;

import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;

public class UIPreferencesUtil {

	private static UIPreferences perference = UIPreferences.newInstance();

	private static String ID_LIMIT = "idLimit";
	private static String TAXRATE = ".taxrate";

	public static String getIdLimit() {
		String infopath = EnvirmentSettingDialog.class.getName();
		String idLimitTxt = perference.getInfo(infopath + ID_LIMIT);
		if (idLimitTxt.equals("")) {
			idLimitTxt = ToolConstants.LIMIT;
		}
		return idLimitTxt;
	}

	public static int getTaxRate() {
		String infopath = EnvirmentSettingDialog.class.getName();
		String taxRate = perference.getInfo(infopath + TAXRATE);
		int rate = ToolConstants.rate;
		if (!StringUtils.isEmpty(taxRate)) {
			rate = Integer.valueOf(taxRate);
		}
		return rate;
	}

}
