package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;

@SuppressWarnings("rawtypes")
public class CostPriceHandler extends SheetsHandler {

    private CostPrice costPrice;
    private List<CostPrice> costPriceList;

    public CostPriceHandler() {
        costPriceList = new ArrayList<>();
    }

    @Override
    public void cell(String cellReference, String formattedValue, XSSFComment comment) {
        super.cell(cellReference, formattedValue, comment);
        if (currentRow > 1 && !isEmpty(formattedValue)) {
            if (currentCol == 0) {
                costPrice.setDevtype(StringUtils.trim(formattedValue));
            } else if (currentCol == 3) { // 类别
                costPrice.setPrice(formattedValue);
            } else if (currentCol == 6) { // 内部报价
                costPrice.setType(1);
                costPrice.setPrice(formattedValue);
            } else if (currentCol == 7) { // 内部成本价格
                costPrice.setType(1);
                costPrice.setCostPrice(formattedValue);
            } else if (currentCol == 9) { // 外部价格
                costPrice.setType(2);
                costPrice.setPrice(formattedValue);
            } else if (currentCol == 10) { // 外部价格
                costPrice.setType(2);
                costPrice.setCostPrice(formattedValue);
            } else if (currentCol == 15) {
                costPrice.setSupply(formattedValue);
            } else if (currentCol == 16) {
                costPrice.setArea(formattedValue);
            }
        }
    }

    @Override
    public void endRow(int rowNum) {
        if (costPrice.getDevtype() != null) {
            costPriceList.add(costPrice);
        }
    }

    public List<CostPrice> getCostPriceList() {
        return costPriceList;
    }

    public void setCostPriceList(List<CostPrice> costPriceList) {
        this.costPriceList = costPriceList;
    }

    @Override
    public void startRow(int rowNum) {
        super.startRow(rowNum);
        this.costPrice = new CostPrice();
    }

}
