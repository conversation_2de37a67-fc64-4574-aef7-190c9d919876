package com.sieyuan.shrcn.tool.pricemanager.wizard;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.sql.SQLException;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.jface.wizard.Wizard;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.common.util.TimeCounter;
import com.shrcn.found.ui.dialog.MessageDialog;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.dao.ProjectDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.SqliteHelper;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dialog.PkgSelectDialog;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.Project;
import com.sieyuan.shrcn.tool.pricemanager.sax.PackageImporter;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;

public class ProjectWizard extends Wizard {
	
	private EnvirmentPage envirmentPage; // 页面二，继承自WizardPage类
    private UIPreferences perference = UIPreferences.newInstance();
    private ProductPage productPage;// 页面三，继承自WizardPage类
    // 把两页面对象定义成实例变量，使其他方法能访问得到
    private ProjectPage projectPage;// 页面一，继承自WizardPage类
	
    public ProjectWizard() {
		super();
		this.setWindowTitle("新建工程");
	}

	// 在此方法将两页面加入并设置初值
    public void addPages() {
        // 创建页面对象，并设置页面的名称
        projectPage = new ProjectPage("projectPage");
        productPage = new ProductPage("roductPage");
        envirmentPage = new EnvirmentPage("envirmentPage");

        // 加入两页面，加入的顺序就是界面上显示的顺序
        addPage(projectPage);
        addPage(envirmentPage);
        addPage(productPage);
    }

    // 由此方法判断“完成”按钮何时有效。返回true则有效，false无效
    public boolean canFinish() {
        // 设置成：当还没到最后一页时“完成”按钮无效
        if (this.getContainer().getCurrentPage() != productPage)
            return false;
        return super.canFinish();
    }

    // 当单击“完成”按钮退出向导时，将执行此方法
    public boolean performFinish() {

        final String root = envirmentPage.getPkgPath();
        final String limitPath = envirmentPage.getLimitPath();
        final String pricePath = envirmentPage.getPricePath();
        final String zbId = envirmentPage.getIdText();
        final String taxRate = envirmentPage.getTaxText();

        if (!new File(root).exists()) {
            MessageDialog.openWarning(this.getShell(), "新建工程", "物料清单信息表" + root + "不存在！");
            return false;
        }

        if (!new File(limitPath).exists()) {
            MessageDialog.openWarning(this.getShell(), "新建工程", "限价信息表" + limitPath + "不存在！");
            return false;
        }
        if (!new File(pricePath).exists()) {
            MessageDialog.openWarning(this.getShell(), "新建工程", "成本价信息表" + limitPath + "不存在！");
            return false;
        }
        if (StringUtils.isEmpty(zbId)) {
            MessageDialog.openWarning(this.getShell(), "新建工程", "招标编号不能为空！");
            return false;
        }
        if (StringUtils.isEmpty(taxRate)) {
            MessageDialog.openWarning(this.getShell(), "新建工程", "税率不能为空！");
            return false;
        }

        PkgSelectDialog expDlg = new PkgSelectDialog(Display.getDefault().getActiveShell(), root);
        expDlg.open();

        final String[] selectNames = expDlg.getPkgs();
        if (selectNames == null || selectNames.length == 0) {
            MessageDialog.openWarning(this.getShell(), "选择导入的包", "导入包不能为空！");
            return false;
        }

        final List<String> allDocxAbsoluteFile = productPage.getAllDocxAbsoluteFile();
        final List<String> allExcelAbsoluteFile = productPage.getAllExcelAbsoluteFile();

        ProgressManager.execute(new IRunnableWithProgress() {
            @Override
            public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {

                Project project = new Project();
                project.setProjectName(projectPage.getName());
                project.setZbId(zbId);
                project.setTaxRate(taxRate);
                ProjectDao.addProject(project);
                perference.setInfo("com.shrcn.pricemangertool.curentprojectname", projectPage.getName());
                monitor.beginTask("正在导入数据中，请稍候...", 50 + 3 * allDocxAbsoluteFile.size());

                // 异步运行
                Display.getDefault().asyncExec(new Runnable() {
                    public void run() {
                        try {
                            FieldUtils.copyDir(productPage.getDiretoryPath(),
                                DirManager.getInputDir(projectPage.getName()));
                        } catch (IOException e1) {
                            e1.printStackTrace();
                        }
                    }
                });

                createDatabase(monitor);

                PackageImporter imp =
                    new PackageImporter(root, allDocxAbsoluteFile, allExcelAbsoluteFile, limitPath, pricePath,
                        selectNames);
                TimeCounter.begin();
                imp.execute(monitor);

                TimeCounter.end("导入总耗时");
                ConsoleManager.getInstance().append("创建工程【" + projectPage.getName() + "】成功！");
                monitor.done();
                NavigatView.refreshTree();
            }
        });

        return true;
    }
    
    
    // 创建数据库
	private void createDatabase(IProgressMonitor monitor) {
		try {
            monitor.setTaskName("第一步：创建数据库文件");
            SqliteHelper sqliteHelper = new SqliteHelper(DirManager.getProjectFile(projectPage.getName()));
            GlobalData.getInstance().setSqliteHelper(sqliteHelper);

            String[] dropSqls = new String[10];
            
            dropSqls[0] = "drop table if exists costprice;";
            dropSqls[1] = "drop table if exists limitprice;";
            dropSqls[2] = "drop table if exists pkginfo;";
            dropSqls[3] = "drop table if exists product;";
            dropSqls[4] = "drop table if exists pkgproduct;";
            dropSqls[5] = "drop table if exists pkgerrinfo;";
            dropSqls[6] = "drop table if exists devprice;";
            dropSqls[7] = "drop table if exists pkgtotal;";
            dropSqls[8] = "drop table if exists pkgadjust;";
            dropSqls[9] = "drop table if exists pricerate;";
            
			for (String dropSql : dropSqls) {
				sqliteHelper.executeUpdate(dropSql);
			}

            monitor.worked(10);

            String[] createSqls = new String[dropSqls.length];
            createSqls[0] =
                "CREATE TABLE 'product' (" + "'id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                    + "'orderid' INTEGER NULL," + "'number' INTEGER NULL," + "'name' TEXT(100) NOT NULL,"
                    + "'bidno' TEXT(100) NOT NULL," + "'devname'  TEXT(100)," + "'devtype'  TEXT(100),"
                    + "'unit'  TEXT(100) NOT NULL," + "'count'  INTEGER NOT NULL," + "'odevtype'  TEXT(100),"
                    + "'ocunnt'  INTEGER NOT NULL," + "'supply'  TEXT(100)," + "'area' TEXT(100),"
                    + "'quote' TEXT(100)," + "'prowid' INTEGER NOT NULL" + ");";
            createSqls[1] =
                "CREATE TABLE 'pkgproduct' (" + "'id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                    + "'parentid' INTEGER NOT NULL," + "'orderid' INTEGER NULL," + "'number' INTEGER NOT NULL,"
                    + "'name' TEXT(100) NOT NULL," + "'bidno' TEXT(100) NOT NULL," + "'devname'  TEXT(100),"
                    + "'devtype'  TEXT(100)," + "'unit'  TEXT(100) NOT NULL," + "'count'  INTEGER NOT NULL,"
                    + "'odevtype'  TEXT(100)," + "'ocunnt'  INTEGER NOT NULL," + "'supply'  TEXT(100),"
                    + "'area' TEXT(100)," + "'intelligence' TEXT(100)," + "'quote' TEXT(100),"
                    + "'searchdevtype'  TEXT(100)," + "'lntype'  TEXT(100)," + "'costprice'  TEXT(100),"
                    + "'weight'  TEXT(100)," + "'price'  TEXT(100)," + "'totalprice'  TEXT(100),"
                    + "'prowid' INTEGER NOT NULL" + ");";
            createSqls[2] =
                "CREATE TABLE 'pkginfo' (" + "'id' INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                    + "'name' TEXT (100) NOT NULL," + "'project_owner' TEXT (100) NOT NULL,"
                    + "'project_name' TEXT (100) NOT NULL," + "'product' TEXT (100) NOT NULL,"
                    + "'product_desc' TEXT (100)," + "'unit' TEXT (100) NOT NULL,"
                    + "'count' INTEGER NOT NULL," + "'intelligence' TEXT (100) NOT NULL,"
                    + "'voltage_grade' TEXT (100)," + "'applyId' TEXT (100)," + "'bidno' TEXT (100)," + "'prowid' INTEGER NOT NULL,"
                    + "'totaltax' TEXT(100)," 
                    + "'isSameId' TEXT(100)," // 是否为相同ID
                    + "'totallimitprice' TEXT(100)," + "'totaltarget' TEXT(100),"
                    + "'price' TEXT(100)," + "'total' TEXT(100)," + "'wthoutTaxPrice' TEXT(100),"
                    + "'withOutTaxTotal' TEXT(100)," + "'isValid' TEXT(100)," + "'avg' TEXT(100)," 
                    + "'pavg' TEXT(100)," + "'pisValid' TEXT(100)," + "'valid' TEXT(100)," 
                    + "'avgRate' TEXT(100)," + "'wisValid' TEXT(100)," + "'wavg' TEXT(100)," 
                    + "'wpavg' TEXT(100)," + "'wpisValid' TEXT(100)," + "'wvalid' TEXT(100)," + "'realprice' TEXT(100)," + "'orgPrice' TEXT(100)," 
                    + "'wavgRate' TEXT(100)," + "'pricerate' TEXT(100)," + "'totalprice' TEXT(100)" + ");";
            createSqls[3] =
                "CREATE TABLE 'limitprice' (" + "'id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                    + "'product'  TEXT(100) NOT NULL," + "'count'  INTEGER NOT NULL,"
                    + "'unit'  TEXT(100) NOT NULL," + "'limitprice'  TEXT(100) NOT NULL);";
            createSqls[4] =
                "CREATE TABLE 'costprice' ('id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                    + "'devtype'  TEXT(100) NOT NULL," + "'type'  INTEGER NULL,"
                    + "'costprice'  TEXT(100) NOT NULL," + "'supply'  TEXT(100) NOT NULL,"
                    + "'area'  TEXT(100) NOT NULL," + "'price'  TEXT(100) NOT NULL);";
            createSqls[5] =
                "CREATE TABLE 'pkgerrinfo' ('id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                    + "'parentid'  INTEGER NOT NULL," + "'errtype'  INTEGER NOT NULL, "
                    + "'errdesc'  TEXT(100) NOT NULL);";

            createSqls[6] =
                "CREATE TABLE 'devprice' (" + "'id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                    + "'orderid' INTEGER NULL," + "'number' INTEGER NULL," + "'name' TEXT(100) NOT NULL,"
                    + "'bidno' TEXT(100) NOT NULL," + "'devname'  TEXT(100)," + "'devtype'  TEXT(100),"
                    + "'unit'  TEXT(100) NOT NULL," + "'count'  INTEGER NOT NULL," + "'odevtype'  TEXT(100),"
                    + "'ocunnt'  INTEGER NOT NULL," + "'supply'  TEXT(100)," + "'area' TEXT(100),"
                    + "'quote' TEXT(100)," + "'searchdevtype' TEXT(100)," + "'lntype'  TEXT(100),"
                    + "'costprice'  TEXT(100),"  + "'file'  TEXT(100)," + "'price'  TEXT(100)" + ");";

            createSqls[7] =
                "CREATE TABLE 'pkgtotal' ('id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,"
                    + "'pkgname' TEXT(100)," + "'costTax' TEXT(100), " + "'cost' TEXT(100), "
                    + "'rateIn' TEXT(100), " + "'rateOut'  TEXT(100), " + "'limitprice' TEXT(100), "
                    + "'contribution' TEXT(100), " + "'rate' TEXT(100), " + "'rate2' TEXT(100), " + "'targetprice' TEXT(100), "
                    + "'price'  TEXT(100));";
			
			createSqls[8] = "CREATE TABLE 'pkgadjust' ('id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL," 
					+ "'pkgname' TEXT(100)," + "'rate' TEXT(100)," + "'rate2' TEXT(100)," + "'result' TEXT(100)," + "'seq' INTEGER," + "'realPrice' TEXT(100)," 
					+ "'hasOnlyId' TEXT(100)," + "'limitprice' TEXT(100)," + "'isPriority' TEXT(100)," + "'ror' TEXT(100)," + "'ror2' TEXT(100),"  + "'targetprice' TEXT(100));";
			
			createSqls[9] = "CREATE TABLE 'pricerate' ('id'  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL," 
					+ "'pkgId' TEXT(100)," + "'pkgName' TEXT(100),"  + "'round' TEXT(100)," 
					+ "'attachment' TEXT(100)," + "'code' TEXT(100),"  + "'productName' TEXT(100),"
					+ "'pid' TEXT(100)," + "'orderid' TEXT(100),"  + "'company' TEXT(100),"
					+ "'unit' TEXT(100)," + "'extendedDesc' TEXT(100),"  + "'limitPrice' TEXT(100),"
				    + "'rowLimitPrice' TEXT(100)," + "'count' TEXT(100),"  + "'fid' TEXT(100),"
				    + "'funit' TEXT(100)," + "'rate' TEXT(100),"  + "'fLimitPrice' TEXT(100),"
				    + "'fprice' TEXT(100)," + "'bprice' TEXT(100),"  + "'taxrate' TEXT(100),"
				    + "'fWithPrice' TEXT(100)," + "'bWithPrice' TEXT(100),"  + "'bTotalPrice' TEXT(100)," 
				    + "'bTotalWithPrice' TEXT(100)," 
				    + "'floatRate' TEXT(100)," 
				    + "'valid' TEXT(100)," + "'avg' TEXT(100)," + "'pavg' TEXT(100)," 
				    + "'pvalid' TEXT(100));";

			for (String createSql : createSqls) {
				sqliteHelper.executeUpdate(createSql);
			}
    
            monitor.worked(10);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
	}
}
