package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;

import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.Product;

public class MonitorHanderTest {

	@Test
	public void testParse() {

		Map<String, CostPrice> priceMap = new HashMap<>();
		List<Product> productlist = new ArrayList<>();
		// String file = "E://9906-500008896-00078--常规110kV监控.docx";
		// Integer rowId = 0;
		// MonitorHander monitorHander = new MonitorHander(priceMap);
		// monitorHander.parse(file, productlist, rowId);
		// for(Product product: productlist){
		// System.out.println(product);
		// }

		// String file = "E://3117-500008897-00044--常规35kV监控.docx";
		Integer rowId = 0;
		// String file =
		// "E://9906-500113832-00271陈埠110kV变电站新建工程智能变电站监控系统,AC110kV.docx";
		String file = "E://9906-500114769-00020蚌埠凤阳县景泰35kV输变电工程智能变电站监控系统,AC35kV.docx";

		Monitor2Hander monitor2Hander = new Monitor2Hander(priceMap);
		monitor2Hander.parse(file, productlist, rowId);
		for (Product product : productlist) {
			System.out.println(product);
		}
	}

}
