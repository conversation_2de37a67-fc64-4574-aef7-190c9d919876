package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.model.DevPrice;

/**
 * @Description:限价处理类
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-23 上午11:18:45
 
 */
@SuppressWarnings("rawtypes")
public class DevPriceHandler extends SheetsHandler {

    private String bidNo;
    private DevPrice devPrice;
    private List<DevPrice> devPriceList;
    private Map<String, CostPrice> priceMap = new HashMap<>();

    public DevPriceHandler(String bidNo, Map<String, CostPrice> priceMap) {
        this.bidNo = bidNo;
        devPriceList = new ArrayList<>();
        devPrice = new DevPrice();
        this.priceMap = priceMap;
    }

    @Override
    public void cell(String cellReference, String formattedValue, XSSFComment comment) {
        super.cell(cellReference, formattedValue, comment);
        if (currentRow > 2 && !isEmpty(formattedValue)) {

            if (currentCol == 0) {
                devPrice.setNumber(formattedValue);
            } else if (currentCol == 1) {
                devPrice.setDevname(formattedValue);
            } else if (currentCol == 2) {
                devPrice.setDevtype(formattedValue);
            } else if (currentCol == 3) {
                devPrice.setUnit(formattedValue);
            } else if (currentCol == 4) {
                devPrice.setCount(formattedValue);
            } else if (currentCol == 5) {
                devPrice.setOdevtype(formattedValue);
            } else if (currentCol == 6) {
                devPrice.setOcunnt(formattedValue);
            } else if (currentCol == 7) {
                devPrice.setSupply(formattedValue);
            } else if (currentCol == 8) {
                devPrice.setArea(formattedValue);
            } else if (currentCol == 9) {
                devPrice.setQuote(formattedValue);
            } else if (currentCol == 10) {
                devPrice.setSearchType(formattedValue);
                CostPrice cst = priceMap.get(formattedValue);
                if (cst != null) {
                    devPrice.setLnType(String.valueOf(cst.getType()));
                    devPrice.setCostprice(cst.getCostPrice());
                    devPrice.setSupply(cst.getSupply());
                    devPrice.setPrice(cst.getPrice());
                } else {
                    devPrice.setLnType("0");
                    devPrice.setCostprice("");
                    devPrice.setPrice("");
                }
            }
        }
    }

    @Override
    public void endRow(int rowNum) {
        devPrice.setBidno(bidNo);
        devPriceList.add(devPrice);
    }

    public DevPrice getDevPrice() {
        return devPrice;
    }

    public List<DevPrice> getDevPriceList() {
        return devPriceList;
    }

    public void setDevPrice(DevPrice devPrice) {
        this.devPrice = devPrice;
    }

    public void setDevPriceList(List<DevPrice> devPriceList) {
        this.devPriceList = devPriceList;
    }

    @Override
    public void startRow(int rowNum) {
        super.startRow(rowNum);
        this.devPrice = new DevPrice();
    }

}
