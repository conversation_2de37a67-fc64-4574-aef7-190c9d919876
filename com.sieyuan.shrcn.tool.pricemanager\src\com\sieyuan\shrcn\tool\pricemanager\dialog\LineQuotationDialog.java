package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.file.excel.Xls2007Parser;
import com.shrcn.found.file.util.FileManipulate;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.u21.table.Table;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PriceRateDao;
import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;
import com.sieyuan.shrcn.tool.pricemanager.model.FileItem;
import com.sieyuan.shrcn.tool.pricemanager.model.PriceRate;
import com.sieyuan.shrcn.tool.pricemanager.sax.PriceRateHandler;
import com.sieyuan.shrcn.tool.pricemanager.utils.ExcelExportUtil;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.utils.LinePriceResolver;
import com.sieyuan.shrcn.tool.pricemanager.views.table.FileTableMode;

/**
 * 辅参计算工具
 * 
 * <AUTHOR>
 * @version 1.0, 2020-6-13
 */
public class LineQuotationDialog extends WrappedDialog {

	private UIPreferences perference = UIPreferences.newInstance();

	// 物料清单
	private FileTableMode fileTableMode;
	private Table table;

	public LineQuotationDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		container.setLayout(new GridLayout(3, false));

		table = new Table(container, SWT.BORDER | SWT.V_SCROLL | SWT.H_SCROLL);
		table.setLayout(new GridLayout(1, false));

		GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 6, 10);
		gd_table.widthHint = 650;
		gd_table.heightHint = 280;
		table.setLayoutData(gd_table);
		fileTableMode = new FileTableMode();
		table.setModel(fileTableMode);

		return container;
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == OK) {
			generateLineQuotationFile();
			return;
		} else if (buttonId == IDialogConstants.ABORT_ID) {
			addFiles();
			return;
		} else if (buttonId == IDialogConstants.BACK_ID) {
			removeFiles();
			return;
		} else if (buttonId == IDialogConstants.CLIENT_ID) {
			exportFile();
			return;
		} else if (buttonId == IDialogConstants.FINISH_ID) {
            ProductOutputDialog expDlg = new ProductOutputDialog(Display.getDefault().getActiveShell(), 3, "");
            expDlg.open();
			return;
		}
		super.buttonPressed(buttonId);
	}

	private void exportFile() {
		String diretoryPath = DirManager.getOutputDir(GlobalData.getInstance().getProjectName()) + File.separator + "辅参计算表";
		FileManipulate.initDir(diretoryPath);
		final String path = DialogHelper.selectDirectory(this.getShell(), diretoryPath);
		if (path != null && !"".equals(path)) {
			List<PriceRate> priceRateList = PriceRateDao.getPriceRates();
			Map<String, LinkedList<PriceRate>> pkgRateMap = new HashMap<String, LinkedList<PriceRate>>();
			for (PriceRate priceRate : priceRateList) {
				String pkgName = priceRate.getPkgName();
				LinkedList<PriceRate> rates = new LinkedList<>();
				if (pkgRateMap.containsKey(pkgName)) {
					rates = (LinkedList<PriceRate>) pkgRateMap.get(pkgName);
				}
				rates.add(priceRate);
				pkgRateMap.put(pkgName, rates);
			}
			for (Map.Entry<String, LinkedList<PriceRate>> entry : pkgRateMap.entrySet()) {
				String pkgName = entry.getKey();
				if (StringUtils.isEmpty(pkgName)) {
					continue;
				}
				String fileName = path + File.separator + pkgName + ".xlsx";
				ExcelExportUtil.exportPriceRate(fileName, entry.getValue(), ToolConstants.PRICERATE);
			}
		}
		DialogHelper.showAsynInformation("导出辅参计算表成功");
		ConsoleManager.getInstance().append("导出辅参计算表" + path + "成功");
	}

	@SuppressWarnings("unchecked")
	private void removeFiles() {
		List<FileItem> itemsList = new ArrayList<>();
		List<FileItem> items = fileTableMode.getItems();
		for (FileItem fileItem : items) {
			if (!fileItem.isChecked()) {
				itemsList.add(fileItem);
			}
		}
		fileTableMode.setItems(itemsList);
		table.redraw();
	}

	@SuppressWarnings("unchecked")
	private void addFiles() {
		FileDialog dialog = new FileDialog(this.getShell(), SWT.OPEN | SWT.MULTI);
		dialog.setFilterExtensions(new String[] { "*.xlsx" });
		@SuppressWarnings("unused")
		String fileName = dialog.open();// 返回最后一个选择文件的全路径
		String[] fileNames = dialog.getFileNames();// 返回所有选择的文件名，不包括路径
		String path = dialog.getFilterPath();// 返回选择的路径，这个和fileNames配合可以得到所有的文件的全路径
		List<FileItem> items = fileTableMode.getItems();
		FileItem item = new FileItem();
		for (String fileItem : fileNames) {
			item = new FileItem();
			item.setChecked(true);
			item.setStatus("");
			item.setFileName(fileItem);
			item.setFileType(fileItem.substring(fileItem.lastIndexOf(".") + 1));
			File file = new File(path + File.separator + fileItem);
			item.setAbsFileName(file.getAbsolutePath());
			item.setFileSize(FieldUtils.getFileSize(file));
			items.add(item);
		}
		fileTableMode.setItems(items);
		table.redraw();
	}

	@SuppressWarnings("unchecked")
	private void generateLineQuotationFile() {
		final List<FileItem> fileItems = fileTableMode.getItems();
		String prj = perference.getInfo("com.shrcn.pricemangertool.curentprojectname");
		if (StringUtils.isEmpty(prj)) {
			DialogHelper.showAsynWarning("请先打开工程，再执行计算！");
			return;
		}

		ProgressManager.execute(new IRunnableWithProgress() {
			@Override
			public void run(final IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {

				monitor.beginTask("正在解析辅参计算表...", fileItems.size() * 30 + fileItems.size());

				List<PriceRate> allPriceRateList = new ArrayList<>();

				for (final FileItem fileItem : fileItems) {
					String templatePath = fileItem.getAbsFileName();
					updateTable(fileItem, "正在处理中...");
					try {
						OPCPackage xlsxPackage = OPCPackage.open(templatePath, PackageAccess.READ);
						ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
						XSSFReader xssfReader = new XSSFReader(xlsxPackage);
						StylesTable styles = xssfReader.getStylesTable();
						XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
						while (iter.hasNext()) {
							InputStream stream = iter.next();
							PriceRateHandler priceHandler = new PriceRateHandler();
							Xls2007Parser.processSheet(styles, strings, priceHandler, stream);
							List<PriceRate> priceRatePrices = priceHandler.getPriceRateList();
							allPriceRateList.addAll(priceRatePrices);
							stream.close();
						}
						xlsxPackage.close();
					} catch (Throwable e) {
						SCTLogger.error(e.getMessage());
					}

					updateTable(fileItem, "文件处理完毕");
					monitor.worked(30);
				}

				boolean result = new LinePriceResolver().adjustPrices(allPriceRateList);
				monitor.done();
				if (result) {
					Display.getDefault().syncExec(new Runnable() {
						public void run() {
							DialogHelper.showAsynInformation("辅参计算表计算完成!");
						}
					});
				} else {
					Display.getDefault().syncExec(new Runnable() {
						public void run() {
							DialogHelper.showAsynError("计算出错，请检查！");
						}
					});
				}
			}
		});
	}

	/**
	 * 更新表格内容
	 * 
	 * @param fileItem
	 * @param status
	 */
	private void updateTable(final FileItem fileItem, final String status) {
		Display.getDefault().asyncExec(new Runnable() {
			@Override
			public void run() {
				fileItem.setStatus(status);
				table.redraw();
			}
		});
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("辅参计算工具");
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, "计算", true);
		createButton(parent, IDialogConstants.ABORT_ID, "添加文件", false);
		createButton(parent, IDialogConstants.BACK_ID, "移除文件", false);
		createButton(parent, IDialogConstants.CLIENT_ID, "导出辅参计算表", false);
		createButton(parent, IDialogConstants.FINISH_ID, "导出固化ID开标", false);
		createButton(parent, IDialogConstants.CANCEL_ID, "关闭", false);
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(860, 480);
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
