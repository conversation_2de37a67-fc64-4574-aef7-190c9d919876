package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.dialog.ProductMergeDialog;

/**
 * 开标文件合并工具
 * 
 * <AUTHOR>
 * @version 1.0, 2020-4-21
 * 
 */
public class ProductMergeAction extends MenuAction {

	public ProductMergeAction(String text) {
		super(text);
		setAccelerator(SWT.CTRL + 'U');
	}

	@Override
	public void run() {
		ProductMergeDialog expDlg = new ProductMergeDialog(Display
				.getDefault().getActiveShell());
		expDlg.open();
	}

}
