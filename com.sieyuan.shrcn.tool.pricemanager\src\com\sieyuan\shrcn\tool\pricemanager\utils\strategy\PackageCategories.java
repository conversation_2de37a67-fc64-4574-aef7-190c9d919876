package com.sieyuan.shrcn.tool.pricemanager.utils.strategy;

import java.util.List;

import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;

/**
 * 包分类结果类
 * 封装包分类的结果
 */
public class PackageCategories {
    
    private final List<PkgAdjust> priorityPackages;
    private final List<PkgAdjust> nonPriorityPackages;
    
    public PackageCategories(List<PkgAdjust> priorityPackages, List<PkgAdjust> nonPriorityPackages) {
        this.priorityPackages = priorityPackages;
        this.nonPriorityPackages = nonPriorityPackages;
    }
    
    public List<PkgAdjust> getPriorityPackages() {
        return priorityPackages;
    }
    
    public List<PkgAdjust> getNonPriorityPackages() {
        return nonPriorityPackages;
    }
} 