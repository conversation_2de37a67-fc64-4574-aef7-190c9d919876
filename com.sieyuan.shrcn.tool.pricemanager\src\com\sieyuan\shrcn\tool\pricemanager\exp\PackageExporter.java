/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.exp;

import java.io.File;
import java.util.List;
import java.util.Map;

import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;

/**
* 
* <AUTHOR>
* @version 1.0, 2017-12-16
*/
public class PackageExporter {

    /**
     * 生成带所有包的表
     * @param name 
     * @param monitor
     * @param totals
     * @param expInfos
     * @param pkgNameMaps 
     * @param price 
     */
    public static void exportPkgs(String path, String pkgName, String tpl, Map<String, List<PkgInfo>> pkgNameMaps,
        String price) {
        String suffix = (tpl == null) ? "" : "-" + tpl;
        String pkgpath = path + File.separator + pkgName + File.separator + pkgName + suffix + ".xlsx";
        PkgTotalExport.export(pkgpath, pkgName, (tpl != null), pkgNameMaps, price);// 新版导出方法(极速)
    }

}
