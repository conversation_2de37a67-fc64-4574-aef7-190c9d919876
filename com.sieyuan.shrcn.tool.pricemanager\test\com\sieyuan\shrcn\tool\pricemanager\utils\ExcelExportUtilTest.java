package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.junit.Test;

import com.alibaba.fastjson.JSONArray;
import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.file.excel.Xls2007Parser;
import com.sieyuan.shrcn.tool.pricemanager.model.PriceRate;
import com.sieyuan.shrcn.tool.pricemanager.sax.PriceRateHandler;

public class ExcelExportUtilTest {

    @SuppressWarnings("rawtypes")
    @Test
    public void test() {
        /**
        * 模拟100W条数据,存入JsonArray,此处使用fastJson(号称第一快json解析)快速解析大数据量数据
        * 至于容量问题,Java数组的length必须是非负的int，所以它的理论最大值就是java.lang.Integer.MAX_VALUE = 2^31-1 = 2147483647。
        * 由于xlsx最大支持行数为1048576行,此处模拟了1048573调数据,剩下的3条占用留给自定义的excel的头信息和列项.
        */
        // int count = 100000;
        // int count = 1000000;
        int count = 3000;
        JSONArray studentArray = new JSONArray();
        for (int i = 0; i < count; i++) {
            // s.setName("POI-" + i);
            // s.setAge(i);
            // s.setBirthday(new Date());
            // s.setHeight(i);
            // s.setWeight(i);
            // s.setSex(i % 2 == 0 ? false : true);
            // studentArray.add(s);
        }

        /*
        * titleList存放了2个元素,分别为titleMap和headMap
        */
        ArrayList<LinkedHashMap> titleList = new ArrayList<LinkedHashMap>();
        // 1.titleMap存放了该excel的头信息
        LinkedHashMap<String, String> titleMap = new LinkedHashMap<String, String>();
        titleMap.put("title1", "POI导出大数据量Excel Demo");
        titleMap.put("title2", "https://github.com/550690513");
        // 2.headMap存放了该excel的列项
        LinkedHashMap<String, String> headMap = new LinkedHashMap<String, String>();
        headMap.put("name", "姓名");
        headMap.put("age", "年龄");
        headMap.put("birthday", "生日");
        headMap.put("height", "身高");
        headMap.put("weight", "体重");
        headMap.put("sex", "性别");
        headMap.put("sex", "性别");

        titleList.add(titleMap);
        titleList.add(headMap);

        File file = new File("D://ExcelExportDemo/");
        if (!file.exists())
            file.mkdirs();// 创建该文件夹目录
        OutputStream os = null;
        try {
            System.out.println("正在导出xlsx...");
            long start = System.currentTimeMillis();
            // .xlsx格式
            os = new FileOutputStream(file.getAbsolutePath() + File.separator + start + ".xlsx");
            SXSSFWorkbookUtil.exportExcel(titleList, studentArray, os, false, true);
            System.out.println("导出完成...共" + count + "条数据,用时" + (System.currentTimeMillis() - start) + "毫秒");
            System.out.println("文件路径：" + file.getAbsolutePath() + File.separator + start + ".xlsx");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //os.close();
        }
    }
    
    
    @Test
	public void testExport() {
		String pricepath = "D://包1.xlsx";
		List<PriceRate> costtPrices = new ArrayList<>();
		try {
			OPCPackage xlsxPackage = OPCPackage.open(pricepath, PackageAccess.READ);
			ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(xlsxPackage);
			XSSFReader xssfReader = new XSSFReader(xlsxPackage);
			StylesTable styles = xssfReader.getStylesTable();
			XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
			while (iter.hasNext()) {
				InputStream stream = iter.next();
				PriceRateHandler priceHandler = new PriceRateHandler();
				Xls2007Parser.processSheet(styles, strings, priceHandler, stream);
				costtPrices = priceHandler.getPriceRateList();
				System.out.println(costtPrices.size());
				stream.close();
			}
			xlsxPackage.close();
		} catch (Throwable e) {
			SCTLogger.error(e.getMessage());
		}
		ExcelExportUtil.exportPriceRate("D://123.xlsx", costtPrices, "报价方式-单价");
	}

}
