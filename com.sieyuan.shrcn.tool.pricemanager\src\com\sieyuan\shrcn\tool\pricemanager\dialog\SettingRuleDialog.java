package com.sieyuan.shrcn.tool.pricemanager.dialog;

import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Shell;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.app.WrappedTitleAreaDialog;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;

/**
 * 设置超限分配规则
 * 
 * <AUTHOR>
 * @version 1.0, 2022-11-17
 */
public class SettingRuleDialog extends WrappedTitleAreaDialog {

	private UIPreferences perference = UIPreferences.newInstance();
	private String INPUTCOUNT = "inputCount";

	private Text inputCountText;

	public SettingRuleDialog(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == IDialogConstants.OK_ID) {
			return;
		}
		super.buttonPressed(buttonId);
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("设置超限规则");
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(600, 250);
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, IDialogConstants.OK_LABEL, true);
		createButton(parent, IDialogConstants.CANCEL_ID, IDialogConstants.CANCEL_LABEL, false);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		setTitle("设置超限规则");
		setMessage("设置超出限价后分配规则，将超出限价部分分配至数量大于配置值的ID内。");
		Composite container = new Composite(parent, SWT.FILL);
		container.setLayout(new GridLayout(3, false));
		container.setLayoutData(new GridData(GridData.FILL_BOTH));

		inputCountText = SwtUtil.createLabelText(container, "设置超限分配数量 >", new GridData(GridData.FILL_HORIZONTAL));

		initData();
		return container;
	}

	private void initData() {
		String infopath = SettingRuleDialog.class.getName();

		String count = perference.getInfo(infopath + INPUTCOUNT);
		if (StringUtil.isEmpty(count)) {
			count = "1";
		}
		inputCountText.setText(count);
	}

}
