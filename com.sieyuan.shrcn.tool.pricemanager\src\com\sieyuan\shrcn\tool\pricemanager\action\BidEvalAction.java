package com.sieyuan.shrcn.tool.pricemanager.action;

import org.eclipse.swt.widgets.Display;

import com.shrcn.found.ui.action.MenuAction;
import com.sieyuan.shrcn.tool.pricemanager.dialog.BidEvalDialog;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;

/**
 * 开标测算工具
 * <AUTHOR>
 */
public class BidEvalAction extends MenuAction {

	public BidEvalAction(String text) {
		super(text);
	}
	
    @Override
    public void run() {
    	BidEvalDialog dlg = new BidEvalDialog(Display.getDefault().getActiveShell());
    	dlg.open();
    }

}
