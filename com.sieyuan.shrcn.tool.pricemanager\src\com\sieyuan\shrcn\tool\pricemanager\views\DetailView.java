package com.sieyuan.shrcn.tool.pricemanager.views;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.CTabFolder;
import org.eclipse.swt.custom.CTabItem;
import org.eclipse.swt.graphics.Color;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.TreeItem;

import com.sieyuan.shrcn.tool.pricemanager.composite.PkgInfoComposite;
import com.sieyuan.shrcn.tool.pricemanager.composite.ProductComposite;
import com.sieyuan.shrcn.tool.pricemanager.composite.WelcomeComposite;
import com.sieyuan.shrcn.tool.pricemanager.model.TreeData;

/**
 * 
 * @Description:TODO
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON><PERSON><PERSON>
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-5-14 下午1:41:48
 
 */
public class DetailView implements Cloneable {

    public static Color bgcolor1;
    public static Color bgcolor2;

    public static Color bgcolor3;
    public static Color bgcolor4;
    public static Color bgcolor5;
    private static Composite composite;
    private static CTabFolder ctabFolder;

    private static PkgInfoComposite packageDataComposite;
    private static ProductComposite productInfoComposite;
    private static WelcomeComposite welcomeComposite;

    // private static TreeItem currentTreeItem;

    /***
     * 界面切换
     * @param type 
     * 
     * @param compositeName
     * @param currentTreeItem
     */
    public static void changeComposite(final TreeData treeData, final TreeItem treeItem) {
        Display.getDefault().syncExec(new Runnable() {
            public void run() {
                Object object = treeItem.getData();
                if (object == null) {
                    return;
                }
                if (treeData.getType() == 0) {
                    setWelcomeCompositeData(treeData);
                } else if (treeData.getType() == 7) {
                    setProductDataCompositeData(treeData);
                } else {
                    setPackageDataCompositeData(treeData);
                }
            }
        });
    }

    private static void disposeCurrentComposite() {
        if (packageDataComposite != null) {
            packageDataComposite.dispose();
        }
        if (productInfoComposite != null) {
            productInfoComposite.dispose();
        }
        CTabItem[] temp = ctabFolder.getItems();
        for (CTabItem cTabItem : temp) {
            cTabItem.dispose();
        }
    }

    private static void setPackageDataCompositeData(TreeData treeData) {
        disposeCurrentComposite();
        packageDataComposite = new PkgInfoComposite(ctabFolder, treeData, SWT.NONE);
        packageDataComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
        CTabItem tbtmNewItem = new CTabItem(ctabFolder, SWT.NONE);
        tbtmNewItem.setText("New Item");
        tbtmNewItem.setControl(packageDataComposite);
        ctabFolder.setFocus();

    }

    private static void setProductDataCompositeData(TreeData treeData) {
        disposeCurrentComposite();
        productInfoComposite = new ProductComposite(ctabFolder, treeData, SWT.NONE);
        productInfoComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
        CTabItem tbtmNewItem = new CTabItem(ctabFolder, SWT.NONE);
        tbtmNewItem.setText("New Item");
        tbtmNewItem.setControl(productInfoComposite);
        ctabFolder.setFocus();

    }

    private static void setWelcomeCompositeData(TreeData treeData) {
        disposeCurrentComposite();
        welcomeComposite = new WelcomeComposite(ctabFolder, treeData, SWT.NONE);
        welcomeComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
        CTabItem tbtmNewItem = new CTabItem(ctabFolder, SWT.NONE);
        tbtmNewItem.setText("New Item");
        tbtmNewItem.setControl(welcomeComposite);
        ctabFolder.setFocus();

    }

    /**
     * 构造函数
     * 
     * @param parent Composite
     */
    public DetailView(Composite parent) {

        bgcolor1 = new Color(parent.getDisplay(), 227, 239, 255);
        bgcolor2 = new Color(parent.getDisplay(), 255, 255, 255);
        bgcolor3 = new Color(parent.getDisplay(), 205, 205, 205);
        bgcolor4 = new Color(parent.getDisplay(), 225, 0, 0);
        bgcolor5 = new Color(parent.getDisplay(), 0, 0, 0);

        composite = new Composite(parent, SWT.NONE);
        composite.setLayout(new GridLayout(1, false));
        ctabFolder = new CTabFolder(composite, SWT.NONE);
        ctabFolder.setTabHeight(0);
        ctabFolder.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true, 1, 1));
        ctabFolder.setFocus();

        TreeData data = new TreeData();
        data.setType(0);
        setWelcomeCompositeData(data);
    }
}
