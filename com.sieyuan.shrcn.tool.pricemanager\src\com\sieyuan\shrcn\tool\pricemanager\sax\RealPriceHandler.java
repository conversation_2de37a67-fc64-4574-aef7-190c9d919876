package com.sieyuan.shrcn.tool.pricemanager.sax;

import java.util.HashMap;
import java.util.Map;

import org.apache.poi.xssf.usermodel.XSSFComment;

import com.shrcn.found.file.excel.SheetsHandler;

/**
 * 参考价读取逻辑
 * 
 * <AUTHOR>
 * @version 1.0,2023-4-12
 * 
 */
@SuppressWarnings("rawtypes")
public class RealPriceHandler extends SheetsHandler {

	private Map<String, String> realPrcies;
	private String applyId;
	private String realPrice;

	@SuppressWarnings("unchecked")
	public RealPriceHandler() {
		realPrcies = new HashMap();
	}

	@Override
	public void cell(String cellReference, String formattedValue, XSSFComment comment) {
		super.cell(cellReference, formattedValue, comment);
		if (currentRow > 0 && !isEmpty(formattedValue)) {
			if (currentCol == 4) { // 外部价格
				applyId = formattedValue;
			} else if (currentCol == 5) { // 外部价格
				// applyId = formattedValue;
			} else if (currentCol == 6) {
				realPrice = formattedValue;
			}
		}
	}

	@Override
	public void endRow(int rowNum) {
		realPrcies.put(applyId, realPrice);
	}

	public Map<String, String> getRealPrcies() {
		return realPrcies;
	}

	public void setRealPrcies(Map<String, String> realPrcies) {
		this.realPrcies = realPrcies;
	}

	@Override
	public void startRow(int rowNum) {
		super.startRow(rowNum);
	}

}
