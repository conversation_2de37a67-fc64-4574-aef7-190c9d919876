package com.sieyuan.shrcn.tool.pricemanager.views.table;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.sieyuan.shr.u21.ui.table.TableModel;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;
import com.sieyuan.shrcn.tool.pricemanager.views.DetailView;

import de.kupzog.ktable.KTableCellEditor;
import de.kupzog.ktable.KTableCellRenderer;
import de.kupzog.ktable.SWTX;
import de.kupzog.ktable.editors.KTableCellEditorCombo;
import de.kupzog.ktable.editors.KTableCellEditorText;

public class ProductInfoTableMode extends TableModel {
    private List<Integer> allZero;

    /**
     * Initialize the base implementation.
     */
    public ProductInfoTableMode() {
        super();
        allZero = new ArrayList<>();
    }

    /**
     * 设置表单元格编辑器
     * 
     * @return
     */
    public KTableCellEditor doGetCellEditor(int col, int row) {
        if (col == 9) {
            KTableCellEditorCombo comboEditor = new KTableCellEditorCombo();
            comboEditor.setItems(new String[] {"是", "否"});
            return comboEditor;
        } else if (col == 5 || col == 6 || col == 7) {
            return new KTableCellEditorText();
        }
        return null;
    }

    public KTableCellRenderer doGetCellRenderer(int col, int row) {
        m_fixedRenderer.setAlignment(SWTX.ALIGN_HORIZONTAL_CENTER | SWTX.ALIGN_VERTICAL_CENTER);
        m_textRenderer.setAlignment(SWTX.ALIGN_HORIZONTAL_CENTER | SWTX.ALIGN_VERTICAL_CENTER | SWTX.WRAP);

        if (allZero.contains(row) && col == 12) {
            m_checkableRenderer.setDefaultBackground(DetailView.bgcolor1);
            m_textRenderer.setDefaultBackground(DetailView.bgcolor4);
            m_textRenderer.setDefaultForeground(DetailView.bgcolor2);
        } else {
            m_checkableRenderer.setDefaultBackground(DetailView.bgcolor2);
            m_textRenderer.setDefaultBackground(DetailView.bgcolor2);
            m_textRenderer.setDefaultForeground(DetailView.bgcolor5);
        }
        return isFixedCell(col, row) ? m_fixedRenderer : m_textRenderer;
    }

    /**
     * 取得表格列个数
     */
    public int doGetColumnCount() {
        return getHead().length;
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.shrcn.vdd.ui.table.model.TableModel#doGetContentAt(int, int)
     */
    public Object doGetContentAt(int col, int row) {
        // 第一行是标题行
        if (row == 0) {
            return getHead()[col];
        }
        PkgProduct item = (PkgProduct)items.get(row - 1);
        switch (col) {
            case 0:
                return StringUtils.trimToEmpty(item.getNumber());
            case 1:
                return StringUtils.trimToEmpty(item.getDevname());
            case 2:
                return StringUtils.trimToEmpty(item.getDevtype());
            case 3:
                return StringUtils.trimToEmpty(item.getUnit());
            case 4:
                return StringUtils.trimToEmpty(item.getCount());
            case 5:
                return StringUtils.trimToEmpty(item.getOdevtype());
            case 6:
                return StringUtils.trimToEmpty(item.getOcunnt());
            case 7:
                return StringUtils.trimToEmpty(item.getSupply());
            case 8:
                return StringUtils.trimToEmpty(item.getArea());
            case 9:
                return StringUtils.trimToEmpty(item.getQuote());
            case 10:
                return StringUtils.trimToEmpty(item.getSearchdevtype());
            case 11:
                return item.getLnType() == 0 ? ToolConstants.PRICE_UNKOWN : (item.getLnType() == 1
                    ? ToolConstants.PRICE_IN : ToolConstants.PRICE_OUT);
            case 12:
                return StringUtils.trimToEmpty(item.getCostprice());
            case 13:
                return StringUtils.trimToEmpty(item.getPrice());
            case 14:
                return StringUtils.trimToEmpty(item.getWeight());
            default:
                return "";
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.shrcn.vdd.ui.table.model.TableModel#doSetContentAt(int, int,
     * java.lang.Object)
     */
    public void doSetContentAt(int col, int row, Object value) {
        if (row - 1 >= 0) {
            PkgProduct item = (PkgProduct)items.get(row - 1);
            if (value == null) {
                value = "";
            }
            switch (col) {
                case 1:
                    item.setDevname((String)value);
                    break;
                case 2:
                    item.setDevtype((String)value);
                    break;
                case 3:
                    item.setUnit((String)value);
                    break;
                case 4:
                    item.setCount((String)value);
                    break;
                case 5:
                    item.setOdevtype((String)value);
                    break;
                case 6:
                    item.setOcunnt((String)value);
                    break;
                case 7:
                    item.setSupply((String)value);
                    break;
                case 8:
                    item.setArea((String)value);
                    break;
                case 9:
                    item.setQuote((String)value);
                    break;
                case 10:
                    item.setSearchdevtype((String)value);
                    break;
                case 11:
                    item.setSearchdevtype((String)value);
                    break;
                case 12:
                    item.setCostprice((String)value);
                    break;
                case 13:
                    item.setPrice((String)value);
                    break;
                case 14:
                    item.setWeight((String)value);
                    break;
            }
        }
    }

    public List<Integer> getAllZero() {
        return allZero;
    }

    public String[] getHead() {
        return ITableMessage.PRODUCT_DATA_HEAD;
    }

    public int[] getHeadWidth() {
        return ITableMessage.PRODUCT_DATA_WIDTH;
    }

    /**
     * 得到初始化行宽度
     */
    public int getInitialColumnWidth(int col) {
        return (col < getHeadWidth().length) ? getHeadWidth()[col] : super.getInitialColumnWidth(col);
    }

    public void setAllZero(List<Integer> allZero) {
        this.allZero = allZero;
    }

}
