<?xml version="1.0" encoding="UTF-8"?>
<table name="bidResult" desc="测算信息" class="com.sieyuan.shrcn.tool.pricemanager/com.sieyuan.shrcn.tool.pricemanager.model.BidEvaluationResult" 
tableClass="com.shrcn.found.ui/com.shrcn.found.ui.table.RKTable" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
  <field fixed="true" desc="序号" editor="none" name="index" width="40" default=""/>  
  <field desc="公司全称" editor="none" name="companyName" width="160" default=""/>
  <field desc="报价" editor="none" name="bid" width="100" default=""/> 
  <field desc="价格得分" editor="none" name="score" width="100" default=""/> 
  <field desc="价格排名" editor="none" name="rank" width="100" default=""/> 
  <field desc="价格分差" editor="none" name="priceDiff" width="100" default=""/>
  <field desc="总分差" editor="none" name="totalDiff" width="100" default=""/>
  <field desc="技术补分" editor="none" name="techBonus" width="100" default=""/>
</table>