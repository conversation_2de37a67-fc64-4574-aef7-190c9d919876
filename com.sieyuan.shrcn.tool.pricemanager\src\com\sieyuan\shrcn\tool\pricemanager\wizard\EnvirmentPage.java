package com.sieyuan.shrcn.tool.pricemanager.wizard;

import org.eclipse.jface.wizard.WizardPage;
import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.events.SelectionListener;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Button;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Text;

import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.dir.DirManager;

/**
 * @Description: 环境设置
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-6-26 下午5:58:06
 
 */
public class EnvirmentPage extends WizardPage {

    private Text id; // 招标编号

    private String ID = ".id";
    private String idTxt;
    private String LIMIT = ".limitRoot";
    private String limitPath;
    private UIPreferences perference = UIPreferences.newInstance();

    private String PKG = ".pkgRoot";
    private String pkgPath;
    private String PRICE = ".priceRoot";
    private String pricePath;
    private String TAXRATE = ".taxrate";

    private String taxTxt;
    private Text txtLimit;// 限价表
    private Text txtPkg; // 包数据表
    private Text txtPrice;// 成本价
    private Text txtTaxrate;// 税率

    // 必须继承父类的构造函数
    protected EnvirmentPage(String pageName) {
        super(pageName);
    }

	// 改写自父类的方法，在此方法中构建页面上的界面组件。注意不要在传入参数parent基础直接创建界面元素，而应在一个新面板topComp上创建
    public void createControl(Composite parent) {
        // 每页的提示信息
        setTitle("环境信息");
        setMessage("设置环境信息", INFORMATION);

        Composite topComp = new Composite(parent, SWT.NULL);
        topComp.setLayout(new GridLayout(3, false));
        GridData gd = new GridData(SWT.LEFT, SWT.LEFT, false, false, 2, 1);
        gd.widthHint = 300;

        id = SwtUtil.createLabelText(topComp, "招标编号：", gd);
        txtTaxrate = SwtUtil.createLabelText(topComp, "税率：", gd);
        txtPkg = SwtUtil.createFileSelector(topComp, "物料清单信息表(Excel)：", "*.xlsx");
        txtLimit = SwtUtil.createFileSelector(topComp, "限价信息表(Excel)：", "*.xlsx");
        txtPrice = SwtUtil.createFileSelector(topComp, "价格库信息表(Excel)：", "*.xlsx");

        id.setEnabled(false);
        txtTaxrate.setEnabled(false);
        txtPkg.setEnabled(false);
        txtLimit.setEnabled(false);
        txtPrice.setEnabled(false);

        final Button selectButton =
            SwtUtil.createButton(topComp, new GridData(SWT.LEFT, SWT.LEFT, false, false, 1, 1), SWT.CHECK, "使用默认的环境配置");
        selectButton.setSelection(true);
        selectButton.addSelectionListener(new SelectionListener() {

            @Override
            public void widgetDefaultSelected(SelectionEvent e) {

            }

            @Override
            public void widgetSelected(SelectionEvent e) {
                if (selectButton.getSelection()) {
                    id.setEnabled(false);
                    txtTaxrate.setEnabled(false);
                    txtPkg.setEnabled(false);
                    txtLimit.setEnabled(false);
                    txtPrice.setEnabled(false);
                } else {
                    id.setEnabled(true);
                    txtTaxrate.setEnabled(true);
                    txtPkg.setEnabled(true);
                    txtLimit.setEnabled(true);
                    txtPrice.setEnabled(true);
                }
            }
        });

        init();
        // 必须要的一行
        setControl(topComp);
    }

    public String getIdText() {
        return id.getText();
    }

    public String getLimitPath() {
        return txtLimit.getText();
    }

    public String getPkgPath() {
        return txtPkg.getText();
    }

    public String getPricePath() {
        return txtPrice.getText();
    }

    public String getTaxText() {
        return txtTaxrate.getText();
    }

    private void init() {
        String infopath = EnvirmentSettingDialog.class.getName();

        idTxt = perference.getInfo(infopath + ID);
        if (idTxt.equals("")) {
            idTxt = "10000";
        }
        id.setText(idTxt);

        taxTxt = perference.getInfo(infopath + TAXRATE);
        if (taxTxt.equals("")) {
            taxTxt = "1.3";
        }
        txtTaxrate.setText(taxTxt);

        pkgPath = perference.getInfo(infopath + PKG);
        if (pkgPath.equals("")) {
            pkgPath = DirManager.getToolPkgFile();
        }
        txtPkg.setText(pkgPath);

        limitPath = perference.getInfo(infopath + LIMIT);
        if (limitPath.equals("")) {
            limitPath = DirManager.getToolLimitFile();
        }
        txtLimit.setText(limitPath);

        pricePath = perference.getInfo(infopath + PRICE);
        if (pricePath.equals("")) {
            pricePath = DirManager.getToolPriceFile();
        }
        txtPrice.setText(pricePath);
    }

}