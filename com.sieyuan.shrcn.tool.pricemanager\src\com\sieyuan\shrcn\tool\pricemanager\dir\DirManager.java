package com.sieyuan.shrcn.tool.pricemanager.dir;

import java.io.File;
import java.io.IOException;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;

import com.shrcn.found.file.xml.XMLFileManager;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.utils.TimeUtils;

/**
 * @Description:DirManager路径管理
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company Sieyuan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2019-4-23 上午10:52:44
 */
public class DirManager {

    public static final String cfg = "cfg";
    public static final String dbfileer = "data";
    public static final String history = "history";
    public static final String input = "input";
    public static final String log = "logs";
    public static final String output = "output";
    /** 目录：workspace. */
    public static final String workspace = "workspace";

    private static void createDir(String path) {
        File file = new File(path);
        if (!file.exists()) {
            file.mkdirs();
        }
    }
    
    // 报价历史数据地址
    public static String getHistoryDir(String projectname) {
        String path =
            getPath(getWorkspacePath() + File.separator + workspace + File.separator + projectname + File.separator
                + history);
        return path;
    }

    public static String getInputDir(String projectname) {
        String path =
            getPath(getWorkspacePath() + File.separator + workspace + File.separator + projectname + File.separator
                + input);
        return path;
    }

    public static String getLogFile() {
        String path = getPath(getWorkspacePath() + File.separator + log) + File.separator + "Log.txt";
        File file = new File(path);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return path;
    }

    public static String getOutputDir(String projectname) {
        String path =
            getPath(getWorkspacePath() + File.separator + workspace + File.separator + projectname + File.separator
                + output);
        return path;
    }

    /**
     * CFG配置文件目录 workspace/cfg
     * 
     * @return
     */
    private static String getPath(String path) {
        createDir(path);
        return path;
    }

    public static String getPriceLogRecordFile() {
        String path = getPath(getWorkspacePath() + File.separator + log) + File.separator + "PriceMangaer.txt";
        File file = new File(path);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return path;
    }

    public static String getProjectDir(String projectname) {
        String path = getPath(getWorkspacePath() + File.separator + workspace + File.separator + projectname);
        return path;
    }
    
    // 获取调价配置文件
    public static String getProjectAdjustFile(String projectname) {
        return getProjectDir(projectname) + File.separator + "pkg.xml";
    }
    
    public static String getProjectBidEvalFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + "BidEval.xml";
    }

    public static String getProjectFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + "project.xml";
    }
    
    public static String getHelpDoc() {
        return getWorkspacePath() + File.separator + ToolConstants.HELP_DOC;
    }
    
    // 历史报价数据文件
    public static String getHistoryDBFile(String dbname) {
    	String path = getHistoryDir(dbname) + File.separator + TimeUtils.timePre() + "-history.db";
    	File file = new File(path);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return path;
    }
    
    // 历史报价记录文件xml
    public static String getHistoryRecordFile(String dbname) {
    	String path =
            getPath(getWorkspacePath() + File.separator + workspace + File.separator + dbname + File.separator
                + dbfileer)
                + File.separator + "history.xml";
        File file = new File(path);
        if (!file.exists()) {
        	Document document = DocumentHelper.createDocument();
        	document.addElement("root");
            XMLFileManager.saveUTF8Document(document, path);
        }
        
        return path;
    }
    
    public static String getProjectFile(String dbname) {
        getInputDir(dbname);
        getOutputDir(dbname);
        getHistoryDir(dbname);
        getHistoryRecordFile(dbname);
        String path =
            getPath(getWorkspacePath() + File.separator + workspace + File.separator + dbname + File.separator
                + dbfileer)
                + File.separator + "price.db";
        File file = new File(path);
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return path;
    }

    public static String getToolDirFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + ToolConstants.PKG_DIR;
    }

    public static String getToolLimitFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + ToolConstants.LIMIT_FILE;
    }

    public static String getToolOutputFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + ToolConstants.OUTPUT_DIR;
    }

    public static String getToolPkgFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + ToolConstants.PKG_FILE;
    }

    public static String getRealPriceFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + ToolConstants.REAL_PRICE_FILE;
    }
    
    public static String getToolPriceFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + ToolConstants.PRICE_FILE;
    }
    
    public static String getPkgTemplateFile() {
        return getWorkspacePath() + File.separator + cfg + File.separator + ToolConstants.TEMPLATE_FILE;
    }

    /**
     * Workspace配置文件目录 workspace/
     * @return
     */
    private static String getWorkspacePath() {
        File directory = new File("");// 参数为空
        String courseFile = "";
        try {
            courseFile = directory.getCanonicalPath();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return courseFile;
    }

}
