package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.io.File;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.model.StylesTable;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.dialogs.IDialogConstants;
import org.eclipse.jface.operation.IRunnableWithProgress;
import org.eclipse.swt.SWT;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.FileDialog;
import org.eclipse.swt.widgets.Shell;
import org.xml.sax.InputSource;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.u21.table.Table;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ProgressManager;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.model.FileItem;
import com.sieyuan.shrcn.tool.pricemanager.utils.FieldUtils;
import com.sieyuan.shrcn.tool.pricemanager.views.table.FileTableMode;

/**
 * 组件材料配置检查工具
 * 
 * <AUTHOR>
 * @version 1.0, 2025-5-13
 */
public class CheckCompConfigConfig extends WrappedDialog {

	// 物料清单
	private FileTableMode fileTableMode;
	private Table table;

	public CheckCompConfigConfig(Shell parentShell) {
		super(parentShell);
	}

	@Override
	protected Control createDialogArea(Composite parent) {
		Composite container = (Composite) super.createDialogArea(parent);
		container.setLayout(new GridLayout(3, false));

		table = new Table(container, SWT.BORDER | SWT.V_SCROLL | SWT.H_SCROLL);
		table.setLayout(new GridLayout(1, false));

		GridData gd_table = new GridData(SWT.FILL, SWT.FILL, true, true, 6, 10);
		gd_table.widthHint = 650;
		gd_table.heightHint = 280;
		table.setLayoutData(gd_table);
		fileTableMode = new FileTableMode();
		table.setModel(fileTableMode);

		return container;
	}

	@Override
	protected void buttonPressed(int buttonId) {
		if (buttonId == OK) {
			chackFile();
			return;
		} else if (buttonId == IDialogConstants.ABORT_ID) {
			addFiles();
			return;
		} else if (buttonId == IDialogConstants.BACK_ID) {
			removeFiles();
			return;
		}
		super.buttonPressed(buttonId);
	}

	@SuppressWarnings("unchecked")
	private void addFiles() {
		FileDialog dialog = new FileDialog(this.getShell(), SWT.OPEN | SWT.MULTI);
		dialog.setFilterExtensions(new String[] { "*.xlsx" });
		@SuppressWarnings("unused")
		String fileName = dialog.open();// 返回最后一个选择文件的全路径
		String[] fileNames = dialog.getFileNames();// 返回所有选择的文件名，不包括路径
		String path = dialog.getFilterPath();// 返回选择的路径，这个和fileNames配合可以得到所有的文件的全路径
		List<FileItem> items = fileTableMode.getItems();
		FileItem item = new FileItem();
		for (String fileItem : fileNames) {
			item = new FileItem();
			item.setChecked(true);
			item.setStatus("");
			item.setFileName(fileItem);
			item.setFileType(fileItem.substring(fileItem.lastIndexOf(".") + 1));
			File file = new File(path + File.separator + fileItem);
			item.setAbsFileName(file.getAbsolutePath());
			item.setFileSize(FieldUtils.getFileSize(file));
			items.add(item);
		}
		fileTableMode.setItems(items);
		table.redraw();
	}

	@SuppressWarnings("unchecked")
	private void removeFiles() {
		List<FileItem> itemsList = new ArrayList<>();
		List<FileItem> items = fileTableMode.getItems();
		for (FileItem fileItem : items) {
			if (!fileItem.isChecked()) {
				itemsList.add(fileItem);
			}
		}
		fileTableMode.setItems(itemsList);
		table.redraw();
	}

	@SuppressWarnings("unchecked")
	private void chackFile() {
		final List<FileItem> items = fileTableMode.getItems();

		ProgressManager.execute(new IRunnableWithProgress() {
			@Override
			public void run(IProgressMonitor monitor) throws InvocationTargetException, InterruptedException {
				boolean hasError = false;
				monitor.beginTask("检查组件材料配置表......", items.size());
				for (FileItem fileItem : items) {
					fileItem.setStatus("检查中...");
					monitor.setTaskName("检查" + fileItem.getFileName() + "中...");
					Display.getDefault().asyncExec(new Runnable() {
						@Override
						public void run() {
							table.redraw();
						}
					});

					try {
						List<String> errors = checkSingleFile(fileItem.getAbsFileName());
						if (errors.isEmpty()) {
							fileItem.setStatus("检查通过");
						} else {
							fileItem.setStatus("检查失败: " + errors.size() + "个错误");
							ConsoleManager.getInstance().append("检查文件: " + fileItem.getFileName() + " 发现以下错误:");
							for (String error : errors) {
								ConsoleManager.getInstance().append(error);
							}

							hasError = true;
						}
					} catch (Exception e) {
						fileItem.setStatus("检查异常: " + e.getMessage());
						SCTLogger.error("Error checking file: " + fileItem.getAbsFileName(), e);
						hasError = true;
					}

					Display.getDefault().asyncExec(new Runnable() {
						@Override
						public void run() {
							table.redraw();
						}
					});

					monitor.worked(1);
				}
				if (!hasError) {
					DialogHelper.showAsynInformation("所有文件检查通过！");
				} else {
					DialogHelper.showAsynError("部分文件检查失败，请查看状态列！");
				}
				monitor.done();
			}
		}, true);

	}

	private List<String> checkSingleFile(String filePath) throws Exception {
		List<String> errors = new LinkedList<>();
		try (OPCPackage pkg = OPCPackage.open(filePath, PackageAccess.READ)) {
			XSSFReader xssfReader = new XSSFReader(pkg);
			StylesTable styles = xssfReader.getStylesTable();
			ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(pkg);

			// 获取所有sheet并逐个检查
			XSSFReader.SheetIterator sheets = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
			int sheetIndex = 0;
			while (sheets.hasNext()) {
				InputStream sheet = sheets.next();
				String sheetName = sheets.getSheetName();
				sheetIndex++;

				XMLReader sheetParser = createXMLReader();
				SheetHandler handler = new SheetHandler(styles, strings, errors, sheetName, sheetIndex);
				sheetParser.setContentHandler(handler);
				sheetParser.parse(new InputSource(sheet));

				sheet.close();
			}
		}
		return errors;
	}

	// Helper method to create XMLReader compatible with older POI versions
	private XMLReader createXMLReader() throws Exception {
		SAXParserFactory saxFactory = SAXParserFactory.newInstance();
		SAXParser saxParser = saxFactory.newSAXParser();
		return saxParser.getXMLReader();
	}

	/**
	 * SAX handler for reading a sheet
	 */
	private static class SheetHandler extends DefaultHandler {
		@SuppressWarnings("unused")
		private StylesTable stylesTable;
		private ReadOnlySharedStringsTable sharedStringsTable;
		private List<String> errors;
		private String cellValue;
		private int currentRow = 0;
		private String currentColumn = null;
		private Map<Integer, String> rowData = new HashMap<>();
		private String sheetName;
		private int sheetIndex;

		// 更新列索引：H列后面的索引都要-1
		private static final int D_COL_INDEX = 3; // 0-indexed
		private static final int N_COL_INDEX = 12; // 0-indexed (原来是13，现在-1)
		private static final int K_COL_INDEX = 9; // 0-indexed (原来是10，现在-1)
		private static final int L_COL_INDEX = 10; // 0-indexed (原来是11，现在-1)
		private static final int I_COL_INDEX = 7; // 0-indexed (原来是8，现在-1)

		// 更新列名：字符要往前偏移一位
		private static final String D_COL_NAME = "D";
		private static final String N_COL_NAME = "M"; // 原来是N，现在是M
		private static final String K_COL_NAME = "J"; // 原来是K，现在是J
		private static final String L_COL_NAME = "K"; // 原来是L，现在是K
		private static final String I_COL_NAME = "H"; // 原来是I，现在是H
		private boolean isCellValueNumeric = false;

		private SheetHandler(StylesTable styles, ReadOnlySharedStringsTable strings, List<String> errors, String sheetName, int sheetIndex) {
			this.stylesTable = styles;
			this.sharedStringsTable = strings;
			this.errors = errors;
			this.sheetName = sheetName;
			this.sheetIndex = sheetIndex;
		}

		public void startElement(String uri, String localName, String name, org.xml.sax.Attributes attributes) throws org.xml.sax.SAXException {
			if ("row".equals(name)) {
				currentRow = Integer.parseInt(attributes.getValue("r"));
				rowData.clear();
			} else if ("c".equals(name)) {
				String cellRef = attributes.getValue("r");
				currentColumn = extractColumnName(cellRef);
				String cellType = attributes.getValue("t");
				// 修复数值类型判断逻辑
				// t="n" 表示数字类型，t="s" 表示共享字符串，t=null 通常也是数字类型
				isCellValueNumeric = "n".equals(cellType) || (cellType == null);
				if (cellType != null && "s".equals(cellType)) {
					// Cell contains a value from the shared strings table
				}
			} else if ("v".equals(name)) {
				// This is the value cell containing the contents
				cellValue = ""; // Reset value
			}
		}

		// 更好的列名提取方法
		private String extractColumnName(String cellRef) {
			if (cellRef == null) return "";
			StringBuilder colName = new StringBuilder();
			for (char c : cellRef.toCharArray()) {
				if (Character.isLetter(c)) {
					colName.append(c);
				} else {
					break; // 遇到数字就停止
				}
			}
			return colName.toString();
		}

		public void endElement(String uri, String localName, String name) throws org.xml.sax.SAXException {
			if ("v".equals(name)) {
				int colIndex = getColumnIndex(currentColumn);
				if (colIndex != -1) {
					String value = cellValue;
					if (!isCellValueNumeric && sharedStringsTable != null) {
						try {
							int idx = Integer.parseInt(value);
							value = sharedStringsTable.getEntryAt(idx);
						} catch (NumberFormatException e) {
							// Not a shared string
						}
					}

					// 调试信息 - 只对M列输出
					if (colIndex == N_COL_INDEX && currentRow > 4 && currentRow <= 10) {
						System.out.println("M列单元格处理: 行" + currentRow + ", 列" + currentColumn + ", 原始值='" + cellValue + "', 处理后值='" + value + "', 数值类型=" + isCellValueNumeric);
						if (!cellValue.equals(value)) {
							System.out.println("  警告: 原始值与处理后值不同！");
						}
					}

					rowData.put(colIndex, value);
				}
			} else if ("row".equals(name)) {
				// Process the row data after reading all cells
				if (currentRow > 4) { // 数据行从第五行开始（原来是第三行）
					checkRowData(rowData, currentRow);
				}
			}
		}

		public void characters(char[] ch, int start, int length) throws org.xml.sax.SAXException {
			if (cellValue != null) {
				cellValue += new String(ch, start, length);
			}
		}

		private void checkRowData(Map<Integer, String> data, int row) {
			String dColValue = data.get(D_COL_INDEX) != null ? data.get(D_COL_INDEX).trim() : "";
			String nColValue = data.get(N_COL_INDEX) != null ? data.get(N_COL_INDEX).trim() : "";
			String kColValue = data.get(K_COL_INDEX) != null ? data.get(K_COL_INDEX).trim() : "";
			String lColValue = data.get(L_COL_INDEX) != null ? data.get(L_COL_INDEX).trim() : "";
			String iColValue = data.get(I_COL_INDEX) != null ? data.get(I_COL_INDEX).trim() : "";

			String sheetInfo = "Sheet[" + sheetName + "]第" + row + "行";

			// 添加调试信息 - 只在M列为空但应该有值时输出
			if (nColValue.isEmpty() && !dColValue.isEmpty()) {
				System.out.println("警告 - " + sheetInfo + ": M列为空但D列有值 - D=" + dColValue + ", M=" + nColValue);
				System.out.println("  完整行数据: " + data.toString());
			}

			// Check 1.1 - D is number, M should be same number
			try {
				double dNum = Double.parseDouble(dColValue);
				if (dNum >= 0) {
					try {
						double nNum = Double.parseDouble(nColValue);
						if (dNum != nNum) {
							errors.add(sheetInfo + "数字不相等 (D:" + dColValue + ", M:" + nColValue + ")");
						}
					} catch (NumberFormatException e) {
						errors.add(sheetInfo + " M列不是数字 (D:" + dColValue + ", M:" + nColValue + ")");
					}
				}
			} catch (NumberFormatException e) {
				// D is not a number, proceed to other checks for D
				if (!iColValue.contains("ATS-501")) {
					// Check 1.2 - D is symbol, M should be 0
					if ("-".equals(dColValue)) {
						try {
							double nNum = Double.parseDouble(nColValue);
							if (nNum != 0) {
								errors.add(sheetInfo + "数字不为0 (D:" + dColValue + ", M:" + nColValue + ")");
							}
						} catch (NumberFormatException e2) {
							errors.add(sheetInfo + " M列不是数字 (D:" + dColValue + ", M:" + nColValue + ")");
						}
					}
					// Check 1.3 - D is text, M should be number >= 0
					else if (!dColValue.isEmpty()) {
						try {
							double nNum = Double.parseDouble(nColValue);
							if (nNum < 0) {
								errors.add(sheetInfo + " M列数字小于0 (D:" + dColValue + ", M:" + nColValue + ")");
							}
						} catch (NumberFormatException e2) {
							errors.add(sheetInfo + "不是数字 (D:" + dColValue + ", M:" + nColValue + ")");
						}
					}
				}
			}

			// Check 2.1 - If M >= 1, J and K should not be empty UNLESS H
			// contains "预制舱"
			try {
				double nNum = Double.parseDouble(nColValue);
				if (nNum >= 1) {
					if (!iColValue.contains("预制舱")) {
						if (kColValue.isEmpty()) {
							errors.add(sheetInfo + "组件产地为空 (M:" + nColValue + ")");
						}
						if (lColValue.isEmpty()) {
							errors.add(sheetInfo + "组件制造商为空 (M:" + nColValue + ")");
						}
					}
				}
			} catch (NumberFormatException e) {
				// M is not a number, skip check 2.1 for this row
			}
		}

		private int getColumnIndex(String colName) {
			// Simple mapping for expected columns. More robust logic might be
			// needed for all columns.
			int result;
			switch (colName) {
			case D_COL_NAME:
				result = D_COL_INDEX;
				break;
			case N_COL_NAME: // M列
				result = N_COL_INDEX;
				break;
			case K_COL_NAME: // J列
				result = K_COL_INDEX;
				break;
			case L_COL_NAME: // K列
				result = L_COL_INDEX;
				break;
			case I_COL_NAME: // H列
				result = I_COL_INDEX;
				break;
			default:
				result = -1; // Not a column we care about
			}

			// 调试信息 - 只输出M列的映射
			if (result != -1 && N_COL_NAME.equals(colName)) {
				System.out.println("M列映射: " + colName + " -> 索引" + result);
			}

			return result;
		}
	}

	/**
	 * 配置对话框.
	 */
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("组件材料配置表检查工具");
	}

	/**
	 * 创建按钮.
	 * 
	 * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
	 */
	@Override
	protected void createButtonsForButtonBar(Composite parent) {
		createButton(parent, IDialogConstants.OK_ID, "检查", true);
		createButton(parent, IDialogConstants.ABORT_ID, "添加文件", false);
		createButton(parent, IDialogConstants.BACK_ID, "移除文件", false);
		createButton(parent, IDialogConstants.CANCEL_ID, "关闭", false);
	}

	/**
	 * 对话框的尺寸.
	 * 
	 * @return 对话框的初始尺寸.
	 */
	@Override
	protected Point getInitialSize() {
		return new Point(860, 480);
	}

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

}
