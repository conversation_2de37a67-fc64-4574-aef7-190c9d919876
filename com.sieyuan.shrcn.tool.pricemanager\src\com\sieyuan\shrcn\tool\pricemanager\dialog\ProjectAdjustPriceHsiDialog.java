package com.sieyuan.shrcn.tool.pricemanager.dialog;

import java.util.ArrayList;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.MouseEvent;
import org.eclipse.swt.events.MouseListener;
import org.eclipse.swt.graphics.Point;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.widgets.Composite;
import org.eclipse.swt.widgets.Control;
import org.eclipse.swt.widgets.List;
import org.eclipse.swt.widgets.Shell;

import com.shrcn.found.ui.app.WrappedDialog;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.SwtUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.sieyuan.shrcn.tool.pricemanager.data.AdjustPriceHistory;
import com.sieyuan.shrcn.tool.pricemanager.views.NavigatView;

/**
 * @Description 调价历史界面
 * @version 1.0
 * @since JDK1.7
 * <AUTHOR>
 * @company <PERSON><PERSON>uan
 * @copyright (c) 2019 Sieyuan Co'Ltd Inc. All rights reserved.
 * @date 2022-11-16 下午16:14:12
 
 */
public class ProjectAdjustPriceHsiDialog extends WrappedDialog {

	private List hisList;
    private String selectHisName;
    
    private UIPreferences perference = UIPreferences.newInstance();
    
	public ProjectAdjustPriceHsiDialog(Shell parentShell) {
		super(parentShell);
	}
	
	@Override
	protected void buttonPressed(int buttonId) {
		String[] selected = hisList.getSelection();
		String prj = perference.getInfo("com.shrcn.pricemangertool.curentprojectname");
        if (buttonId == OK) {
            if (selected == null || selected.length <= 0) {
            	DialogHelper.showWarning("请选择待打开调价历史！");
            	return;
            }
        	selectHisName = selected[0];
            boolean select = AdjustPriceHistory.selectByName(prj, selectHisName);
            if (!select) {
            	DialogHelper.showWarning("该调价历史异常，请选择其他调价历史！");
            	return;
			}
			NavigatView.refreshTree();
        }
        if (buttonId == 100) {
        	if (selected == null || selected.length <= 0) {
        		DialogHelper.showWarning("请选择待删调价历史！");
            	return;
            }
        	if (DialogHelper.showConfirm("确定删除所选历史？", "是", "否")) {
        		selectHisName = selected[0];
        		AdjustPriceHistory.deleteHis(prj, selectHisName);
                initData();
            }
        }
        super.buttonPressed(buttonId);
	}
	
	@Override
	protected void configureShell(Shell newShell) {
		super.configureShell(newShell);
		newShell.setText("调价历史");
	}
	
	/**
     * 创建按钮.
     * @return 此方法返回<code>null</code>可去掉对话框上的按钮.
     */
    @Override
    protected void createButtonsForButtonBar(Composite parent) {
    	createButton(parent, OK, "打开", true);
        createButton(parent, 100, "删除", false);
        createButton(parent, CANCEL, "关闭", false);
    }
    
    @Override
    protected Control createDialogArea(Composite parent) {
    	GridData gridData = new GridData(GridData.FILL_BOTH);
        gridData.heightHint = 320;
        hisList = SwtUtil.createList(parent, gridData);
        initData();
        hisList.addMouseListener(new MouseListener() {

            @Override
            public void mouseDoubleClick(MouseEvent e) {
                buttonPressed(OK);
            }

            @Override
            public void mouseDown(MouseEvent e) {}

            @Override
            public void mouseUp(MouseEvent e) {}
        });
        return super.createDialogArea(parent);
    }

    /**
     * 对话框的尺寸.
     * 
     * @return 对话框的初始尺寸.
     */
    @Override
    protected Point getInitialSize() {
        return new Point(600, 350);
    }

	@Override
	protected void setShellStyle(int newShellStyle) {
		super.setShellStyle(SWT.DIALOG_TRIM | SWT.RESIZE);
	}

    private void initData() {
        java.util.List<String> lis = new ArrayList<>();
        lis = AdjustPriceHistory.getAllHisNames(perference.getInfo("com.shrcn.pricemangertool.curentprojectname"));
        hisList.removeAll();
        if (lis.size() > 0) {
        	hisList.setItems(lis.toArray(new String[lis.size()]));
        	hisList.select(0);
        }
    }
}
