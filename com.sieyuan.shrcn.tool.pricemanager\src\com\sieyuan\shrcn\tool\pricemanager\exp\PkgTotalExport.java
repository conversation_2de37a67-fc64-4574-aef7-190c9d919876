package com.sieyuan.shrcn.tool.pricemanager.exp;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.shrcn.found.common.log.SCTLogger;
import com.shrcn.found.common.util.NumberToCN;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.utils.SXSSFWorkbookUtil;
import com.sieyuan.shrcn.tool.pricemanager.utils.UIPreferencesUtil;

public class PkgTotalExport {

    /**
     * 创建价格行
     * @param price
     * @param priceZhStr
     */
    private static int createPriceRow(SXSSFWorkbook wb, Sheet sheet, int rowNum, String price, String priceZhStr) {
        Row row = sheet.createRow(rowNum);
        row.setHeightInPoints(20);// 设置行高
        rowNum++;
        SXSSFWorkbookUtil.createCell(row, 0, "合计人民币（小写）：  ", wb, true, false, false, false,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 1, "", wb, true, false, false, false,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 2, "", wb, true, false, false, false,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCellNumeric(row, 3, price, wb, true, false, false, false,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 4, "万元人民币", wb, true, false, false, false,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 5, "", wb, true, false, false, false,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 6, "（大写）： ", wb, true, true, true, true,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 7, priceZhStr, wb, true, true, true, true,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 8, "", wb, true, true, true, true,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 9, "", wb, true, true, true, true,  HorizontalAlignment.CENTER);
        SXSSFWorkbookUtil.createCell(row, 10, "", wb, true, true, true, true,  HorizontalAlignment.CENTER);

        sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, 2));
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 4, 5));
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 7, 10));
        return rowNum;
    }

    public static void export(String fileName, String pkgName, boolean isCheck, Map<String, List<PkgInfo>> pkgNameMaps,
        String price) {
        SXSSFWorkbook wb = new SXSSFWorkbook();
        int rowNum = 0;
        Sheet sheet;
        if (isCheck) {
            sheet = wb.createSheet("pkg-check");
        } else {
            sheet = wb.createSheet("pkg");
        }
        sheet.setColumnWidth(0, 2500);// 设置行宽
        sheet.setColumnWidth(1, 2500);
        sheet.setColumnWidth(2, 2500);
        sheet.setColumnWidth(3, 7000);
        sheet.setColumnWidth(4, 3000);
        sheet.setColumnWidth(5, 3000);
        sheet.setColumnWidth(6, 3500);
        sheet.setColumnWidth(7, 3500);
        sheet.setColumnWidth(8, 2500);
        sheet.setColumnWidth(9, 3500);
        sheet.setColumnWidth(10, 3500);
        sheet.setColumnWidth(11, 3500);
        if (isCheck) {
            sheet.setColumnWidth(12, 3500);
            sheet.setColumnWidth(13, 3500);
        }
        // 标题
        Row titleRow = sheet.createRow(rowNum);
        titleRow.setHeightInPoints(20);// 设置行高
        rowNum++;
        SXSSFWorkbookUtil.createCell(titleRow, 0, "货物清单行报价暨投标报价汇总表", wb, true, (short)16);
        Row titleRow1 = sheet.createRow(rowNum);
        rowNum++;
        SXSSFWorkbookUtil.createCell(titleRow1, 0, "序号", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 1, "项目单位", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 2, "项目名称", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 3, "货物（功能规格）描述", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 4, "单位", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 5, "数量", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 6, "未含税报价", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 7, "", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 8, "税率", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 9, "含税报价", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow1, 10, "", wb, true, (short)10);

        if (isCheck) {
            SXSSFWorkbookUtil.createCell(titleRow1, 11, " ", wb, true, (short)10);
            SXSSFWorkbookUtil.createCell(titleRow1, 12, "比较", wb, true, (short)10);
            SXSSFWorkbookUtil.createCell(titleRow1, 13, "", wb, true, (short)10);
        }

        Row titleRow2 = sheet.createRow(rowNum);
        rowNum++;
        SXSSFWorkbookUtil.createCell(titleRow2, 0, "", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 1, "", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 2, "", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 3, "", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 4, "", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 5, "", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 6, "单价", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 7, "合价", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 8, "(%)", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 9, "单价", wb, true, (short)10);
        SXSSFWorkbookUtil.createCell(titleRow2, 10, "合价", wb, true, (short)10);
        if (isCheck) {
            SXSSFWorkbookUtil.createCell(titleRow2, 11, " ", wb, true, (short)10);
            SXSSFWorkbookUtil.createCell(titleRow2, 12, "限价", wb, true, (short)10);
            SXSSFWorkbookUtil.createCell(titleRow2, 13, "价差", wb, true, (short)10);
        }
        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 10));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 5, 5));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 6, 7));
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 9, 10));
        if (isCheck) {
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 12, 13));
        }
        List<PkgInfo> pkgs = pkgNameMaps.get(pkgName);
        BigDecimal priceZh;
        if (StringUtils.isEmpty(price)) {
            priceZh = new BigDecimal("0.000000");
        } else {
            priceZh = new BigDecimal(price).multiply(new BigDecimal("10000"));
        }

        String priceZhStr = NumberToCN.number2CNMontrayUnit(priceZh);
        int index = 1;
        for (PkgInfo pkg : pkgs) {
            Row row = sheet.createRow(rowNum);
            rowNum++;
            SXSSFWorkbookUtil.createCellNumberic(row, 0, String.valueOf(index), wb, false, (short)10);
            SXSSFWorkbookUtil.createCell(row, 1, pkg.getProjectOwner(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCell(row, 2, pkg.getProjectName(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCell(row, 3, pkg.getProductDesc(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCell(row, 4, pkg.getUnit(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCellNumberic(row, 5, pkg.getCount(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCellNumberic(row, 6, pkg.getWithoutTaxPrice(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCellNumberic(row, 7, pkg.getWithoutTotalPrice(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCellNumberic(row, 8, String.valueOf(UIPreferencesUtil.getTaxRate()), wb, false, (short)10);// 税率
            SXSSFWorkbookUtil.createCellNumberic(row, 9, pkg.getPrice(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCellNumberic(row, 10, pkg.getTotalPrice(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCell(row, 11, pkg.getBidNo(), wb, false, (short)10);
            SXSSFWorkbookUtil.createCell(row, 12, pkg.getApplyId(), wb, false, (short)10);
            if (isCheck) {
                SXSSFWorkbookUtil.createCell(row, 13, pkg.getTotalLimitPrice(), wb, false, (short)10);
                SXSSFWorkbookUtil.createCell(row, 14, "", wb, IndexedColors.RED.getIndex());
            }
            index++;
        }

        rowNum = createPriceRow(wb, sheet, rowNum, price, priceZhStr);
        Row downRow = sheet.createRow(rowNum);// 底部行
        downRow.setHeightInPoints(20);// 设置行高
        rowNum++;
        SXSSFWorkbookUtil.createCell(downRow, 0, "投标保证金：（单独密封）", wb, true, true, true, true, HorizontalAlignment.LEFT);
        SXSSFWorkbookUtil.createCell(downRow, 1, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 2, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 3, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 4, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 5, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 6, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 7, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 8, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 9, "", wb, false, (short)10);
        SXSSFWorkbookUtil.createCell(downRow, 10, "", wb, false, (short)10);
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, 10));

        FileOutputStream out = null;
        try {
            out = new FileOutputStream(fileName);
            wb.write(out);
        } catch (IOException e) {
            SCTLogger.error(e.getMessage());
        } finally {
            try {
                if (out != null)
                    out.close();
            } catch (IOException e) {
                SCTLogger.error(e.getMessage());
            }
        }
    }

}
