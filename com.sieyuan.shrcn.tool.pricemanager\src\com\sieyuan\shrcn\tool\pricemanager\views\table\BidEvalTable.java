package com.sieyuan.shrcn.tool.pricemanager.views.table;

import java.awt.Toolkit;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.StringSelection;
import java.awt.datatransfer.Transferable;
import java.awt.datatransfer.UnsupportedFlavorException;
import java.io.IOException;
import java.util.Arrays;

import org.apache.commons.lang.math.NumberUtils;
import org.eclipse.swt.widgets.Composite;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.UIConstants;
import com.shrcn.found.ui.model.IField;
import com.shrcn.found.ui.model.TableConfig;
import com.shrcn.found.ui.table.DefaultKTable;
import com.shrcn.found.ui.table.RKTable;
import com.shrcn.found.ui.table.action.CopyAction;
import com.shrcn.found.ui.table.action.TableAction;
import com.shrcn.found.ui.util.DialogHelper;
import com.shrcn.found.ui.util.ImageConstants;
import com.shrcn.found.ui.util.ImgDescManager;
import com.shrcn.found.ui.view.ConsoleManager;

/**
 * @Description:BidEvalTableModel
 * <AUTHOR>
 */
public class BidEvalTable extends RKTable {

	class PasteAllAction extends TableAction {

		private RKTable rktable;

		public PasteAllAction(RKTable table) {
			super(table);
			setText("粘贴全部");
			this.rktable = table;
			setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
		}

		@Override
		public void run() {
			String clipBoardContent = "";
			Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
			try {
				Transferable contents = clipboard.getContents(null);
				if (contents.isDataFlavorSupported(DataFlavor.stringFlavor)) {
					// 获取文本数据
					clipBoardContent = (String) contents.getTransferData(DataFlavor.stringFlavor);
					// 使用clipboardText进行进一步处理
				}
			} catch (UnsupportedFlavorException | IOException e) {
				// 处理异常
				e.printStackTrace();
			}

			if (StringUtil.isEmpty(clipBoardContent)) {
				DialogHelper.showWarning("复制参数为空！");
				return;
			}
			int filedLength = getVisibleFields().length;
			int col = 1;

			IField field = getVisibleFields()[col];
			if (field.getEditor().equals("none")) {
				DialogHelper.showAsynError(field.getTitle() + "列不允许编辑");
				return;
			}

			String[] str = clipBoardContent.split("\\n");
			java.util.List<?> input = rktable.getInput();
			if (input.size() != str.length) {
				DialogHelper.showAsynError("复制条数与表格内条数不一致！");
				return;
			}
			for (int i = 0; i < rktable.getInput().size(); i++) {
				copyValues(filedLength, col, str, i);
			}
			rktable.refresh();

		}

		private void copyValues(int filedLength, int col, String[] str, int i) {
			String[] lineValue = str[i].split("\\t");
			int index = -1;
			int startcol = col;
			for (; startcol < filedLength; startcol++) {
				IField newfield = getVisibleFields()[startcol];
				if (!newfield.getEditor().equals("none")) {
					index++;
					if (index >= lineValue.length) {
						break;
					}
					setValue(i, lineValue, index, startcol, newfield);
				}
			}
		}

		private void setValue(int i, String[] lineValue, int index, int startcol, IField newfield) {
			if (StringUtil.isEmpty(lineValue[index])) {
				rktable.getTablemodel().doSetContentAt(startcol, i + 1, "");
			} else {
				if (NumberUtils.isNumber(lineValue[index].trim())) {
					rktable.getTablemodel().doSetContentAt(startcol, i + 1, lineValue[index].trim());
				} else {
					ConsoleManager.getInstance().append(lineValue[index] + "是非法的数字!");
				}
			}
		}

		@Override
		public boolean isEnabled() {
			return rktable.getSelection() != null;
		}
	}

	class CopyValueAction extends CopyAction {

		private RKTable rktable;

		public CopyValueAction(RKTable table) {
			super(table);
			setText("复制全部");
			this.rktable = table;
			setImageDescriptor(ImgDescManager.getImageDesc(ImageConstants.PASTE));
		}

		@Override
		public boolean isEnabled() {
			return rktable.getSelection() != null;
		}

		@Override
		public void run() {
			StringBuilder textBuff = new StringBuilder();

			for (int i = 0; i < rktable.getInput().size(); i++) {
				int columnNum = getVisibleFields().length;
				for (int j = 1; j < columnNum; j++) {
					textBuff.append(rktable.getTablemodel().getContentAt(j, i + 1));
					if (j < columnNum - 1) {
						textBuff.append("\t");
					}
				}
				textBuff.append("\n"); // 添加一行的结束，回车
			}
			String text = new String(textBuff);
			Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
			StringSelection str = new StringSelection(text);
			clipboard.setContents(str, null);
		}
	}

	public BidEvalTable(Composite parent, TableConfig config) {
		super(parent, config);
	}

	@Override
	protected void initUI() {
		tablemodel = new BidEvalTableModel(this, config);
		table = new DefaultKTable(parent, UIConstants.KTABLE_CELL_STYLE, tablemodel);
	}

	@Override
	protected void initData() {
	}

	@Override
	protected void initOthers() {
		actions.add(new CopyValueAction(this));
		actions.add(new PasteAllAction(this));
	}

}
