package com.sieyuan.shrcn.tool.pricemanager.views.table;

import org.apache.commons.lang.StringUtils;

import com.sieyuan.shr.u21.ui.table.TableModel;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.model.CostPrice;
import com.sieyuan.shrcn.tool.pricemanager.views.DetailView;

import de.kupzog.ktable.KTableCellEditor;
import de.kupzog.ktable.KTableCellRenderer;
import de.kupzog.ktable.SWTX;

public class PriceInfoTableMode extends TableModel {

    /**
     * Initialize the base implementation.
     */
    public PriceInfoTableMode() {
        super();
    }

    /**
     * 设置表单元格编辑器
     * 
     * @return
     */
    public KTableCellEditor doGetCellEditor(int col, int row) {
        return null;
    }

    public KTableCellRenderer doGetCellRenderer(int col, int row) {
        m_fixedRenderer.setAlignment(SWTX.ALIGN_HORIZONTAL_CENTER | SWTX.ALIGN_VERTICAL_CENTER);
        m_textRenderer.setAlignment(SWTX.ALIGN_HORIZONTAL_CENTER | SWTX.ALIGN_VERTICAL_CENTER);
        if (row % 2 == 0) {
            m_checkableRenderer.setDefaultBackground(DetailView.bgcolor1);
            m_textRenderer.setDefaultBackground(DetailView.bgcolor1);
        } else {
            m_checkableRenderer.setDefaultBackground(DetailView.bgcolor2);
            m_textRenderer.setDefaultBackground(DetailView.bgcolor2);
        }
        return isFixedCell(col, row) ? m_fixedRenderer : m_textRenderer;
    }

    /**
     * 取得表格列个数
     */
    public int doGetColumnCount() {
        return getHead().length;
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.shrcn.vdd.ui.table.model.TableModel#doGetContentAt(int, int)
     */
    public Object doGetContentAt(int col, int row) {
        // 第一行是标题行
        if (row == 0) {
            return getHead()[col];
        }
        CostPrice item = (CostPrice)items.get(row - 1);
        switch (col) {
            case 0:
                return row;
            case 1:
                return StringUtils.trimToEmpty(item.getDevtype());
            case 2:
                return StringUtils.trimToEmpty(item.getSupply());
            case 3:
                return StringUtils.trimToEmpty(item.getArea());
            case 4:
                return item.getType() == 1 ? ToolConstants.PRICE_IN : ToolConstants.PRICE_OUT;
            case 5:
                return StringUtils.trimToEmpty(item.getCostPrice());
            case 6:
                return StringUtils.trimToEmpty(item.getPrice());
            default:
                return "";
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see com.shrcn.vdd.ui.table.model.TableModel#doSetContentAt(int, int,
     * java.lang.Object)
     */
    public void doSetContentAt(int col, int row, Object value) {
        if (row - 1 >= 0) {
            CostPrice item = (CostPrice)items.get(row - 1);
            if (value == null) {
                value = "";
            }
            switch (col) {
                case 1:
                    item.setDevtype((String)value);
                    break;
                case 2:
                    item.setSupply((String)value);
                    break;
                case 3:
                    item.setArea((String)value);
                    break;
                case 4:
                    item.setType(Integer.valueOf((String)value));
                    break;
                case 5:
                    item.setCostPrice((String)value);
                    break;
                case 6:
                    item.setPrice((String)value);
                    break;
            }
        }
    }

    public String[] getHead() {
        return ITableMessage.PRICE_INFO_HEAD;
    }

    public int[] getHeadWidth() {
        return ITableMessage.PRICE_INFO_WIDTH;
    }

    /**
     * 得到初始化行宽度
     */
    public int getInitialColumnWidth(int col) {
        return (col < getHeadWidth().length) ? getHeadWidth()[col] : super.getInitialColumnWidth(col);
    }

}
