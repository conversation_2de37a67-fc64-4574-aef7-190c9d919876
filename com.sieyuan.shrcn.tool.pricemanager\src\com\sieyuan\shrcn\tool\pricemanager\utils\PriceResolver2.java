/**
 * Copyright (c) 2007-2017 思源电气股份有限公司. All rights reserved. This program is an eclipse Rich Client Application.
 */
package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.eclipse.core.runtime.IProgressMonitor;

import com.shrcn.found.common.util.StringUtil;
import com.shrcn.found.ui.util.UIPreferences;
import com.shrcn.found.ui.view.ConsoleManager;
import com.sieyuan.shrcn.tool.pricemanager.app.ToolConstants;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgAdjustDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgInfoDao;
import com.sieyuan.shrcn.tool.pricemanager.dao.PkgProductDao;
import com.sieyuan.shrcn.tool.pricemanager.dialog.EnvirmentSettingDialog;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgAdjust;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgInfo;
import com.sieyuan.shrcn.tool.pricemanager.model.PkgProduct;

/**
 * 新版本调价算法
 * 
 * <AUTHOR> (mailto:<EMAIL>)
 * @version 1.0, 2022-11-16
 */
public class PriceResolver2 {

	private UIPreferences perference = UIPreferences.newInstance();
	private String IN_TAX = ".inTax";

	private List<PkgInfo> allPkgList;
	private List<PkgProduct> allProducts;
	private List<String> warnings;

	public PriceResolver2() {
		super();
		this.allPkgList = new ArrayList<>();
		this.allProducts = new ArrayList<>();
		this.warnings = new ArrayList<>();
	}

	public void adjustPrices(List<PkgAdjust> adjsutList, IProgressMonitor monitor) {
		// 无独有ID的包
		List<PkgAdjust> noOnlyIdPkgs = new ArrayList<>();
		// 有独有ID的包
		List<PkgAdjust> hasOnlyIdPkgs = new ArrayList<>();

		Map<String, List<PkgInfo>> pkgMap = getPkgMap();
		// 获取所有PRODUCT按照父节点组装为MAP
		Map<Integer, List<PkgProduct>> productMap = getProductMap();

		monitor.worked(10);
		// 遍历所有的待调价包，区分独有ID的包和不包含独有ID的包
		for (PkgAdjust adjust : adjsutList) {
			if (Boolean.FALSE.equals(adjust.getIsPriority())) {
				hasOnlyIdPkgs.add(adjust);
			} else {
				noOnlyIdPkgs.add(adjust);
			}
		}

		String inWight = perference.getInfo(EnvirmentSettingDialog.class.getName() + IN_TAX);
		if (StringUtils.isEmpty(inWight)) {
			inWight = "2";
		}
		String outweight = "1";

		monitor.worked(10);

		Map<String, BigDecimal> priceMap = new HashMap<>();

		// 遍历所有无独有ID的包和优先调价的包，更新成本价和利润率
		List<String> pkgNames = new ArrayList<>();
		// 按照优先级调价, 非独有ID
		Collections.sort(noOnlyIdPkgs, new Comparator<PkgAdjust>() {
			@Override
			public int compare(PkgAdjust o1, PkgAdjust o2) {
				return o1.getSeq() - o2.getSeq();
			}
		});
		Map<Integer, List<PkgAdjust>> noOnlyPkgMap = new LinkedHashMap<>();
		for (PkgAdjust adjust : noOnlyIdPkgs) {
			List<PkgAdjust> pkgAdjust = noOnlyPkgMap.get(adjust.getSeq());
			if (pkgAdjust == null || pkgAdjust.isEmpty()) {
				pkgAdjust = new ArrayList<>();
			}
			pkgAdjust.add(adjust);
			noOnlyPkgMap.put(adjust.getSeq(), pkgAdjust);
		}
		for (Entry<Integer, List<PkgAdjust>> entry : noOnlyPkgMap.entrySet()) {
			Integer key = entry.getKey();
			processNoOnlyIdPkg(noOnlyPkgMap.get(key), pkgMap, productMap, inWight, outweight, priceMap, pkgNames);
		}

		// 遍历所有包含独有ID的包，更新成本价和利润率
		pkgNames = new ArrayList<>();
		// 按照优先级调价, 非独有ID
		Collections.sort(hasOnlyIdPkgs, new Comparator<PkgAdjust>() {
			@Override
			public int compare(PkgAdjust o1, PkgAdjust o2) {
				return o1.getSeq() - o2.getSeq();
			}
		});
		Map<Integer, List<PkgAdjust>> hasOnlyPkgMap = new LinkedHashMap<>();
		for (PkgAdjust adjust : hasOnlyIdPkgs) {
			List<PkgAdjust> pkgAdjust = hasOnlyPkgMap.get(adjust.getSeq());
			if (pkgAdjust == null || pkgAdjust.isEmpty()) {
				pkgAdjust = new ArrayList<>();
			}
			pkgAdjust.add(adjust);
			hasOnlyPkgMap.put(adjust.getSeq(), pkgAdjust);
		}
		
		for (Entry<Integer, List<PkgAdjust>> entry : hasOnlyPkgMap.entrySet()) {
			Integer key = entry.getKey();
			processNoOnlyIdPkg(hasOnlyPkgMap.get(key), pkgMap, productMap, inWight, outweight, priceMap, pkgNames);
		}

	}

	private void processNoOnlyIdPkg(List<PkgAdjust> noOnlyIdPkgs, Map<String, List<PkgInfo>> pkgMap, Map<Integer, List<PkgProduct>> productMap, String inWight, String outweight, Map<String, BigDecimal> priceMap, List<String> pkgNames) {
		for (PkgAdjust newPkgAdjust : noOnlyIdPkgs) {
			pkgNames.add(newPkgAdjust.getPkgname());
		}

		Set<String> bidCountMap = FieldUtils.getOnlyBidSet(pkgNames);
		for (PkgAdjust newPkgAdjust : noOnlyIdPkgs) {
			String pkgName = newPkgAdjust.getPkgname();
			List<PkgInfo> pkgList = pkgMap.get(pkgName);
			BigDecimal rate = new BigDecimal(newPkgAdjust.getRate()).divide(new BigDecimal(100));
			BigDecimal rate2 = new BigDecimal(newPkgAdjust.getRate2()).divide(new BigDecimal(100));

			BigDecimal targetPrice = new BigDecimal(newPkgAdjust.getTargetprice()); // 目标价
			BigDecimal allRealPrice = CalcUtil.getBigDecimal();
			BigDecimal ror = new BigDecimal(newPkgAdjust.getRor());// 微调系数
			BigDecimal ror2 = new BigDecimal(newPkgAdjust.getRor2());// 微调系数
			BigDecimal allPrice = CalcUtil.getBigDecimal();
			// 初始分配价格，计算多出的部分
			for (PkgInfo pkgInfo : pkgList) {
				// 已经确定的价格
				if (priceMap.containsKey(pkgInfo.getBidNo())) {
					pkgInfo.setTargetPrice(String.valueOf(priceMap.get(pkgInfo.getBidNo()).multiply(new BigDecimal(pkgInfo.getCount()))));
				} else {
					// 未确认的价格
					if (StringUtil.isEmpty(pkgInfo.getTotalLimitPrice()) || pkgInfo.getTotalLimitPrice().equals("0")) {
						pkgInfo.setTargetPrice(String.valueOf(new BigDecimal(pkgInfo.getRealPrice()).multiply(getPkgRor(pkgInfo, ror, ror2))));
					} else {
						pkgInfo.setTargetPrice(String.valueOf(new BigDecimal(pkgInfo.getTotalLimitPrice()).multiply(getPkgRate(pkgInfo, rate, rate2))));
					}
				}
				// 独有ID
				if (!priceMap.containsKey(pkgInfo.getBidNo())) { // 未确定价格的，独有ID
					if (bidCountMap.contains(pkgInfo.getBidNo())) {
						if (StringUtil.isEmpty(pkgInfo.getTotalLimitPrice()) || pkgInfo.getTotalLimitPrice().equals("0")) {
							allRealPrice = allRealPrice.add(new BigDecimal(pkgInfo.getTargetPrice()));
						}
					}
				}
				allPrice = allPrice.add(new BigDecimal(pkgInfo.getTargetPrice()));
			}
			BigDecimal morePrice = targetPrice.subtract(allPrice);
			BigDecimal tag = CalcUtil.getBigDecimal();
			if (CalcUtil.isEqual(allRealPrice, new BigDecimal(0))) {
				String msg = pkgName + "不存在独有ID或独有ID均存在限价，按比例分配其他id！";
				ConsoleManager.getInstance().append(msg);
//				warnings.add(msg);
//				break;
				
				tag = CalcUtil.divide(String.valueOf(morePrice), String.valueOf(allPrice));
				// 调整多余的价格, 不存在独有ID所有按比例分配
				for (PkgInfo pkgInfo : pkgList) {
					String target = CalcUtil.getBigDecimalStringDown(new BigDecimal(pkgInfo.getTargetPrice()).multiply(tag).add(new BigDecimal(pkgInfo.getTargetPrice())));
					pkgInfo.setTargetPrice(target);
				}
				
			} else {
				tag = CalcUtil.divide(String.valueOf(morePrice), String.valueOf(allRealPrice));
				// 调整多余的价格
				for (PkgInfo pkgInfo : pkgList) {
					if (!priceMap.containsKey(pkgInfo.getBidNo())) { // 未确定价格的，独有ID
						if (bidCountMap.contains(pkgInfo.getBidNo())) {
							if (StringUtil.isEmpty(pkgInfo.getTotalLimitPrice()) || pkgInfo.getTotalLimitPrice().equals("0")) {
								String target = CalcUtil.getBigDecimalStringDown(new BigDecimal(pkgInfo.getTargetPrice()).multiply(tag).add(new BigDecimal(pkgInfo.getTargetPrice())));
								pkgInfo.setTargetPrice(target);
							}
						}
					}
				}
			}
	
			// 存储已调整的价格，并调价
			for (PkgInfo pkgInfo : pkgList) {
				adjustPrices(pkgInfo, productMap.get(pkgInfo.getId()), inWight, outweight);
			}
			
			String orgRate = perference.getInfo(EnvirmentSettingDialog.class.getName() + ".originrate");
			if (StringUtils.isEmpty(orgRate)) {
				orgRate = "1.0";
			}
			
			// 计算占比
			BigDecimal tagTemp = tag.add(new BigDecimal(1));
			BigDecimal tag1 = CalcUtil.getBigDecimal(tagTemp.multiply(new BigDecimal(orgRate)).multiply(ror));
			BigDecimal tag2 = CalcUtil.getBigDecimal(tagTemp.multiply(new BigDecimal(orgRate)).multiply(ror2));

			ConsoleManager.getInstance().append(">>>>>>>>>>>>>>>>" + pkgName + " 110KV：" + tag1 + "，220KV：" + tag2);
		}
		
		for (PkgAdjust newPkgAdjust : noOnlyIdPkgs) {
			List<PkgInfo> pkgList = pkgMap.get(newPkgAdjust.getPkgname());
			for (PkgInfo pkgInfo : pkgList) {
				priceMap.put(pkgInfo.getBidNo(), CalcUtil.divide(pkgInfo.getTargetPrice(), pkgInfo.getCount()));
			}
		}
	}

	private BigDecimal getPkgRate(PkgInfo pkgInfo, BigDecimal rate, BigDecimal rate2) {
		String desc = pkgInfo.getProductDesc();
		if (desc.contains("220") || desc.contains("330") || desc.contains("500") || desc.contains("750")) {
			return rate2;
		}
		return rate;
	}

	private BigDecimal getPkgRor(PkgInfo pkgInfo, BigDecimal ror, BigDecimal ror2) {
		String desc = pkgInfo.getProductDesc();
		if (desc.contains("220") || desc.contains("330") || desc.contains("500") || desc.contains("750")) {
			return ror2;
		}
		return ror;
	}

	// 查询货物清单信息
	private Map<String, List<PkgInfo>> getPkgMap() {
		Map<String, List<PkgInfo>> pkgMaps = new HashMap<>();
		List<PkgInfo> pkgs = PkgInfoDao.getPkgsWithLimitPrice("", "", "");
		List<PkgInfo> plist = null;
		for (PkgInfo pkgInfo : pkgs) {
			plist = new ArrayList<PkgInfo>();
			if (pkgMaps.containsKey(pkgInfo.getName())) {
				plist = pkgMaps.get(pkgInfo.getName());
				if (plist == null) {
					plist = new ArrayList<PkgInfo>();
				}
			}
			plist.add(pkgInfo);
			pkgMaps.put(pkgInfo.getName(), plist);
		}
		return pkgMaps;
	}

	// 获取货物里子项的信息
	private Map<Integer, List<PkgProduct>> getProductMap() {
		Map<Integer, List<PkgProduct>> pkgProductMaps = new HashMap<>();
		List<PkgProduct> pkgLists = PkgProductDao.getPkgProductByParentid(null);
		List<PkgProduct> plist = null;
		for (PkgProduct pkgProduct : pkgLists) {
			plist = new ArrayList<PkgProduct>();
			if (pkgProductMaps.containsKey(pkgProduct.getParentid())) {
				plist = pkgProductMaps.get(pkgProduct.getParentid());
				if (plist == null) {
					plist = new ArrayList<PkgProduct>();
				}
			}
			plist.add(pkgProduct);
			pkgProductMaps.put(pkgProduct.getParentid(), plist);
		}
		return pkgProductMaps;
	}

	/**
	 * 调价设置
	 * 
	 * @param pkg
	 * @param products
	 * @param inWight
	 * @param outweight
	 */
	public void adjustPrices(PkgInfo pkg, List<PkgProduct> products, String inWight, String outweight) {

		List<PkgProduct> tempPkgProduct = new ArrayList<>();
		BigDecimal targetPrice = new BigDecimal(pkg.getTargetPrice());// 目标价格
		BigDecimal totalTaxPkg = CalcUtil.getBigDecimal();// 带税成本
		BigDecimal weightTaxPkg = CalcUtil.getBigDecimal();// 权重成本
		BigDecimal allPrice = CalcUtil.getBigDecimal(); // 对外报价
		BigDecimal showPrice = CalcUtil.getBigDecimal()

		; // 显示的价格
		BigDecimal limitPrice = null;
		if (!StringUtil.isEmpty(pkg.getTotalLimitPrice())) {
			limitPrice = new BigDecimal(pkg.getTotalLimitPrice());// 目标价格
		}

		// 内购权重 外购权重
		if (products == null || products.size() == 0) {
			String msg = pkg.getName() + "_" + pkg.getBidNo() + "缺少技术应答信息";
			ConsoleManager.getInstance().append(msg);
			warnings.add(msg);
			return;
		}
		for (PkgProduct product : products) {
			String bidName = product.getName();
			String count = product.getOcunnt();
			if (StringUtil.isEmpty(bidName)) {
				continue;
			}
			if (StringUtil.isEmpty(count)) {
				continue;
			}
			if (!product.getQuote().equals("是")) {
				continue;
			}
			if (!NumberUtils.isNumber(count)) {
				continue;
			}
			if (count.equals("0")) {
				continue;
			}
			if (StringUtil.isEmpty(product.getCostprice())) {
				continue;
			}
			tempPkgProduct.add(product);
			BigDecimal price = new BigDecimal(product.getCostprice());
			BigDecimal cost = price.multiply(new BigDecimal(count));

			BigDecimal weightcost = null;
			if (product.getLnType() == 1) {
				product.setWeight(inWight);
				weightcost = price.multiply(new BigDecimal(count).multiply(new BigDecimal(inWight)));
			} else {
				product.setWeight(outweight);
				weightcost = price.multiply(new BigDecimal(count).multiply(new BigDecimal(outweight)));
			}
			totalTaxPkg = totalTaxPkg.add(cost);
			weightTaxPkg = weightTaxPkg.add(weightcost);
		}
		if (!StringUtil.isEmpty(pkg.getTotalLimitPrice())) {
			if (CalcUtil.isBigThan(totalTaxPkg, limitPrice)) {
				// String msg = pkg.getName() + "_" + pkg.getProjectName() + "_"
				// + pkg.getProduct() + "_" + pkg.getBidNo() + "成本" +
				// totalTaxPkg + "超出限价 " + limitPrice + "！";
				// ConsoleManager.getInstance().append(msg);
				// warnings.add(msg);
			}
		}

		for (PkgProduct product : products) {
			product.setPrice("");
			product.setTotalprice("");

			String bidName = product.getName();
			String count = product.getOcunnt();

			if (StringUtil.isEmpty(bidName)) {
				continue;
			}
			if (StringUtil.isEmpty(count)) {
				continue;
			}
			if (!product.getQuote().equals("是")) {
				continue;
			}
			if (!NumberUtils.isNumber(count)) {
				continue;
			}
			if (count.equals("0")) {
				continue;
			}
			if (StringUtil.isEmpty(product.getCostprice())) {
				continue;
			}

			BigDecimal costprice = new BigDecimal(product.getCostprice());

			BigDecimal weightcost = null;
			if (product.getLnType() == 1) {
				weightcost = costprice.multiply(new BigDecimal(count).multiply(new BigDecimal(inWight)));
			} else {
				weightcost = costprice.multiply(new BigDecimal(count).multiply(new BigDecimal(outweight)));
			}

			// 单价，8位小数
			BigDecimal price = CalcUtil.getBigDecimal2(weightcost.divide(weightTaxPkg, ToolConstants.pPrice, BigDecimal.ROUND_DOWN).multiply(targetPrice).divide(new BigDecimal(count), ToolConstants.pPrice, BigDecimal.ROUND_DOWN));
			BigDecimal totalPrice = CalcUtil.getBigDecimal2(price.multiply(new BigDecimal(count)));

			// 截取六位
			product.setPrice(CalcUtil.getBigDecimalString(price));
			BigDecimal showTotalPrice = new BigDecimal(CalcUtil.getBigDecimalString(price)).multiply(new BigDecimal(count));
			product.setTotalprice(CalcUtil.getBigDecimalString(showTotalPrice));
			showPrice = showPrice.add(new BigDecimal(CalcUtil.getBigDecimalString(showTotalPrice)));

			// 计算的价格,8位
			allPrice = allPrice.add(totalPrice);
		}
		// 单价
		BigDecimal price = CalcUtil.getAverageValue(allPrice, Integer.valueOf(pkg.getCount()));

		pkg.setTotalTax(CalcUtil.getBigDecimalString(totalTaxPkg));
		// pkg.setTotalLimitPrice(CalcUtil.getBigDecimalString(new
		// BigDecimal(pkg.getTotalLimitPrice()))); // 总限价
		pkg.setTargetPrice(CalcUtil.getBigDecimalString(targetPrice));

		String withoutTaxtPrice = CalcUtil.getBigDecimalString(CalcUtil.divide(String.valueOf(price), String.valueOf(CalcUtil.getTaxRate())));
		String withoutTotalPrice = CalcUtil.getBigDecimalString(new BigDecimal(withoutTaxtPrice).multiply(new BigDecimal(pkg.getCount())));

		pkg.setWithoutTaxPrice(withoutTaxtPrice);
		pkg.setWithoutTotalPrice(withoutTotalPrice);

		// 乘以税率返回正确值

		// 1.当尾数小于或等于4时，直接将尾数舍去
		// 2.当尾数大于或等于6时将尾数舍去向前一位进位
		// 3.当尾数为5，而尾数后面的数字均为0时，应看尾数“5”的前一位：若前一位数字此时为奇数，就应向前进一位；若前一位数字此时为偶数，则应将尾数舍去。数字“0”在此时应被视为偶数。
		// 4.当尾数为5，而尾数“5”的后面还有任何不是0的数字时，无论前一位在此时为奇数还是偶数，也无论“5”后面不为0的数字在哪一位上，都应向前进一位。
		String withprice = CalcUtil.getBigDecimalStringHalfEven(new BigDecimal(withoutTaxtPrice).multiply(CalcUtil.getTaxRate()));
		String withtotalprice = CalcUtil.getBigDecimalStringHalfEven(new BigDecimal(withoutTaxtPrice).multiply(new BigDecimal(pkg.getCount())).multiply(CalcUtil.getTaxRate()));

		pkg.setTotalPrice(withtotalprice);
		pkg.setPrice(withprice);

		// 调正价格，把多余的部分分出去

		BigDecimal appendPrice = new BigDecimal(withtotalprice).subtract(showPrice);
		// 找到数量为1的进行添加
		for (PkgProduct product : products) {
			String bidName = product.getName();
			String count = product.getOcunnt();
			if (StringUtil.isEmpty(bidName)) {
				continue;
			}
			if (StringUtil.isEmpty(count)) {
				continue;
			}
			if (!product.getQuote().equals("是")) {
				continue;
			}
			if (StringUtil.isEmpty(count)) {
				continue;
			}
			if (!NumberUtils.isNumber(count)) {
				continue;
			}
			if (count.equals("0")) {
				continue;
			}
			if (StringUtil.isEmpty(product.getCostprice())) {
				continue;
			}
			if (count.equals("1")) {
				BigDecimal finalprice = new BigDecimal(product.getPrice());
				BigDecimal finaltotalprice = new BigDecimal(product.getTotalprice());
				finalprice = finalprice.add(appendPrice);
				finaltotalprice = finaltotalprice.add(appendPrice);
				product.setPrice(CalcUtil.getBigDecimalString(finalprice));
				product.setTotalprice(CalcUtil.getBigDecimalString(finaltotalprice));
				break;
			}
			if (CalcUtil.isBigThan(BigDecimal.valueOf(0.000001), new BigDecimal(product.getPrice()))) {
				String msg = pkg.getName() + "_" + pkg.getProjectName() + "_" + pkg.getProduct() + "_" + pkg.getBidNo() + "存在负值" + "！";
				ConsoleManager.getInstance().append(msg);
				warnings.add(msg);
			}
		}
		if (!StringUtil.isEmpty(pkg.getTotalLimitPrice())) {
			if (CalcUtil.isBigThan(allPrice, limitPrice)) {
				String msg = pkg.getName() + "_" + pkg.getProjectName() + "_" + pkg.getProduct() + "_" + pkg.getBidNo() + "报价" + allPrice + "超出限价 " + limitPrice + "！";
				ConsoleManager.getInstance().append(msg);
				warnings.add(msg);
			}
		}

		allPkgList.add(pkg);
		allProducts.addAll(tempPkgProduct);
	}

	/**
	 * 更新计算的结果
	 */
	public void updateCalResult() {
		PkgInfoDao.updatePkgInfo(allPkgList);
		PkgProductDao.updatePkgProductAnalysisPrice(allProducts);
	}

	/**
	 * 更新结果
	 * 
	 * @param adjsutList
	 */
	public void updateResult(List<PkgAdjust> adjsutList) {
		PkgAdjustDao.updateResultPricePkgInfo(adjsutList);
	}

	public List<String> getWarnings() {
		return warnings;
	}

	public void calOnlyRate(List<PkgAdjust> adjsutList) {
		List<PkgAdjust> selectList = new ArrayList<>();
		
		
		List<String> pkgNames = new ArrayList<>();
		for (PkgAdjust newPkgAdjust : adjsutList) {
			pkgNames.add(newPkgAdjust.getPkgname());
			selectList.add(newPkgAdjust);
		}
		Set<String> bidCountMap = FieldUtils.getOnlyBidSet(pkgNames);
		Map<String, List<PkgInfo>> pkgMap = getPkgMap();

		for (PkgAdjust newPkgAdjust : adjsutList) {
			String pkgName = newPkgAdjust.getPkgname();
			List<PkgInfo> pkgList = pkgMap.get(pkgName);
			BigDecimal ror = new BigDecimal(newPkgAdjust.getRor());// 微调系数
			BigDecimal ror2 = new BigDecimal(newPkgAdjust.getRor2());// 微调系数
			BigDecimal allRealPrice = CalcUtil.getBigDecimal();
			BigDecimal targetPrice = new BigDecimal(newPkgAdjust.getTargetprice());
			for (PkgInfo pkgInfo : pkgList) {
				// 独有ID
				if (bidCountMap.contains(pkgInfo.getBidNo())) {
					if (StringUtil.isEmpty(pkgInfo.getTotalLimitPrice()) || pkgInfo.getTotalLimitPrice().equals("0")) {
						String pkgTargetPrice = String.valueOf(new BigDecimal(pkgInfo.getRealPrice()).multiply(getPkgRor(pkgInfo, ror, ror2)));
						allRealPrice = allRealPrice.add(new BigDecimal(pkgTargetPrice));
					}
				}
			}
			String result = "0";
			if (!CalcUtil.isEqual(allRealPrice, new BigDecimal(0))) {
				result = String.valueOf(CalcUtil.divide(String.valueOf(allRealPrice), String.valueOf(targetPrice)));
			}
			newPkgAdjust.setResult(result);
		}

		// 按照优先级调价, 非独有ID
		Collections.sort(selectList, new Comparator<PkgAdjust>() {
			@Override
			public int compare(PkgAdjust o1, PkgAdjust o2) {
				return Double.parseDouble(o1.getResult()) - Double.parseDouble(o2.getResult()) > 0 ? 1 : -1;
			}
		});

		int seq = 0;
		for (PkgAdjust newPkgAdjust : selectList) {
			if (!newPkgAdjust.getResult().equals("0")) {
				newPkgAdjust.setSeq(seq);
				seq++;
			}
		}
	}

}