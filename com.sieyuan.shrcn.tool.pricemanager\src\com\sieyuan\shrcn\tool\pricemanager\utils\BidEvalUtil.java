package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.util.Arrays;
import java.util.Comparator;

import com.shrcn.found.common.log.SCTLogger;
import com.sieyuan.shrcn.tool.pricemanager.model.BidEvaluationResult;

public class BidEvalUtil {
	
    /**
     * 将字符串转换为浮点数，除以100，并保留两位小数。
     *
     * @param numberStr 要转换的字符串
     * @return 处理后的浮点数，保留两位小数
     */
    public static double parseAndScale(double number) {
        try {
            double scaledNumber = number / 100;
            return Math.round(scaledNumber * 100.0) / 100.0;
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

	public static void main(String[] args) {
		BidEvaluationResult[] bids = new BidEvaluationResult[] { 
				new BidEvaluationResult(595.65, "北京四方继保工程技术有限公司"), 
				new BidEvaluationResult(1163.48, "长园深瑞继保自动化有限公司"), 
				new BidEvaluationResult(636.55, "国电南京自动化股份有限公司"), 
				new BidEvaluationResult(635.52, "国电南瑞南京控制系统有限公司"),
				new BidEvaluationResult(604.47, "南京南瑞继保工程技术有限公司"), 
				new BidEvaluationResult(648.42, "许继电气股份有限公司"), 
				new BidEvaluationResult(672.23, "上海思源弘瑞自动化有限公司") };

		double a = 0.0; // 下浮比例
		double m = 0.2; // 当P < B时的指数
		double n = 0.25; // 当P >= B时的指数
		double techWeight = 0.5; // 技术分占比
		double priceWeight = 0.4; // 价格分在总分中的占比

		// 计算最终报价A
		
	    // 提取价格到double数组中
        double[] prices = extractPrices(bids);
		double finalBid = calculateFinalBid(prices);
		finalBid = Math.round(finalBid * 100.0) / 100.0; // 保留两位小数

		// 计算基准价B
		double basePrice = finalBid * (1 - a);
		basePrice = Math.round(basePrice * 100.0) / 100.0; // 保留两位小数

		// 计算每个报价的得分
		for (BidEvaluationResult bid : bids) {
			bid.setScore(calculateScore(bid.getBid(), basePrice, n, m));
		}

		// 获取价格得分排名、价格分差、总分差和技术补分
		BidEvaluationResult[] rankings = getRankings(bids, priceWeight, techWeight);

		// 输出结果
		SCTLogger.debug("最终报价A: " + String.format("%.2f", finalBid));
		SCTLogger.debug("基准价B: " + String.format("%.2f", basePrice));
		SCTLogger.debug("价格得分排名及价格分差、总分差、技术补分：");
		for (BidEvaluationResult ranking : rankings) {
			SCTLogger.debug("厂家 " + ranking.getCompanyName() + " 报价 " + String.format("%.2f", ranking.getBid()) + " 的得分: " + String.format("%.2f", ranking.getScore()) + " 排名: " + ranking.getRank() + " 价格分差: " + String.format("%.2f", ranking.getPriceDiff()) + " 总分差: "
					+ String.format("%.2f", ranking.getTotalDiff()) + " 技术补分: " + String.format("%.2f", ranking.getTechBonus()));
		}
	}
	
    /**
     * 提取Bid对象数组中的价格到double数组
     *
     * @param bids Bid对象数组
     * @return 价格数组
     */
    public static double[] extractPrices(BidEvaluationResult[] bids) {
        double[] prices = new double[bids.length];
        for (int i = 0; i < bids.length; i++) {
            prices[i] = bids[i].getBid();
        }
        return prices;
    }

    /**
     * 计算最终报价A
     *
     * @param bids 投标报价数组
     * @return 最终报价A
     */
    public static double calculateFinalBid(double[] bids) {
        Arrays.sort(bids); // 对报价数组进行排序

        while (bids.length > 1) {
        	SCTLogger.debug(Arrays.toString(bids));
            double minDiff = Double.MAX_VALUE;
            int minDiffIndex = 0;

            // 找到相邻报价对中差距最小的一对
            for (int i = 0; i < bids.length - 1; i++) {
                 double diff = bids[i + 1] - bids[i];
                 if (diff <= minDiff) {
                     minDiff = diff;
                     minDiffIndex = i;
                 }
             }

            // 用这对报价的平均值替换它们
            double avg = (bids[minDiffIndex] + bids[minDiffIndex + 1]) / 2;
            double[] newBids = new double[bids.length - 1];
            System.arraycopy(bids, 0, newBids, 0, minDiffIndex);
            newBids[minDiffIndex] = avg;
            System.arraycopy(bids, minDiffIndex + 2, newBids, minDiffIndex + 1, bids.length - minDiffIndex - 2);
            bids = newBids;
            Arrays.sort(bids); // 再次排序
        }
        SCTLogger.debug(Arrays.toString(bids));
        return bids[0];
    }

	/**
	 * 计算每个报价的得分
	 * 
	 * @param value
	 *            投标报价
	 * @param basePrice
	 *            基准价B
	 * @param n
	 *            当P >= B时的指数
	 * @param m
	 *            当P < B时的指数
	 * @return 每个报价的得分
	 */
	public static double calculateScore(double value, double basePrice, double n, double m) {
		double score = 0;
		if (value >= basePrice) {
			score = Math.pow(basePrice / value, n) * 100;
		} else {
			score = Math.pow(value / basePrice, m) * 100;
		}
		return score;
	}

	/**
	 * 获取价格得分排名、价格分差、总分差和技术补分
	 * 
	 * @param bids
	 *            投标报价数组
	 * @param priceWeight
	 *            价格分在总分中的占比
	 * @param techWeight
	 *            技术分占比
	 * @return 价格得分排名、价格分差、总分差和技术补分数组
	 */
	public static BidEvaluationResult[] getRankings(BidEvaluationResult[] bids, double priceWeight, double techWeight) {
		BidEvaluationResult[] rankings = new BidEvaluationResult[bids.length];
		for (int i = 0; i < bids.length; i++) {
			rankings[i] = new BidEvaluationResult(bids[i].getBid(), bids[i].getScore(), bids[i].getCompanyName());
		}

		Arrays.sort(rankings, new Comparator<BidEvaluationResult>() {
			@Override
			public int compare(BidEvaluationResult o1, BidEvaluationResult o2) {
				return Double.compare(o2.getScore(), o1.getScore());
			}
		});

		double maxScore = rankings[0].getScore();
		for (int i = 0; i < rankings.length; i++) {
			rankings[i].setRank(i + 1);
			rankings[i].setPriceDiff( rankings[i].getScore()  - maxScore); // 修正价格分差计算，确保为负数
			rankings[i].setTotalDiff(rankings[i].getPriceDiff() * priceWeight);
			rankings[i].setTechBonus(rankings[i].getTotalDiff() / techWeight);
			rankings[i].setScore(Math.round(rankings[i].getScore() * 100.0) / 100.0); // 保留两位小数
			rankings[i].setPriceDiff(Math.round(rankings[i].getPriceDiff() * 100.0) / 100.0); // 保留两位小数
			rankings[i].setTotalDiff(Math.round(rankings[i].getTotalDiff() * 100.0) / 100.0); // 保留两位小数
			rankings[i].setTechBonus(Math.round(rankings[i].getTechBonus() * 100.0) / 100.0); // 保留两位小数
		}
		return rankings;
	}
}