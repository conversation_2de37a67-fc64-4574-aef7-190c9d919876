package com.sieyuan.shrcn.tool.pricemanager.utils;

import java.math.BigDecimal;

import org.junit.Test;

public class CalcUtilTest {

	@Test
	public void test() {
		// 1.当尾数小于或等于4时，直接将尾数舍去
		System.out.println("10.2731: " + new BigDecimal("10.2731").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 10.2731:
		System.out.println("18.5049: " + new BigDecimal("18.5049").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 18.5049:
		System.out.println("16.4005: " + new BigDecimal("16.4005").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 16.4005:
		System.out.println("27.1829: " + new BigDecimal("27.1829").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 27.1829:
																												// 27.18

		// 2.当尾数大于或等于6时将尾数舍去向前一位进位
		System.out.println("16.7777: " + new BigDecimal("16.7777").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 16.7777:
		System.out.println("10.29501: " + new BigDecimal("10.29501").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 10.29501:
		System.out.println("21.0191: " + new BigDecimal("21.0191").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 21.0191:
																												// 21.02

		// 3.当尾数为5，而尾数后面的数字均为0时，应看尾数“5”的前一位：若前一位数字此时为奇数，就应向前进一位；若前一位数字此时为偶数，则应将尾数舍去。数字“0”在此时应被视为偶数。
		System.out.println("12.6450: " + new BigDecimal("12.6450").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 12.6450:
		System.out.println("18.2750: " + new BigDecimal("18.2750").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 18.2750:
		System.out.println("12.7350: " + new BigDecimal("12.7350").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 12.7350:
		System.out.println("21.845000: " + new BigDecimal("21.845000").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 21.845000:
																													// 21.84

		// 4.当尾数为5，而尾数“5”的后面还有任何不是0的数字时，无论前一位在此时为奇数还是偶数，也无论“5”后面不为0的数字在哪一位上，都应向前进一位。
		System.out.println("12.73507: " + new BigDecimal("12.73507").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 12.73507:
		System.out.println("21.84502: " + new BigDecimal("21.84502").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 21.84502:
		System.out.println("12.64501: " + new BigDecimal("12.64501").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 12.64501:
		System.out.println("18.27509: " + new BigDecimal("18.27509").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 18.27509:
		System.out.println("38.305000001: " + new BigDecimal("38.305000001").setScale(2, BigDecimal.ROUND_HALF_EVEN)); // 38.305000001:
																														// 38.31
	}

}
