package com.sieyuan.shrcn.tool.pricemanager.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.sieyuan.shrcn.tool.pricemanager.data.GlobalData;
import com.sieyuan.shrcn.tool.pricemanager.model.PriceRate;

/**
 * 辅参数报价dao
 *
 * <AUTHOR>
 * @version 1.0, 2020-12-4
 *
 */
public class PriceRateDao {

	// 查询所有
	public static List<PriceRate> getPriceRates() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PriceRate> sList = new ArrayList<>();
        try {
			sList = sqliteHelper.executeQuery("select * from pricerate", new RowMapper<PriceRate>() {
				@Override
				public PriceRate mapRow(ResultSet rs, int index) throws SQLException {
					PriceRate priceRate = setPriceRate(rs);
					return priceRate;
				}
			});
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }
	
	// 查询所有按照排序
	public static List<PriceRate> getPriceRatesOrderBy() {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<PriceRate> sList = new ArrayList<>();
        try {
			sList = sqliteHelper.executeQuery("select * from pricerate order by pid, funit desc", new RowMapper<PriceRate>() {
				@Override
				public PriceRate mapRow(ResultSet rs, int index) throws SQLException {
					PriceRate priceRate = setPriceRate(rs);
					return priceRate;
				}
			});
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return sList;
    }
	
	private static PriceRate setPriceRate(ResultSet rs) throws SQLException {
		PriceRate priceRate = new PriceRate();
		priceRate.setId(rs.getInt("id"));
		priceRate.setPkgId(rs.getString("pkgId"));
		priceRate.setPkgName(rs.getString("pkgName"));
		priceRate.setRound(rs.getString("round"));
		priceRate.setAttachment(rs.getString("attachment"));
		priceRate.setCode(rs.getString("code"));
		priceRate.setProductName(rs.getString("productName"));
		priceRate.setPid(rs.getString("pid"));
		priceRate.setOrderid(rs.getString("orderid"));
		priceRate.setCompany(rs.getString("company"));
		priceRate.setUnit(rs.getString("unit"));
		priceRate.setExtendedDesc(rs.getString("extendedDesc"));
		priceRate.setLimitPrice(rs.getString("limitPrice"));
		priceRate.setCount(rs.getString("count"));
		priceRate.setFid(rs.getString("fid"));
		priceRate.setFunit(rs.getString("funit"));
		priceRate.setRate(rs.getString("rate"));
		priceRate.setfLimitPrice(rs.getString("fLimitPrice"));
		priceRate.setFprice(rs.getString("fprice"));
		priceRate.setBprice(rs.getString("bprice"));
		priceRate.setTaxrate(rs.getString("taxrate"));
		priceRate.setfWithPrice(rs.getString("fWithPrice"));
		priceRate.setbWithPrice(rs.getString("bWithPrice"));
		priceRate.setbTotalPrice(rs.getString("bTotalPrice"));
		priceRate.setbTotalWithPrice(rs.getString("bTotalWithPrice"));
		priceRate.setFloatRate(rs.getString("floatRate"));
		
		priceRate.setValid(rs.getString("valid"));
		priceRate.setAvg(rs.getString("avg"));
		priceRate.setPvalid(rs.getString("pvalid"));
		priceRate.setPavg(rs.getString("pavg"));
		return priceRate;
	}
	//
    public static void savePriceRates(List<PriceRate> priceRates) {

        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();

		for (PriceRate priceRate : priceRates) {

			String pkgId = priceRate.getPkgId();
			String pkgName = priceRate.getPkgName();
			String round = priceRate.getRound();
			String attachment = priceRate.getAttachment();
			String code = priceRate.getCode();
			String productName = priceRate.getProductName();
			String pid = priceRate.getPid();
			String orderid = priceRate.getOrderid();
			String company = priceRate.getCompany();
			String unit = priceRate.getUnit();

			String extendedDesc = priceRate.getExtendedDesc();
			String limitPrice = priceRate.getLimitPrice();
			String rowLimitPrice = priceRate.getRowLimitPrice();
			String count = priceRate.getCount();
			String fid = priceRate.getFid();
			String funit = priceRate.getFunit();
			String rate = priceRate.getRate();
			String fLimitPrice = priceRate.getfLimitPrice();
			String fprice = priceRate.getFprice();
			String bprice = priceRate.getBprice();

			String taxrate = priceRate.getTaxrate();
			String fWithPrice = priceRate.getfWithPrice();
			String bWithPrice = priceRate.getbWithPrice();
			String bTotalPrice = priceRate.getbTotalPrice();
			String bTotalWithPrice = priceRate.getbTotalWithPrice();
			String floatRate = priceRate.getFloatRate();
			
			String valid = priceRate.getValid();
			String pvalid = priceRate.getPvalid();
			String avg = priceRate.getAvg();
			String pavg = priceRate.getPavg();

			String sql = "INSERT INTO pricerate (pkgId, pkgName, round, attachment, code, productName, pid, orderid, company, unit, extendedDesc, limitPrice"
					+ ",rowLimitPrice ,count ,fid ,funit ,rate ,fLimitPrice ,fprice  ,bprice  ,taxrate  ,fWithPrice  ,bWithPrice  ,bTotalPrice,bTotalWithPrice, floatRate, valid, avg, pvalid, pavg) " + "VALUES ('"
					+ pkgId
					+ "','"
					+ pkgName
					+ "','"
					+ round
					+ "','"
					+ attachment
					+ "','"
					+ code
					+ "','"
					+ productName
					+ "','"
					+ pid
					+ "','"
					+ orderid
					+ "','"
					+ company
					+ "','"
					+ unit
					+ "','"
					+ extendedDesc
					+ "','"
					+ limitPrice
					+ "','"
					+ rowLimitPrice
					+ "','"
					+ count
					+ "','"
					+ fid
					+ "','"
					+ funit
					+ "','" + rate + "','" + fLimitPrice + "','" + fprice + "','" + bprice + "','" + taxrate + "','" + fWithPrice + "','" + bWithPrice + "','" + bTotalPrice + "','" + bTotalWithPrice 
					+ "','" + floatRate + "','" + valid + "','" + avg + "','" + pvalid + "','" + pavg + "')";
			sqls.add(sql);
		}
        try {
            String sql1 = "delete from pricerate;";
            String sql2 = "update sqlite_sequence SET seq = 0 where name ='pricerate';";
            sqliteHelper.executeUpdate(sql1);
            sqliteHelper.executeUpdate(sql2);

            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
    
    public static void updatePriceRates(List<PriceRate> priceRateList) {
        SqliteHelper sqliteHelper = GlobalData.getInstance().getSqliteHelper();
        List<String> sqls = new ArrayList<String>();
		for (PriceRate priceRate : priceRateList) {
			String fPrice = priceRate.getFprice();
			String taxrate = priceRate.getTaxrate();
			String floatRate = priceRate.getFloatRate();
			String valid = priceRate.getValid();
			String avg = priceRate.getAvg();
			String pvalid = priceRate.getPvalid();
			String pavg = priceRate.getPavg();
			Integer id = priceRate.getId();
			String sql = "update pricerate set fprice='" + fPrice + "', taxrate='" + taxrate 
					+ "', valid='" + valid + "', avg='" + avg + "', pvalid='" + pvalid + "', pavg='" + pavg + "', floatRate='" + floatRate + "' where id=" + id;
			sqls.add(sql);
		}
        try {
            sqliteHelper.executeUpdate(sqls);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }
    }
}
